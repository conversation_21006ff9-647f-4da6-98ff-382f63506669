"""
统一异常处理模块
"""

from typing import Dict, Any, Optional


class ASRError(Exception):
    """ASR系统基础异常类"""
    
    def __init__(self, code: int, message: str, details: Dict[str, Any] = None, cause: Exception = None):
        self.code = code
        self.message = message
        self.details = details or {}
        self.cause = cause
        super().__init__(f"[{code}] {message}")


class HandshakeError(ASRError):
    """握手阶段异常"""
    pass


class AudioProcessingError(ASRError):
    """音频处理异常"""
    pass


class SessionError(ASRError):
    """会话管理异常"""
    pass


class SystemError(ASRError):
    """系统异常"""
    pass


class LIDError(ASRError):
    """语种识别异常"""
    pass


class VADError(ASRError):
    """语音活动检测异常"""
    pass


# 错误码常量
class ErrorCodes:
    """错误码定义"""
    
    # 握手阶段错误 (1000-1999)
    INVALID_HANDSHAKE_FORMAT = 1001
    MISSING_HANDSHAKE_PARAMS = 1002
    UNSUPPORTED_LANGUAGE = 1003
    INVALID_AUDIO_CONFIG = 1004
    SERVER_CAPACITY_EXCEEDED = 1005
    
    # 数据传输阶段错误 (2000-2999)
    INVALID_AUDIO_FORMAT = 2001
    AUDIO_SIZE_MISMATCH = 2002
    SEQUENCE_ID_ERROR = 2003
    AUDIO_DECODING_FAILED = 2004
    SAMPLE_RATE_MISMATCH = 2005
    PROCESSING_TIMEOUT = 2006
    
    # 会话管理错误 (3000-3999)
    SESSION_NOT_FOUND = 3001
    SESSION_EXPIRED = 3002
    INVALID_SESSION_STATE = 3003
    SESSION_LIMIT_EXCEEDED = 3004
    
    # 系统错误 (4000-4999)
    INTERNAL_SERVER_ERROR = 4001
    SERVICE_UNAVAILABLE = 4002
    MODEL_LOADING_FAILED = 4003
    RESOURCE_EXHAUSTED = 4004
    
    # 协议错误 (5000-5999)
    PROTOCOL_VIOLATION = 5001
    UNSUPPORTED_MESSAGE = 5002
    MESSAGE_FORMAT_ERROR = 5003
    CONNECTION_TIMEOUT = 5004


# 错误码到消息的映射
ERROR_MESSAGES = {
    # 握手阶段错误
    ErrorCodes.INVALID_HANDSHAKE_FORMAT: "Invalid handshake request format",
    ErrorCodes.MISSING_HANDSHAKE_PARAMS: "Missing required handshake parameters",
    ErrorCodes.UNSUPPORTED_LANGUAGE: "Unsupported language",
    ErrorCodes.INVALID_AUDIO_CONFIG: "Invalid audio configuration",
    ErrorCodes.SERVER_CAPACITY_EXCEEDED: "Server capacity exceeded",
    
    # 数据传输阶段错误
    ErrorCodes.INVALID_AUDIO_FORMAT: "Invalid audio data format",
    ErrorCodes.AUDIO_SIZE_MISMATCH: "Audio data size mismatch",
    ErrorCodes.SEQUENCE_ID_ERROR: "Sequence ID error",
    ErrorCodes.AUDIO_DECODING_FAILED: "Audio data decoding failed",
    ErrorCodes.SAMPLE_RATE_MISMATCH: "Sample rate mismatch",
    ErrorCodes.PROCESSING_TIMEOUT: "Processing timeout",
    
    # 会话管理错误
    ErrorCodes.SESSION_NOT_FOUND: "Session not found",
    ErrorCodes.SESSION_EXPIRED: "Session expired",
    ErrorCodes.INVALID_SESSION_STATE: "Invalid session state",
    ErrorCodes.SESSION_LIMIT_EXCEEDED: "Session limit exceeded",
    
    # 系统错误
    ErrorCodes.INTERNAL_SERVER_ERROR: "Internal server error",
    ErrorCodes.SERVICE_UNAVAILABLE: "Service unavailable",
    ErrorCodes.MODEL_LOADING_FAILED: "Model loading failed",
    ErrorCodes.RESOURCE_EXHAUSTED: "Resource exhausted",
    
    # 协议错误
    ErrorCodes.PROTOCOL_VIOLATION: "Protocol violation",
    ErrorCodes.UNSUPPORTED_MESSAGE: "Unsupported message type",
    ErrorCodes.MESSAGE_FORMAT_ERROR: "Message format error",
    ErrorCodes.CONNECTION_TIMEOUT: "Connection timeout"
}


def create_error_response(code: int, message: str = None, details: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    创建标准错误响应
    
    Args:
        code: 错误码
        message: 错误消息（可选，如果不提供则使用默认消息）
        details: 错误详情（可选）
        
    Returns:
        标准错误响应字典
    """
    if message is None:
        message = ERROR_MESSAGES.get(code, f"Unknown error (code: {code})")
    
    return {
        "error": message,
        "error_code": code,
        "details": details or {},
        "success": False
    }


def handle_exception(e: Exception, default_code: int = ErrorCodes.INTERNAL_SERVER_ERROR) -> Dict[str, Any]:
    """
    处理异常并返回标准错误响应
    
    Args:
        e: 异常对象
        default_code: 默认错误码
        
    Returns:
        标准错误响应字典
    """
    if isinstance(e, ASRError):
        return create_error_response(e.code, e.message, e.details)
    else:
        return create_error_response(default_code, str(e), {"exception_type": type(e).__name__})


class ErrorHandler:
    """错误处理器"""
    
    @staticmethod
    def log_and_return_error(logger, code: int, message: str = None, 
                           details: Dict[str, Any] = None, level: str = "error") -> Dict[str, Any]:
        """
        记录日志并返回错误响应
        
        Args:
            logger: 日志记录器
            code: 错误码
            message: 错误消息
            details: 错误详情
            level: 日志级别
            
        Returns:
            标准错误响应字典
        """
        if message is None:
            message = ERROR_MESSAGES.get(code, f"Unknown error (code: {code})")
        
        # 记录日志
        log_message = f"[{code}] {message}"
        if details:
            log_message += f" Details: {details}"
            
        if level == "error":
            logger.error(log_message)
        elif level == "warning":
            logger.warning(log_message)
        elif level == "info":
            logger.info(log_message)
        else:
            logger.debug(log_message)
        
        return create_error_response(code, message, details)
    
    @staticmethod
    def handle_and_log_exception(logger, e: Exception, 
                                default_code: int = ErrorCodes.INTERNAL_SERVER_ERROR,
                                context: str = "") -> Dict[str, Any]:
        """
        处理异常，记录日志并返回错误响应
        
        Args:
            logger: 日志记录器
            e: 异常对象
            default_code: 默认错误码
            context: 上下文信息
            
        Returns:
            标准错误响应字典
        """
        if isinstance(e, ASRError):
            log_message = f"{context} ASR Error [{e.code}]: {e.message}"
            if e.details:
                log_message += f" Details: {e.details}"
            logger.error(log_message)
            return create_error_response(e.code, e.message, e.details)
        else:
            log_message = f"{context} Unexpected error: {str(e)}"
            logger.error(log_message, exc_info=True)
            return create_error_response(default_code, str(e), {
                "exception_type": type(e).__name__,
                "context": context
            })
