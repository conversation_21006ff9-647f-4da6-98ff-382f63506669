#!/usr/bin/env python3
"""
项目完整性验证脚本
检查所有文件、配置、依赖和逻辑的完整性
"""

import os
import sys
import json
import yaml
import importlib.util
from pathlib import Path

class ProjectValidator:
    """项目验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.passed = []
        
    def log_error(self, message):
        """记录错误"""
        self.errors.append(message)
        print(f"❌ ERROR: {message}")
    
    def log_warning(self, message):
        """记录警告"""
        self.warnings.append(message)
        print(f"⚠️  WARNING: {message}")
    
    def log_pass(self, message):
        """记录通过"""
        self.passed.append(message)
        print(f"✅ PASS: {message}")
    
    def validate_file_structure(self):
        """验证文件结构"""
        print("\n🔍 验证文件结构...")
        
        required_files = [
            "config.yaml",
            "config_loader.py",
            "app.py",
            "app_stream.py",
            "start_services.py",
            "test_services.py",
            "requirements.txt",
            "model/zh/unified_asr/config.pbtxt",
            "model/zh/unified_asr/1/model.py"
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                self.log_pass(f"文件存在: {file_path}")
            else:
                self.log_error(f"文件缺失: {file_path}")
        
        # 检查目录结构
        required_dirs = [
            "model/zh/unified_asr",
            "model/zh/unified_asr/1",
            "tools"
        ]
        
        for dir_path in required_dirs:
            if os.path.exists(dir_path):
                self.log_pass(f"目录存在: {dir_path}")
            else:
                self.log_error(f"目录缺失: {dir_path}")
    
    def validate_config_files(self):
        """验证配置文件"""
        print("\n🔍 验证配置文件...")
        
        # 验证config.yaml
        try:
            with open("config.yaml", 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            required_sections = [
                "triton", "http_service", "stream_service", 
                "asr", "monitoring", "logging"
            ]
            
            for section in required_sections:
                if section in config_data:
                    self.log_pass(f"配置段存在: {section}")
                else:
                    self.log_error(f"配置段缺失: {section}")
                    
        except Exception as e:
            self.log_error(f"config.yaml 解析失败: {e}")
        
        # 验证Triton配置
        try:
            with open("model/zh/unified_asr/config.pbtxt", 'r') as f:
                content = f.read()
            
            required_params = [
                "max_batch_size", "input", "output", "instance_group"
            ]
            
            for param in required_params:
                if param in content:
                    self.log_pass(f"Triton配置包含: {param}")
                else:
                    self.log_error(f"Triton配置缺失: {param}")
                    
        except Exception as e:
            self.log_error(f"Triton配置解析失败: {e}")
    
    def validate_python_imports(self):
        """验证Python导入"""
        print("\n🔍 验证Python导入...")
        
        # 验证config_loader
        try:
            spec = importlib.util.spec_from_file_location("config_loader", "config_loader.py")
            config_loader = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_loader)
            
            # 检查必要的配置类
            required_configs = [
                "triton_config", "http_config", "stream_config", 
                "asr_config", "monitoring_config"
            ]
            
            for config_name in required_configs:
                if hasattr(config_loader, config_name):
                    self.log_pass(f"配置对象存在: {config_name}")
                else:
                    self.log_error(f"配置对象缺失: {config_name}")
                    
        except Exception as e:
            self.log_error(f"config_loader.py 导入失败: {e}")
        
        # 验证主要模块的导入语法
        modules_to_check = [
            "app.py",
            "app_stream.py", 
            "start_services.py"
        ]
        
        for module_file in modules_to_check:
            try:
                with open(module_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有config_loader导入
                if "from config_loader import" in content:
                    self.log_pass(f"{module_file} 正确导入配置")
                else:
                    self.log_warning(f"{module_file} 可能未正确导入配置")
                    
                # 检查是否还有硬编码配置
                if "class Config:" in content or "class StreamConfig:" in content:
                    self.log_warning(f"{module_file} 仍包含硬编码配置类")
                    
            except Exception as e:
                self.log_error(f"检查 {module_file} 失败: {e}")
    
    def validate_dependencies(self):
        """验证依赖"""
        print("\n🔍 验证依赖...")
        
        try:
            with open("requirements.txt", 'r') as f:
                requirements = f.read()
            
            critical_deps = [
                "fastapi", "uvicorn", "websockets", "pydantic",
                "tritonclient", "torch", "numpy", "requests",
                "psutil", "PyYAML"
            ]
            
            for dep in critical_deps:
                if dep in requirements:
                    self.log_pass(f"依赖包含: {dep}")
                else:
                    self.log_warning(f"依赖可能缺失: {dep}")
                    
        except Exception as e:
            self.log_error(f"requirements.txt 读取失败: {e}")
    
    def validate_configuration_consistency(self):
        """验证配置一致性"""
        print("\n🔍 验证配置一致性...")
        
        try:
            # 加载配置
            sys.path.append('.')
            from config_loader import triton_config, http_config, stream_config
            
            # 检查端口冲突
            ports = [
                triton_config.HTTP_PORT,
                triton_config.GRPC_PORT, 
                triton_config.METRICS_PORT,
                http_config.PORT,
                stream_config.PORT
            ]
            
            if len(ports) == len(set(ports)):
                self.log_pass("端口配置无冲突")
            else:
                self.log_error("端口配置存在冲突")
            
            # 检查路径配置
            if os.path.exists(triton_config.MODEL_REPOSITORY):
                self.log_pass(f"模型仓库路径存在: {triton_config.MODEL_REPOSITORY}")
            else:
                self.log_error(f"模型仓库路径不存在: {triton_config.MODEL_REPOSITORY}")
            
            if os.path.exists(http_config.UPLOAD_DIR) or http_config.UPLOAD_DIR == "wavs/uploads":
                self.log_pass(f"上传目录配置正确: {http_config.UPLOAD_DIR}")
            else:
                self.log_warning(f"上传目录可能需要创建: {http_config.UPLOAD_DIR}")
                
        except Exception as e:
            self.log_error(f"配置一致性检查失败: {e}")
    
    def validate_model_configuration(self):
        """验证模型配置"""
        print("\n🔍 验证模型配置...")
        
        model_file = "model/zh/unified_asr/1/model.py"
        if os.path.exists(model_file):
            try:
                with open(model_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查必要的类和方法
                required_elements = [
                    "class TritonPythonModel",
                    "def initialize",
                    "def execute", 
                    "def finalize",
                    "PerformanceMonitor",
                    "VolumeCalculator",
                    "ConfidenceCalculator"
                ]
                
                for element in required_elements:
                    if element in content:
                        self.log_pass(f"模型包含: {element}")
                    else:
                        self.log_error(f"模型缺失: {element}")
                        
            except Exception as e:
                self.log_error(f"模型文件检查失败: {e}")
        else:
            self.log_error("模型文件不存在")
    
    def validate_service_scripts(self):
        """验证服务脚本"""
        print("\n🔍 验证服务脚本...")
        
        # 检查启动脚本
        start_script = "start_services.py"
        if os.path.exists(start_script):
            try:
                with open(start_script, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否使用配置
                if "triton_config" in content and "http_config" in content:
                    self.log_pass("启动脚本使用配置对象")
                else:
                    self.log_error("启动脚本未正确使用配置对象")
                
                # 检查硬编码
                if "localhost:8000" in content or "localhost:8080" in content:
                    self.log_warning("启动脚本可能包含硬编码地址")
                    
            except Exception as e:
                self.log_error(f"启动脚本检查失败: {e}")
    
    def run_validation(self):
        """运行完整验证"""
        print("🚀 开始项目完整性验证...")
        
        self.validate_file_structure()
        self.validate_config_files()
        self.validate_python_imports()
        self.validate_dependencies()
        self.validate_configuration_consistency()
        self.validate_model_configuration()
        self.validate_service_scripts()
        
        # 输出总结
        print("\n" + "="*60)
        print("📊 验证结果总结")
        print("="*60)
        print(f"✅ 通过: {len(self.passed)}")
        print(f"⚠️  警告: {len(self.warnings)}")
        print(f"❌ 错误: {len(self.errors)}")
        
        if self.errors:
            print("\n❌ 发现的错误:")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.warnings:
            print("\n⚠️  发现的警告:")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        print("\n" + "="*60)
        
        if not self.errors:
            print("🎉 项目验证通过！可以开始部署。")
            return True
        else:
            print("💥 项目验证失败！请修复错误后重试。")
            return False

def main():
    """主函数"""
    validator = ProjectValidator()
    success = validator.run_validation()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
