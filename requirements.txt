# Core dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=11.0
pydantic>=2.0.0
python-multipart>=0.0.6
aiofiles>=23.0.0
cryptography>=45.0.4

# Triton client
tritonclient>=2.40.0

# Audio processing
numpy>=1.24.0
librosa>=0.10.0
soundfile>=0.12.0

# Feature extraction
# kaldifeat>=1.25.0 (whl)

# ONNX Runtime
onnx
onnxruntime-gpu>=1.16.0  # Optional for GPU support

# CTC Decoding (custom build required)
# swig-decoders  # Custom package, needs to be built separately

# System monitoring
psutil>=5.9.0

# HTTP client
requests>=2.31.0
httpx>=0.25.0

# YAML support
PyYAML>=6.0

# WebRTC VAD
webrtcvad>=2.0.10

# Development dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Optional: For advanced audio processing
# scipy>=1.11.0
# matplotlib>=3.7.0  # For audio visualization

# Optional: For database support (if needed)
# sqlalchemy>=2.0.0
# alembic>=1.12.0

# Optional: For Redis support (if needed for task storage)
# redis>=5.0.0
# aioredis>=2.0.0
