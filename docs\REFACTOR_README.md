# ASR Server 重构说明

## 重构概述

本次重构将原来分散的多个Triton Python backends整合为一个统一的自定义Python后端 `unified_asr`，实现了端到端的语音识别处理流程。

## 原始架构

### 原始模块结构
```
model/zh/
├── audio_trans/          # 音频流入口
├── audio_file_trans/     # 音频文件入口  
├── attention_rescoring/  # Ensemble模型
├── feature_extractor/    # 特征提取
├── encoder/             # ONNX编码器
├── decoder/             # ONNX解码器
└── scoring/             # CTC解码和后处理
```

### 原始处理流程
1. `audio_trans` 或 `audio_file_trans` 接收请求
2. 音频预处理和VAD切分
3. 调用 `attention_rescoring` ensemble模型
4. `attention_rescoring` 串行调用：
   - `feature_extractor` → `encoder` → `scoring`
5. 返回转录结果

## 重构后架构

### 新模块结构
```
model/zh/
└── unified_asr/          # 统一ASR后端
    ├── config.pbtxt      # 配置文件
    └── 1/
        └── model.py      # 统一实现
```

### 新处理流程
1. `unified_asr` 接收音频数据流和语种代码
2. 内部完成所有处理步骤：
   - 音频解析和预处理
   - VAD语音活动检测
   - 特征提取（集成kaldifeat）
   - ONNX编码器推理
   - CTC beam search解码
   - 后处理和结果格式化
3. 返回完整转录结果

## 重构优势

### 1. 简化架构
- **减少模型数量**：从7个模型减少到1个
- **消除网络开销**：避免模型间的网络通信
- **简化部署**：只需部署一个模型

### 2. 提升性能
- **减少序列化开销**：数据在内存中直接传递
- **优化资源使用**：统一的GPU/CPU资源管理
- **降低延迟**：消除模型间调用延迟

### 3. 易于维护
- **统一配置**：所有参数在一个配置文件中
- **集中日志**：统一的日志管理
- **简化调试**：单一入口点便于问题定位

## 配置参数

### VAD参数
- `vad_enable`: 是否启用VAD
- `vad_type`: VAD类型（webrtcvad）
- `vad_level`: VAD敏感度级别
- `vad_frame_len`: VAD帧长度（毫秒）
- `vad_window_size`: VAD窗口大小
- `vad_decision_thres`: VAD决策阈值
- `vad_max_speech_len`: 最大语音长度（秒）
- `vad_min_speech_len`: 最小语音长度（秒）
- `vad_merge_sil_len`: 静音合并长度（秒）

### 特征提取参数
- `num_mel_bins`: Mel滤波器组数量
- `frame_shift_in_ms`: 帧移（毫秒）
- `frame_length_in_ms`: 帧长（毫秒）
- `sample_rate`: 采样率

### 模型参数
- `encoder_model_path`: 编码器ONNX模型路径
- `language`: 语言代码
- `vocabulary`: 词汇表路径
- `bidecoder`: 是否使用双向解码器
- `lm_path`: 语言模型路径
- `hotwords_path`: 热词文件路径

### 后处理参数
- `post_enable`: 是否启用后处理
- `itn_enable`: 是否启用ITN（逆文本规范化）

## 输入输出格式

### 输入
- `WAV`: 音频数据流（TYPE_FP32, dims: [-1]）
- `LANG`: 语种代码（TYPE_STRING, dims: [1]）

### 输出
- `TRANSCRIPTS`: 转录结果（TYPE_STRING, dims: [-1]）

### 输出格式示例
```json
{
  "text": "完整的转录文本",
  "segments": [
    {
      "text": "第一段文本",
      "start": 0.0,
      "end": 2.5
    },
    {
      "text": "第二段文本", 
      "start": 2.5,
      "end": 5.0
    }
  ],
  "duration": 5.0,
  "info": {
    "cost_time": "0.1234 s"
  }
}
```

## 使用方法

### 1. 部署模型
将 `model/zh/unified_asr` 目录复制到Triton模型仓库中。

### 2. 启动Triton Server
```bash
tritonserver --model-repository=/path/to/model/repository
```

### 3. 发送推理请求
```python
import tritonclient.http as httpclient
import numpy as np

# 创建客户端
client = httpclient.InferenceServerClient(url="localhost:8000")

# 准备输入数据
audio_data = np.random.randn(16000).astype(np.float32)  # 1秒音频
lang_code = np.array(["zh"], dtype=object)

# 创建输入张量
inputs = [
    httpclient.InferInput("WAV", audio_data.shape, "FP32"),
    httpclient.InferInput("LANG", lang_code.shape, "BYTES")
]
inputs[0].set_data_from_numpy(audio_data)
inputs[1].set_data_from_numpy(lang_code)

# 创建输出张量
outputs = [httpclient.InferRequestedOutput("TRANSCRIPTS")]

# 发送请求
response = client.infer("unified_asr", inputs, outputs=outputs)

# 获取结果
result = response.as_numpy("TRANSCRIPTS")
print(result)
```

## 依赖要求

### Python包
- torch
- torchaudio
- kaldifeat
- onnxruntime
- numpy
- pyyaml
- webrtcvad

### 系统依赖
- CTC解码器（swig_decoders）
- ONNX模型文件
- 词汇表文件
- 语言模型文件（可选）
- 热词文件（可选）

## 注意事项

1. **模型路径**：确保配置文件中的模型路径正确
2. **GPU支持**：如果使用GPU，确保CUDA环境正确配置
3. **内存使用**：统一模型可能使用更多内存，注意监控
4. **兼容性**：确保所有依赖包版本兼容

## 测试验证

运行测试脚本验证重构结果：
```bash
python test_unified_asr.py
```

## 回滚方案

如果需要回滚到原始架构，原始模型文件仍然保留在相应目录中，可以随时恢复使用。
