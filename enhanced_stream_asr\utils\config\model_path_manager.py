"""
模型路径管理器
负责模型文件路径的验证、配置和管理
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import yaml

from .validator import ConfigValidator
from ..exceptions import ErrorCodes, create_error_response

logger = logging.getLogger(__name__)


class ModelPathManager:
    """模型路径管理器"""
    
    def __init__(self, base_model_dir: str = "models"):
        """
        初始化模型路径管理器
        
        Args:
            base_model_dir: 模型基础目录
        """
        self.base_model_dir = Path(base_model_dir)
        self.required_files = {
            "asr": ["encoder.onnx", "ctc.onnx", "units.txt"],
            "lid": ["lid_model.onnx"],
            "vad": []  # VAD使用webrtcvad，不需要模型文件
        }
        
    def validate_model_paths(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证模型路径配置
        
        Args:
            config: 完整配置字典
            
        Returns:
            (是否有效, 错误列表)
        """
        errors = []
        
        try:
            # 验证语种模型路径
            language_configs = config.get("languages", {})
            for lang_code, lang_config in language_configs.items():
                lang_errors = self._validate_language_model_paths(lang_code, lang_config)
                errors.extend(lang_errors)
            
            # 验证LID模型路径
            lid_config = config.get("lid", {})
            if lid_config.get("enable", True):
                lid_errors = self._validate_lid_model_paths(lid_config)
                errors.extend(lid_errors)
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Model path validation failed: {e}")
            errors.append(f"Model path validation error: {e}")
            return False, errors
    
    def _validate_language_model_paths(self, lang_code: str, config: Dict[str, Any]) -> List[str]:
        """验证语种模型路径"""
        errors = []
        
        model_config = config.get("model", {})
        model_path = model_config.get("model_path", "")
        
        if not model_path:
            errors.append(f"Missing model_path for language: {lang_code}")
            return errors
        
        # 转换为绝对路径
        if not os.path.isabs(model_path):
            model_path = self.base_model_dir / model_path
        else:
            model_path = Path(model_path)
        
        # 检查模型目录是否存在
        if not model_path.exists():
            errors.append(f"Model directory not found for {lang_code}: {model_path}")
            return errors
        
        # 检查必需的模型文件
        for required_file in self.required_files["asr"]:
            file_path = model_path / required_file
            if not file_path.exists():
                errors.append(f"Required model file not found for {lang_code}: {file_path}")
        
        # 检查可选文件
        dict_path = model_config.get("dict_path", "")
        if dict_path:
            if not os.path.isabs(dict_path):
                dict_path = model_path / dict_path
            else:
                dict_path = Path(dict_path)
            
            if not dict_path.exists():
                errors.append(f"Dictionary file not found for {lang_code}: {dict_path}")
        
        hotwords_path = model_config.get("hotwords_path", "")
        if hotwords_path:
            if not os.path.isabs(hotwords_path):
                hotwords_path = model_path / hotwords_path
            else:
                hotwords_path = Path(hotwords_path)
            
            if not hotwords_path.exists():
                logger.warning(f"Hotwords file not found for {lang_code}: {hotwords_path}")
                # 热词文件不是必需的，只记录警告
        
        return errors
    
    def _validate_lid_model_paths(self, config: Dict[str, Any]) -> List[str]:
        """验证LID模型路径"""
        errors = []
        
        model_config = config.get("model", {})
        model_path = model_config.get("model_path", "")
        
        if not model_path:
            errors.append("Missing model_path for LID")
            return errors
        
        # 转换为绝对路径
        if not os.path.isabs(model_path):
            model_path = self.base_model_dir / model_path
        else:
            model_path = Path(model_path)
        
        # 检查LID模型文件
        if not model_path.exists():
            errors.append(f"LID model file not found: {model_path}")
        
        return errors
    
    def auto_discover_models(self) -> Dict[str, Any]:
        """
        自动发现模型文件
        
        Returns:
            发现的模型配置
        """
        discovered = {
            "languages": {},
            "lid": {},
            "base_dir": str(self.base_model_dir)
        }
        
        try:
            if not self.base_model_dir.exists():
                logger.warning(f"Model base directory not found: {self.base_model_dir}")
                return discovered
            
            # 发现语种模型
            for lang_dir in self.base_model_dir.iterdir():
                if lang_dir.is_dir() and lang_dir.name != "lid":
                    lang_code = lang_dir.name
                    
                    # 检查是否包含必需的ASR模型文件
                    has_required_files = all(
                        (lang_dir / file).exists() 
                        for file in self.required_files["asr"]
                    )
                    
                    if has_required_files:
                        discovered["languages"][lang_code] = {
                            "model_path": str(lang_dir),
                            "dict_path": str(lang_dir / "units.txt"),
                            "hotwords_path": str(lang_dir / "hotwords.txt") 
                                if (lang_dir / "hotwords.txt").exists() else ""
                        }
                        logger.info(f"Discovered ASR model for language: {lang_code}")
            
            # 发现LID模型
            lid_model_path = self.base_model_dir / "lid" / "lid_model.onnx"
            if lid_model_path.exists():
                discovered["lid"] = {
                    "model_path": str(lid_model_path)
                }
                logger.info("Discovered LID model")
            
            return discovered
            
        except Exception as e:
            logger.error(f"Model auto-discovery failed: {e}")
            return discovered
    
    def generate_model_config(self, discovered_models: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据发现的模型生成配置
        
        Args:
            discovered_models: 自动发现的模型信息
            
        Returns:
            生成的配置字典
        """
        config = {}
        
        # 生成语种配置
        languages = discovered_models.get("languages", {})
        for lang_code, model_info in languages.items():
            config[f"lang_configs/{lang_code}.yaml"] = {
                "code": lang_code,
                "name": self._get_language_name(lang_code),
                "separator": self._get_default_separator(lang_code),
                "silence_threshold": 0.35,
                "model": {
                    "model_path": model_info["model_path"],
                    "dict_path": model_info["dict_path"],
                    "hotwords_path": model_info.get("hotwords_path", ""),
                    "chunk_size": 16,
                    "left_chunks": 16,
                    "decoding_window": 67,
                    "subsampling_rate": 4,
                    "right_context": 7,
                    "device": "cpu",
                    "device_id": 0,
                    "quantized": True
                },
                "features": {
                    "enable_punctuation": True,
                    "enable_itn": True,
                    "enable_hotwords": bool(model_info.get("hotwords_path"))
                }
            }
        
        # 生成LID配置
        lid_info = discovered_models.get("lid", {})
        if lid_info:
            config["lid_config.yaml"] = {
                "enable": True,
                "model": {
                    "model_path": lid_info["model_path"]
                },
                "confidence_threshold": 0.8,
                "min_audio_duration": 0.4,
                "max_audio_duration": 2.4,
                "progressive_steps": [0.4, 0.8, 2.4],
                "language_mapping": self._get_default_language_mapping(languages.keys())
            }
        
        return config
    
    def _get_language_name(self, lang_code: str) -> str:
        """获取语种名称"""
        name_map = {
            "zh": "中文",
            "en": "English",
            "ru": "Русский",
            "ug": "ئۇيغۇرچە",
            "kk": "Қазақша"
        }
        return name_map.get(lang_code, lang_code.upper())
    
    def _get_default_separator(self, lang_code: str) -> str:
        """获取默认分隔符"""
        separator_map = {
            "zh": "，。！？；：",
            "en": ",.!?;:",
            "ru": ",.!?;:",
            "ug": "،.!?؛:",
            "kk": ",.!?;:"
        }
        return separator_map.get(lang_code, ",.!?;:")
    
    def _get_default_language_mapping(self, lang_codes: List[str]) -> Dict[int, str]:
        """获取默认语种映射"""
        mapping = {}
        for i, lang_code in enumerate(sorted(lang_codes)):
            mapping[i] = lang_code
        return mapping
    
    def fix_model_paths(self, config_dir: str = "configs") -> Tuple[bool, List[str]]:
        """
        修复模型路径配置
        
        Args:
            config_dir: 配置目录
            
        Returns:
            (是否成功, 消息列表)
        """
        messages = []
        
        try:
            # 自动发现模型
            discovered = self.auto_discover_models()
            
            if not discovered["languages"] and not discovered["lid"]:
                messages.append("No models found in the models directory")
                return False, messages
            
            # 生成配置
            generated_configs = self.generate_model_config(discovered)
            
            # 保存配置文件
            config_path = Path(config_dir)
            config_path.mkdir(exist_ok=True)
            
            for config_file, config_data in generated_configs.items():
                file_path = config_path / config_file
                file_path.parent.mkdir(exist_ok=True)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_data, f, default_flow_style=False, 
                             allow_unicode=True, sort_keys=False)
                
                messages.append(f"Generated config: {config_file}")
            
            messages.append(f"Model path configuration completed")
            return True, messages
            
        except Exception as e:
            logger.error(f"Failed to fix model paths: {e}")
            messages.append(f"Model path fix failed: {e}")
            return False, messages
    
    def create_model_directory_structure(self) -> Tuple[bool, List[str]]:
        """
        创建标准的模型目录结构
        
        Returns:
            (是否成功, 消息列表)
        """
        messages = []
        
        try:
            # 创建基础目录
            self.base_model_dir.mkdir(exist_ok=True)
            messages.append(f"Created base model directory: {self.base_model_dir}")
            
            # 创建语种目录
            default_languages = ["zh", "en", "ru", "ug", "kk"]
            for lang in default_languages:
                lang_dir = self.base_model_dir / lang
                lang_dir.mkdir(exist_ok=True)
                
                # 创建README文件
                readme_path = lang_dir / "README.md"
                if not readme_path.exists():
                    readme_content = f"""# {lang.upper()} Language Model

## Required Files:
- encoder.onnx: Encoder model
- ctc.onnx: CTC model  
- units.txt: Vocabulary file

## Optional Files:
- hotwords.txt: Hotwords file
- decoder.onnx: Decoder model (for attention rescoring)

## Usage:
Place the model files in this directory and update the configuration accordingly.
"""
                    with open(readme_path, 'w', encoding='utf-8') as f:
                        f.write(readme_content)
                
                messages.append(f"Created language directory: {lang_dir}")
            
            # 创建LID目录
            lid_dir = self.base_model_dir / "lid"
            lid_dir.mkdir(exist_ok=True)
            
            lid_readme = lid_dir / "README.md"
            if not lid_readme.exists():
                lid_content = """# Language Identification Model

## Required Files:
- lid_model.onnx: Language identification model

## Usage:
Place the LID model file in this directory and update the configuration accordingly.
"""
                with open(lid_readme, 'w', encoding='utf-8') as f:
                    f.write(lid_content)
            
            messages.append(f"Created LID directory: {lid_dir}")
            
            return True, messages
            
        except Exception as e:
            logger.error(f"Failed to create model directory structure: {e}")
            messages.append(f"Directory creation failed: {e}")
            return False, messages


# 全局模型路径管理器实例
global_model_path_manager = ModelPathManager()
