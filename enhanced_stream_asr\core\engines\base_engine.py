"""
基础推理引擎抽象类
定义推理引擎的通用接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union
import numpy as np
import torch


class BaseEngine(ABC):
    """推理引擎基类"""
    
    def __init__(self, model_path: str, config: Dict[str, Any]):
        """
        初始化推理引擎
        
        Args:
            model_path: 模型路径
            config: 引擎配置
        """
        self.model_path = model_path
        self.config = config
        self.is_loaded = False
        
    @abstractmethod
    def load_model(self) -> bool:
        """
        加载模型
        
        Returns:
            bool: 加载是否成功
        """
        pass
        
    @abstractmethod
    def unload_model(self) -> bool:
        """
        卸载模型
        
        Returns:
            bool: 卸载是否成功
        """
        pass
        
    @abstractmethod
    def infer(self, inputs: Dict[str, Union[np.ndarray, torch.Tensor]]) -> Dict[str, Any]:
        """
        执行推理
        
        Args:
            inputs: 输入数据字典
            
        Returns:
            Dict[str, Any]: 推理结果
        """
        pass
        
    @abstractmethod
    def is_ready(self) -> bool:
        """
        检查引擎是否就绪
        
        Returns:
            bool: 是否就绪
        """
        pass
        
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        return {
            "model_path": self.model_path,
            "is_loaded": self.is_loaded,
            "config": self.config
        }
        
    def __enter__(self):
        """上下文管理器入口"""
        self.load_model()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.unload_model()
