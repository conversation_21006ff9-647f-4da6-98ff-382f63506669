2025-05-16 07:53:54.495 | INFO  | modules.config:read_params :19 - 加载配置: /ws/online_asr_server/server_config_zh.yaml
2025-05-16 07:53:54.531 | INFO  | __main__:<module>    :5 - Run server on 0.0.0.0:10080
2025-05-16 07:53:54.569 | INFO  | modules.decoder:load_onnx   :61 - 加载模型: /ws/MODELS/online_onnx_zh, fp16: False, quant: True, device: cpu
2025-05-16 07:53:55.502 | INFO  | modules.symbol_table:load_dict   :19 - 加载词表: /ws/MODELS/online_onnx_zh/units.txt
2025-05-16 07:53:55.510 | INFO  | contextlib:__aenter__  :171 - Sever start, init FEAT_PIPE, manager, SYMBOL_TABLE
2025-05-16 07:53:57.011 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "000"

2025-05-16 07:53:57.025 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:53:57.424 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:53:57.555 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第0个数据包, 更新识别结果: "很"
2025-05-16 07:53:57.828 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:53:58.229 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:53:58.337 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第1个数据包, 更新识别结果: "很高兴来"
2025-05-16 07:53:58.637 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:53:59.037 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:53:59.240 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第2个数据包, 更新识别结果: "很高兴来到这里"
2025-05-16 07:53:59.442 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:53:59.659 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第3个数据包, 更新识别结果: "很高兴来到这里与大"
2025-05-16 07:53:59.844 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:00.245 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:00.466 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第4个数据包, 更新识别结果: "很高兴来到这里与大家探讨"
2025-05-16 07:54:00.649 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:01.051 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:01.223 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第5个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为"
2025-05-16 07:54:01.454 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:01.635 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第6个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一"
2025-05-16 07:54:01.857 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:02.260 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:02.373 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第7个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题"
2025-05-16 07:54:02.662 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:03.065 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:03.240 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第8个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那"
2025-05-16 07:54:03.468 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:03.614 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第9个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美"
2025-05-16 07:54:03.872 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:04.277 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:04.680 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-05-16 07:54:05.085 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-05-16 07:54:05.258 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第10个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究"
2025-05-16 07:54:05.486 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-05-16 07:54:05.677 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第11个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与"
2025-05-16 07:54:05.890 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-05-16 07:54:06.288 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-05-16 07:54:06.485 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第12个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与审美哲学"
2025-05-16 07:54:06.693 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-05-16 07:54:07.097 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-05-16 07:54:07.507 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-05-16 07:54:07.903 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-05-16 07:54:08.107 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第13个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与审美哲学事实上"
2025-05-16 07:54:08.303 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-05-16 07:54:08.480 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第14个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与审美哲学事实上这"
2025-05-16 07:54:08.708 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-05-16 07:54:09.109 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-05-16 07:54:09.419 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第15个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与审美哲学事实上这是我的"
2025-05-16 07:54:09.509 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第31个数据包, 累计帧数: 1181
2025-05-16 07:54:09.665 | INFO  | modules.connect:on_decode   :301 - client_id:000 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:09.665 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第16个数据包, 更新识别结果: "很高兴来到这里 与大家探讨我这为关心的一个主题 那就是美 我研究艺术与审美哲学 事实上 这是我的工作"
2025-05-16 07:54:09.665 | INFO  | asyncio.events:_run        :81 - client_id:000 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:09.665 | INFO  | asyncio.events:_run        :81 - client_id: 000 - 关闭连接，清理资源
2025-05-16 07:54:09.665 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:09.667 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:09.667 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:09.667 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:09.920 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "000"

2025-05-16 07:54:09.927 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "111"

2025-05-16 07:54:09.930 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "222"

2025-05-16 07:54:09.933 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "333"

2025-05-16 07:54:09.936 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "444"

2025-05-16 07:54:09.939 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "666"

2025-05-16 07:54:09.942 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "555"

2025-05-16 07:54:09.945 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "777"

2025-05-16 07:54:09.952 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "888"

2025-05-16 07:54:09.956 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "999"

2025-05-16 07:54:09.967 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:09.973 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:09.980 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:09.986 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:09.992 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:09.998 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:10.006 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:10.015 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:10.023 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:10.029 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 07:54:10.336 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:10.577 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第0个数据包, 更新识别结果: "很"
2025-05-16 07:54:10.588 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:10.808 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第0个数据包, 更新识别结果: "我想"
2025-05-16 07:54:10.815 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:10.952 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第0个数据包, 更新识别结果: "哪"
2025-05-16 07:54:10.959 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:11.060 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第0个数据包, 更新识别结果: "这是"
2025-05-16 07:54:11.070 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:11.164 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第0个数据包, 更新识别结果: "想"
2025-05-16 07:54:11.187 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:11.350 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第0个数据包, 更新识别结果: "一"
2025-05-16 07:54:11.358 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:11.450 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第0个数据包, 更新识别结果: "加利"
2025-05-16 07:54:11.457 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:11.583 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第0个数据包, 更新识别结果: "很难"
2025-05-16 07:54:11.596 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:11.780 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第0个数据包, 更新识别结果: "然"
2025-05-16 07:54:11.788 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 07:54:11.948 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第0个数据包, 更新识别结果: "这"
2025-05-16 07:54:11.967 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:11.977 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:11.984 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:11.992 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:12.000 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:12.007 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:12.013 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:12.019 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:12.025 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:12.032 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 07:54:12.041 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:12.216 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第1个数据包, 更新识别结果: "很高兴来"
2025-05-16 07:54:12.224 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:12.420 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第1个数据包, 更新识别结果: "我想在理性"
2025-05-16 07:54:12.430 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:12.656 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第1个数据包, 更新识别结果: "哪些感知"
2025-05-16 07:54:12.667 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:12.866 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第1个数据包, 更新识别结果: "这是个很复杂"
2025-05-16 07:54:12.875 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:13.109 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第1个数据包, 更新识别结果: "想象一下这"
2025-05-16 07:54:13.124 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:13.357 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第1个数据包, 更新识别结果: "一部简奥斯丁"
2025-05-16 07:54:13.366 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:13.563 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第1个数据包, 更新识别结果: "加利福尼亚"
2025-05-16 07:54:13.572 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:13.744 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第1个数据包, 更新识别结果: "很难用一个"
2025-05-16 07:54:13.752 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:14.007 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第1个数据包, 更新识别结果: "然而"
2025-05-16 07:54:14.031 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 07:54:14.156 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第1个数据包, 更新识别结果: "这一理论"
2025-05-16 07:54:14.174 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.182 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.190 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.199 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.206 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.213 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.220 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.227 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.235 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.242 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第4个数据包, 累计帧数: 190
2025-05-16 07:54:14.249 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:14.432 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第2个数据包, 更新识别结果: "很高兴来到这里"
2025-05-16 07:54:14.442 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:14.667 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第2个数据包, 更新识别结果: "我想在理性哲"
2025-05-16 07:54:14.681 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:14.946 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第2个数据包, 更新识别结果: "哪些感知是能被"
2025-05-16 07:54:14.959 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:15.265 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第2个数据包, 更新识别结果: "这是个很复杂的问题"
2025-05-16 07:54:15.275 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:15.507 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第2个数据包, 更新识别结果: "想象一下这高度的多"
2025-05-16 07:54:15.516 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:15.711 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第2个数据包, 更新识别结果: "一部简奥斯丁的小说"
2025-05-16 07:54:15.718 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:15.884 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第2个数据包, 更新识别结果: "加利福尼亚中部的风"
2025-05-16 07:54:15.893 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:16.056 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第2个数据包, 更新识别结果: "很难用一个解释"
2025-05-16 07:54:16.064 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:16.256 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第2个数据包, 更新识别结果: "然而我能给"
2025-05-16 07:54:16.263 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第5个数据包, 累计帧数: 228
2025-05-16 07:54:16.444 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第2个数据包, 更新识别结果: "这一理论不是来自"
2025-05-16 07:54:16.463 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:16.637 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第3个数据包, 更新识别结果: "很高兴来到这里与大"
2025-05-16 07:54:16.644 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:16.857 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第3个数据包, 更新识别结果: "我想在理性哲学"
2025-05-16 07:54:16.865 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:17.022 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第3个数据包, 更新识别结果: "哪些感知是能被描述出来"
2025-05-16 07:54:17.029 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:17.221 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:17.415 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第3个数据包, 更新识别结果: "想象一下这高度的多元性"
2025-05-16 07:54:17.421 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:17.603 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:17.782 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第3个数据包, 更新识别结果: "加利福尼亚中部的风景"
2025-05-16 07:54:17.789 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:17.959 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第3个数据包, 更新识别结果: "很难用一个解释就说清楚"
2025-05-16 07:54:17.965 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:18.158 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第3个数据包, 更新识别结果: "然而我能给大家体会"
2025-05-16 07:54:18.167 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第6个数据包, 累计帧数: 266
2025-05-16 07:54:18.326 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第3个数据包, 更新识别结果: "这一理论不是来自艺"
2025-05-16 07:54:18.340 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.348 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.354 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.360 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.370 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.381 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.388 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.394 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.400 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.405 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第7个数据包, 累计帧数: 304
2025-05-16 07:54:18.413 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:18.615 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第4个数据包, 更新识别结果: "很高兴来到这里与大家探讨"
2025-05-16 07:54:18.622 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:18.780 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第4个数据包, 更新识别结果: "我想在理性哲学以及心理"
2025-05-16 07:54:18.787 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:18.954 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第4个数据包, 更新识别结果: "哪些感知是能被描述出来的"
2025-05-16 07:54:18.961 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:19.157 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第3个数据包, 更新识别结果: "这是个很复杂的问题部分原因"
2025-05-16 07:54:19.165 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:19.417 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第4个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸"
2025-05-16 07:54:19.424 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:19.651 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第3个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷"
2025-05-16 07:54:19.659 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:19.898 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第4个数据包, 更新识别结果: "加利福尼亚中部的风景北"
2025-05-16 07:54:19.905 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:20.076 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第4个数据包, 更新识别结果: "很难用一个解释就说清楚所"
2025-05-16 07:54:20.084 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:20.251 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第4个数据包, 更新识别结果: "然而我能给大家体会一哈"
2025-05-16 07:54:20.259 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第8个数据包, 累计帧数: 342
2025-05-16 07:54:20.452 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第4个数据包, 更新识别结果: "这一理论不是来自艺术哲学家"
2025-05-16 07:54:20.465 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.471 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.477 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.484 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.492 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.499 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.505 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.511 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.518 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.525 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第9个数据包, 累计帧数: 380
2025-05-16 07:54:20.532 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:20.767 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第5个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为"
2025-05-16 07:54:20.776 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:20.995 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第5个数据包, 更新识别结果: "我想在理性哲学以及心理学的层面"
2025-05-16 07:54:21.003 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:21.198 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第5个数据包, 更新识别结果: "哪些感知是能被描述出来的以"
2025-05-16 07:54:21.205 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:21.395 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第4个数据包, 更新识别结果: "这是个很复杂的问题部分原因是"
2025-05-16 07:54:21.404 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:21.679 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第5个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋"
2025-05-16 07:54:21.687 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:21.852 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第4个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿"
2025-05-16 07:54:21.863 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:22.032 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第5个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的"
2025-05-16 07:54:22.041 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:22.217 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第5个数据包, 更新识别结果: "很难用一个解释就说清楚所有这些"
2025-05-16 07:54:22.225 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:22.392 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第5个数据包, 更新识别结果: "然而我能给大家体会一哈我认为"
2025-05-16 07:54:22.399 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第10个数据包, 累计帧数: 418
2025-05-16 07:54:22.614 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:22.819 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第6个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一"
2025-05-16 07:54:22.827 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:23.050 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第6个数据包, 更新识别结果: "我想在理性哲学以及心理学的层面上理解"
2025-05-16 07:54:23.061 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:23.219 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第6个数据包, 更新识别结果: "哪些感知是能被描述出来的以及人们在"
2025-05-16 07:54:23.228 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:23.389 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第5个数据包, 更新识别结果: "这是个很复杂的问题部分原因是因为我们"
2025-05-16 07:54:23.397 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:23.549 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第6个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩"
2025-05-16 07:54:23.557 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:23.743 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第5个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔"
2025-05-16 07:54:23.754 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:23.922 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第6个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的富士山"
2025-05-16 07:54:23.930 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:24.093 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第6个数据包, 更新识别结果: "很难用一个解释就说清楚所有这些事物中"
2025-05-16 07:54:24.100 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:24.257 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第6个数据包, 更新识别结果: "然而我能给大家体会一哈我认为迄今为"
2025-05-16 07:54:24.265 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第11个数据包, 累计帧数: 456
2025-05-16 07:54:24.437 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第5个数据包, 更新识别结果: "这一理论不是来自艺术哲学家也不是"
2025-05-16 07:54:24.448 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.454 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.460 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.466 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.473 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.479 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.486 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.492 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.499 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.505 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第12个数据包, 累计帧数: 494
2025-05-16 07:54:24.512 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:24.703 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第7个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题"
2025-05-16 07:54:24.714 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:24.924 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:25.090 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第7个数据包, 更新识别结果: "哪些感知是能被描述出来的以及人们在尝试理"
2025-05-16 07:54:25.103 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:25.272 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第6个数据包, 更新识别结果: "这是个很复杂的问题部分原因是因为我们认为"
2025-05-16 07:54:25.280 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:25.472 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第7个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所"
2025-05-16 07:54:25.479 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:25.655 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第6个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在"
2025-05-16 07:54:25.663 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:25.838 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:26.023 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第7个数据包, 更新识别结果: "很难用一个解释就说清楚所有这些事物中所存在"
2025-05-16 07:54:26.030 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:26.211 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第7个数据包, 更新识别结果: "然而我能给大家体会一哈我认为迄今为止"
2025-05-16 07:54:26.218 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第13个数据包, 累计帧数: 532
2025-05-16 07:54:26.397 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第6个数据包, 更新识别结果: "这一理论不是来自艺术哲学家也不是来自"
2025-05-16 07:54:26.406 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.414 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.421 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.427 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.433 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.440 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.446 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.452 | INFO  | modules.connect:on_check    :271 - client_id:777 - >>> [解析] 第14个数据包, 累计帧数: 552
2025-05-16 07:54:26.682 | INFO  | modules.connect:on_decode   :301 - client_id:777 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:26.682 | INFO  | modules.connect:on_result   :152 - client_id:777 - <<< [发送] 第8个数据包, 更新识别结果: "很难用一个解释 就说清楚 所有这些事物中所存在的美"
2025-05-16 07:54:26.683 | INFO  | asyncio.events:_run        :81 - client_id:777 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:26.683 | INFO  | asyncio.events:_run        :81 - client_id: 777 - 关闭连接，清理资源
2025-05-16 07:54:26.683 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:26.692 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.700 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第14个数据包, 累计帧数: 570
2025-05-16 07:54:26.708 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:26.883 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第8个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那"
2025-05-16 07:54:26.892 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:27.096 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第7个数据包, 更新识别结果: "我想在理性哲学以及心理学的层面上理解美的"
2025-05-16 07:54:27.104 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:27.269 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第8个数据包, 更新识别结果: "哪些感知是能被描述出来的以及人们在尝试理解它的过程"
2025-05-16 07:54:27.279 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:27.469 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第7个数据包, 更新识别结果: "这是个很复杂的问题部分原因是因为我们认为买的是"
2025-05-16 07:54:27.477 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:27.639 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第8个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所做的哈罗"
2025-05-16 07:54:27.647 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:27.847 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第7个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起"
2025-05-16 07:54:27.857 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:28.064 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第7个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的富士山玫瑰"
2025-05-16 07:54:28.072 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:28.325 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第8个数据包, 更新识别结果: "然而我能给大家体会一哈我认为迄今为止我们所"
2025-05-16 07:54:28.332 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第15个数据包, 累计帧数: 608
2025-05-16 07:54:28.520 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第7个数据包, 更新识别结果: "这一理论不是来自艺术哲学家也不是来自后现代艺"
2025-05-16 07:54:28.530 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:28.703 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第9个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美"
2025-05-16 07:54:28.721 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:29.008 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第8个数据包, 更新识别结果: "我想在理性哲学以及心理学的层面上理解美的体验"
2025-05-16 07:54:29.018 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:29.283 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第9个数据包, 更新识别结果: "哪些感知是能被描述出来的以及人们在尝试理解它的过程中"
2025-05-16 07:54:29.293 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:29.499 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第8个数据包, 更新识别结果: "这是个很复杂的问题部分原因是因为我们认为买的是物是"
2025-05-16 07:54:29.508 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:29.721 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第9个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所做的哈罗德"
2025-05-16 07:54:29.749 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:29.933 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第8个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞"
2025-05-16 07:54:29.941 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:30.111 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第8个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的富士山玫瑰骑士"
2025-05-16 07:54:30.122 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:30.368 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第9个数据包, 更新识别结果: "然而我能给大家体会一哈我认为迄今为止我们所有的"
2025-05-16 07:54:30.377 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第16个数据包, 累计帧数: 646
2025-05-16 07:54:30.605 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第8个数据包, 更新识别结果: "这一理论不是来自艺术哲学家也不是来自后现代艺术理论"
2025-05-16 07:54:30.618 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.632 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.642 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.651 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.660 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.669 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.678 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.687 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.696 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第17个数据包, 累计帧数: 684
2025-05-16 07:54:30.698 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:30.698 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:30.698 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:30.709 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:30.968 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:31.205 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第9个数据包, 更新识别结果: "我想在理性哲学以及心理学的层面上理解美的体验究竟是"
2025-05-16 07:54:31.213 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:31.396 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第10个数据包, 更新识别结果: "哪些感知是能被描述出来的以及人们在尝试理解它的过程中犯了什么"
2025-05-16 07:54:31.405 | INFO  | modules.connect:on_check    :271 - client_id:333 - >>> [解析] 第18个数据包, 累计帧数: 714
2025-05-16 07:54:31.709 | INFO  | modules.connect:on_decode   :301 - client_id:333 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:31.709 | INFO  | modules.connect:on_result   :152 - client_id:333 - <<< [发送] 第9个数据包, 更新识别结果: "这是个很复杂的问题 部分原因是因为我们认为 买的是物是如此的不同"
2025-05-16 07:54:31.709 | INFO  | asyncio.events:_run        :81 - client_id:333 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:31.710 | INFO  | asyncio.events:_run        :81 - client_id: 333 - 关闭连接，清理资源
2025-05-16 07:54:31.710 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:31.718 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:31.887 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第10个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所做的哈罗德在意大利"
2025-05-16 07:54:31.898 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:32.133 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:32.327 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:32.497 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第10个数据包, 更新识别结果: "然而我能给大家体会一哈我认为迄今为止我们所有的做有力"
2025-05-16 07:54:32.504 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第18个数据包, 累计帧数: 722
2025-05-16 07:54:32.688 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第9个数据包, 更新识别结果: "这一理论不是来自艺术哲学家也不是来自后现代艺术理论家"
2025-05-16 07:54:32.696 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-05-16 07:54:32.704 | INFO  | modules.connect:on_check    :271 - client_id:111 - >>> [解析] 第19个数据包, 累计帧数: 743
2025-05-16 07:54:32.867 | INFO  | modules.connect:on_decode   :301 - client_id:111 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:32.867 | INFO  | modules.connect:on_result   :152 - client_id:111 - <<< [发送] 第10个数据包, 更新识别结果: "我想在理性 哲学 以及心理学的层面上理解 美的体验究竟是什么"
2025-05-16 07:54:32.867 | INFO  | asyncio.events:_run        :81 - client_id:111 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:32.867 | INFO  | asyncio.events:_run        :81 - client_id: 111 - 关闭连接，清理资源
2025-05-16 07:54:32.867 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:32.876 | INFO  | modules.connect:on_check    :271 - client_id:222 - >>> [解析] 第19个数据包, 累计帧数: 742
2025-05-16 07:54:32.989 | INFO  | modules.connect:on_decode   :301 - client_id:222 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:32.989 | INFO  | modules.connect:on_result   :152 - client_id:222 - <<< [发送] 第11个数据包, 更新识别结果: "哪些感知是能被描述出来的 以及人们在尝试理解它的过程中犯了什么错误"
2025-05-16 07:54:32.989 | INFO  | asyncio.events:_run        :81 - client_id:222 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:32.989 | INFO  | asyncio.events:_run        :81 - client_id: 222 - 关闭连接，清理资源
2025-05-16 07:54:32.989 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:33.008 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-05-16 07:54:33.017 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-05-16 07:54:33.024 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-05-16 07:54:33.031 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-05-16 07:54:33.037 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第19个数据包, 累计帧数: 760
2025-05-16 07:54:33.044 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-05-16 07:54:33.155 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第10个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究"
2025-05-16 07:54:33.163 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-05-16 07:54:33.340 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-05-16 07:54:33.448 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第9个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞这一简单"
2025-05-16 07:54:33.455 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-05-16 07:54:33.591 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第9个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的富士山玫瑰骑士世界杯足"
2025-05-16 07:54:33.599 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-05-16 07:54:33.758 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第11个数据包, 更新识别结果: "然而我能给大家体会一哈我认为迄今为止我们所有的做有力的关"
2025-05-16 07:54:33.766 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第20个数据包, 累计帧数: 798
2025-05-16 07:54:34.021 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第10个数据包, 更新识别结果: "这一理论不是来自艺术哲学家也不是来自后现代艺术理论家或是"
2025-05-16 07:54:34.034 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-05-16 07:54:34.219 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第11个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与"
2025-05-16 07:54:34.228 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-05-16 07:54:34.412 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第11个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所做的哈罗德在意大利像绿野"
2025-05-16 07:54:34.421 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-05-16 07:54:34.643 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第10个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞这一简单的列表"
2025-05-16 07:54:34.650 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-05-16 07:54:34.904 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第10个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的富士山玫瑰骑士世界杯足球赛中"
2025-05-16 07:54:34.914 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-05-16 07:54:35.131 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第12个数据包, 更新识别结果: "然而我能给大家体会一哈我认为迄今为止我们所有的做有力的关于美的理论"
2025-05-16 07:54:35.139 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第21个数据包, 累计帧数: 836
2025-05-16 07:54:35.331 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第11个数据包, 更新识别结果: "这一理论不是来自艺术哲学家也不是来自后现代艺术理论家或是有名的"
2025-05-16 07:54:35.333 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:35.333 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:35.333 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:35.341 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-05-16 07:54:35.352 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-05-16 07:54:35.363 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-05-16 07:54:35.371 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-05-16 07:54:35.381 | INFO  | modules.connect:on_check    :271 - client_id:888 - >>> [解析] 第22个数据包, 累计帧数: 839
2025-05-16 07:54:35.492 | INFO  | modules.connect:on_decode   :301 - client_id:888 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:35.492 | INFO  | modules.connect:on_result   :152 - client_id:888 - <<< [发送] 第13个数据包, 更新识别结果: "然而 我能给大家体会一哈我认为迄今为止 我们所有的做有力的关于美的理论"
2025-05-16 07:54:35.493 | INFO  | asyncio.events:_run        :81 - client_id:888 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:35.493 | INFO  | asyncio.events:_run        :81 - client_id: 888 - 关闭连接，清理资源
2025-05-16 07:54:35.493 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:35.503 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第22个数据包, 累计帧数: 874
2025-05-16 07:54:35.505 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:35.505 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:35.505 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:35.506 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:35.506 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:35.506 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:35.514 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-05-16 07:54:35.691 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第12个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与审美哲学"
2025-05-16 07:54:35.701 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-05-16 07:54:35.924 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第12个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所做的哈罗德在意大利像绿野仙踪"
2025-05-16 07:54:35.932 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-05-16 07:54:36.116 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第11个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞这一简单的列表中"
2025-05-16 07:54:36.123 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-05-16 07:54:36.336 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第11个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的富士山玫瑰骑士世界杯足球赛中的一场漂"
2025-05-16 07:54:36.344 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第23个数据包, 累计帧数: 912
2025-05-16 07:54:36.534 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第12个数据包, 更新识别结果: "这一理论不是来自艺术哲学家也不是来自后现代艺术理论家或是有名的艺术评"
2025-05-16 07:54:36.544 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-05-16 07:54:36.551 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-05-16 07:54:36.562 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-05-16 07:54:36.569 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-05-16 07:54:36.576 | INFO  | modules.connect:on_check    :271 - client_id:999 - >>> [解析] 第24个数据包, 累计帧数: 950
2025-05-16 07:54:36.587 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-05-16 07:54:36.839 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-05-16 07:54:37.019 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第13个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所做的哈罗德在意大利像绿野仙踪这样的影片"
2025-05-16 07:54:37.032 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-05-16 07:54:37.299 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第12个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞这一简单的列表中包含了人"
2025-05-16 07:54:37.308 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第25个数据包, 累计帧数: 988
2025-05-16 07:54:37.485 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第12个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的富士山玫瑰骑士世界杯足球赛中的一场漂亮的决"
2025-05-16 07:54:37.672 | INFO  | modules.connect:on_decode   :301 - client_id:999 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:37.672 | INFO  | modules.connect:on_result   :152 - client_id:999 - <<< [发送] 第13个数据包, 更新识别结果: "这一理论不是来自 艺术哲学家 也不是来自后现代艺术理论家 或是有名的艺术评论家"
2025-05-16 07:54:37.673 | INFO  | asyncio.events:_run        :81 - client_id:999 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:37.673 | INFO  | asyncio.events:_run        :81 - client_id: 999 - 关闭连接，清理资源
2025-05-16 07:54:37.673 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:37.674 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:37.674 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:37.674 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:37.683 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-05-16 07:54:37.692 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-05-16 07:54:37.700 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-05-16 07:54:37.707 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第26个数据包, 累计帧数: 1026
2025-05-16 07:54:37.714 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-05-16 07:54:37.893 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第13个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与审美哲学事实上"
2025-05-16 07:54:37.901 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-05-16 07:54:38.066 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-05-16 07:54:38.258 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第13个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞这一简单的列表中包含了人类自身"
2025-05-16 07:54:38.265 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第27个数据包, 累计帧数: 1064
2025-05-16 07:54:38.431 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第13个数据包, 更新识别结果: "加利福尼亚中部的风景北扎眼中的富士山玫瑰骑士世界杯足球赛中的一场漂亮的决胜赛"
2025-05-16 07:54:38.439 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-05-16 07:54:38.620 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第14个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与审美哲学事实上这"
2025-05-16 07:54:38.628 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-05-16 07:54:38.816 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第14个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所做的哈罗德在意大利像绿野仙踪这样的影片或是"
2025-05-16 07:54:38.824 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-05-16 07:54:39.003 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第28个数据包, 累计帧数: 1102
2025-05-16 07:54:39.216 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-05-16 07:54:39.232 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-05-16 07:54:39.240 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-05-16 07:54:39.248 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第29个数据包, 累计帧数: 1140
2025-05-16 07:54:39.249 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:39.249 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:39.249 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:39.256 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-05-16 07:54:39.382 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第15个数据包, 更新识别结果: "很高兴来到这里与大家探讨我这为关心的一个主题那就是美我研究艺术与审美哲学事实上这是我的"
2025-05-16 07:54:39.390 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-05-16 07:54:39.567 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第15个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋柏撩兹所做的哈罗德在意大利像绿野仙踪这样的影片或是契诃夫的"
2025-05-16 07:54:39.575 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-05-16 07:54:39.694 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第14个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞这一简单的列表中包含了人类自身自然景观"
2025-05-16 07:54:39.709 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第30个数据包, 累计帧数: 1178
2025-05-16 07:54:39.836 | INFO  | modules.connect:on_check    :271 - client_id:000 - >>> [解析] 第31个数据包, 累计帧数: 1181
2025-05-16 07:54:39.927 | INFO  | modules.connect:on_decode   :301 - client_id:000 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:39.927 | INFO  | modules.connect:on_result   :152 - client_id:000 - <<< [发送] 第16个数据包, 更新识别结果: "很高兴来到这里 与大家探讨我这为关心的一个主题 那就是美 我研究艺术与审美哲学 事实上 这是我的工作"
2025-05-16 07:54:39.927 | INFO  | asyncio.events:_run        :81 - client_id:000 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:39.927 | INFO  | asyncio.events:_run        :81 - client_id: 000 - 关闭连接，清理资源
2025-05-16 07:54:39.927 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:39.934 | INFO  | modules.connect:on_check    :271 - client_id:444 - >>> [解析] 第31个数据包, 累计帧数: 1191
2025-05-16 07:54:40.092 | INFO  | modules.connect:on_decode   :301 - client_id:444 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:40.092 | INFO  | modules.connect:on_result   :152 - client_id:444 - <<< [发送] 第16个数据包, 更新识别结果: "想象一下这高度的多元性婴儿的脸蛋 柏撩兹所做的哈罗德在意大利 像绿野仙踪这样的影片 或是契诃夫的戏剧"
2025-05-16 07:54:40.092 | INFO  | asyncio.events:_run        :81 - client_id:444 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:40.092 | INFO  | asyncio.events:_run        :81 - client_id: 444 - 关闭连接，清理资源
2025-05-16 07:54:40.092 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:40.101 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第31个数据包, 累计帧数: 1216
2025-05-16 07:54:40.107 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第31个数据包, 累计帧数: 1216
2025-05-16 07:54:40.115 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第32个数据包, 累计帧数: 1254
2025-05-16 07:54:40.231 | INFO  | modules.connect:on_check    :271 - client_id:555 - >>> [解析] 第32个数据包, 累计帧数: 1251
2025-05-16 07:54:40.511 | INFO  | modules.connect:on_decode   :301 - client_id:555 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:40.511 | INFO  | modules.connect:on_result   :152 - client_id:555 - <<< [发送] 第14个数据包, 更新识别结果: "加利福尼亚中部的风景 北扎眼中的富士山 玫瑰骑士 世界杯足球赛中的一场漂亮的决胜赛 梵高的星空"
2025-05-16 07:54:40.511 | INFO  | asyncio.events:_run        :81 - client_id:555 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:40.511 | INFO  | asyncio.events:_run        :81 - client_id: 555 - 关闭连接，清理资源
2025-05-16 07:54:40.512 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:40.521 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第33个数据包, 累计帧数: 1292
2025-05-16 07:54:40.645 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第15个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞这一简单的列表中包含了人类自身自然景观艺术作品"
2025-05-16 07:54:40.657 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第34个数据包, 累计帧数: 1330
2025-05-16 07:54:40.665 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第35个数据包, 累计帧数: 1368
2025-05-16 07:54:40.817 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第16个数据包, 更新识别结果: "一部简奥斯丁的小说弗雷德阿斯泰尔在舞台上起舞这一简单的列表中包含了人类自身自然景观艺术作品和"
2025-05-16 07:54:40.818 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:40.818 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:40.818 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:40.819 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:40.819 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:40.819 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:40.828 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第36个数据包, 累计帧数: 1406
2025-05-16 07:54:40.829 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:40.830 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:40.830 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:40.838 | INFO  | modules.connect:on_check    :271 - client_id:666 - >>> [解析] 第37个数据包, 累计帧数: 1421
2025-05-16 07:54:41.032 | INFO  | modules.connect:on_decode   :301 - client_id:666 - *** 最后一个数据包完成解码 ***
2025-05-16 07:54:41.032 | INFO  | modules.connect:on_result   :152 - client_id:666 - <<< [发送] 第17个数据包, 更新识别结果: "一部简奥斯丁的小说 弗雷德阿斯泰尔 在舞台上起舞 这一简单的列表中 包含了人类自身 自然景观 艺术作品 和专业活动"
2025-05-16 07:54:41.033 | INFO  | asyncio.events:_run        :81 - client_id:666 - 已发送最后一个识别结果, 主动关闭客户连接
2025-05-16 07:54:41.033 | INFO  | asyncio.events:_run        :81 - client_id: 666 - 关闭连接，清理资源
2025-05-16 07:54:41.033 | INFO  | modules.connect:disconnect  :82 - 关闭 ws 连接
2025-05-16 07:54:41.035 | INFO  | modules.decoder:__del__     :404 - ASRDecoder 显式释放资源
2025-05-16 07:54:41.035 | INFO  | modules.decoder:__del__     :149 - Encoder 显式释放资源
2025-05-16 07:54:41.036 | INFO  | modules.decoder:__del__     :268 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 07:54:51.767 | INFO  | contextlib:__aexit__   :178 - Sever shutdown, delete FEAT_PIPE, manager, SYMBOL_TABLE
