from typing import Dict, List
from utils.lat2ug import Lat2ug
from modules.logger import logger
import os

class SymbolTable:
    def __init__(self, onnx_dir, dict_path, lang_code):
        self.dict = self.load_dict(dict_path)  # int: str
        self.char2id_dict = {v: k for k, v in self.dict.items()}  # str: int
        self.lang = lang_code
        if lang_code in ['ug', 'kkin']:
            map_path = os.path.join(onnx_dir, f"map_{self.lang}2lat.txt")
            assert os.path.exists(map_path)
            self.L2U = Lat2ug(map_path)
        else:
            self.L2U = None

    def load_dict(self, path: str) -> Dict:
        logger.info(f"加载词表: {path}")
        char_dict = {}
        with open(path, 'r') as fin:
            for line in fin:
                arr = line.strip().split()
                assert len(arr) == 2
                char_dict[int(arr[1])] = arr[0]
        return char_dict


    def ids2tokens(self, ids: List[int]) -> List[str]:
        content = [self.dict[w] for w in ids]
        return content

    def char_map(self, text: str):
        # logger.info(f"raw_text: {text}")
        if self.lang in ['en', 'ru']:
            text = text.replace('▁', ' ').replace('  ', ' ').strip()
        if self.lang in ['ug', 'kkin']:
            text = self.L2U.lat2ug(text)
        return text


