/**
 * WebSocket客户端
 * 实现三阶段连接协议
 */

class WebSocketClient {
    constructor() {
        this.websocket = null;
        this.sessionId = null;
        this.clientId = this.generateClientId();
        this.connectionState = 'disconnected';
        this.sequenceId = 0;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 3;
        this.heartbeatInterval = null;
        
        // 事件回调
        this.onConnectionStateChange = null;
        this.onRecognitionResult = null;
        this.onError = null;
        this.onSessionInfo = null;
        this.onStatusUpdate = null;
    }
    
    generateClientId() {
        return 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    async connect(config = {}) {
        try {
            this.updateConnectionState('connecting');
            
            // 构建WebSocket URL
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const wsUrl = `${protocol}//${host}/ws/stream`;
            
            // 创建WebSocket连接
            this.websocket = new WebSocket(wsUrl);
            
            // 设置事件处理器
            this.websocket.onopen = () => this.handleOpen(config);
            this.websocket.onmessage = (event) => this.handleMessage(event);
            this.websocket.onclose = (event) => this.handleClose(event);
            this.websocket.onerror = (error) => this.handleError(error);
            
            return new Promise((resolve, reject) => {
                this.connectResolve = resolve;
                this.connectReject = reject;
                
                // 连接超时
                setTimeout(() => {
                    if (this.connectionState === 'connecting') {
                        this.connectReject(new Error('Connection timeout'));
                    }
                }, 10000);
            });
            
        } catch (error) {
            this.updateConnectionState('disconnected');
            throw error;
        }
    }
    
    async handleOpen(config) {
        try {
            this.updateConnectionState('handshaking');
            
            // 发送握手请求
            const handshakeRequest = {
                type: 'handshake_request',
                client_id: this.clientId,
                language: config.language || null,
                auto_language_detection: config.autoLanguageDetection !== false,
                sample_rate: config.sampleRate || 16000,
                channels: config.channels || 1,
                sample_width: config.sampleWidth || 2,
                chunk_duration: config.chunkDuration || 0.4,
                enable_intermediate_result: config.enableIntermediate !== false,
                enable_punctuation: config.enablePunctuation !== false,
                enable_itn: config.enableITN !== false,
                custom_separator: config.customSeparator || null,
                hotwords: config.hotwords || null,
                client_info: {
                    user_agent: navigator.userAgent,
                    timestamp: Date.now()
                }
            };
            
            this.send(handshakeRequest);
            
        } catch (error) {
            this.handleError(error);
        }
    }
    
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            
            switch (message.type) {
                case 'handshake_response':
                    this.handleHandshakeResponse(message);
                    break;
                case 'recognition_result':
                case 'intermediate_result':
                    this.handleRecognitionResult(message);
                    break;
                case 'status_update':
                    this.handleStatusUpdate(message);
                    break;
                case 'session_info':
                    this.handleSessionInfo(message);
                    break;
                case 'error':
                    this.handleErrorMessage(message);
                    break;
                case 'heartbeat':
                    this.handleHeartbeat(message);
                    break;
                case 'disconnect_response':
                    this.handleDisconnectResponse(message);
                    break;
                default:
                    console.warn('Unknown message type:', message.type);
            }
            
        } catch (error) {
            console.error('Message parsing error:', error);
            this.handleError(error);
        }
    }
    
    handleHandshakeResponse(message) {
        if (message.status === 'success') {
            this.sessionId = message.session_id;
            this.updateConnectionState('connected');
            this.startHeartbeat();
            
            if (this.connectResolve) {
                this.connectResolve({
                    sessionId: this.sessionId,
                    serverConfig: message.server_config
                });
                this.connectResolve = null;
            }
            
            this.log('info', `Handshake successful. Session ID: ${this.sessionId}`);
        } else {
            const error = new Error(message.message || 'Handshake failed');
            error.code = message.error_code;
            this.handleError(error);
        }
    }
    
    handleRecognitionResult(message) {
        if (this.onRecognitionResult) {
            this.onRecognitionResult({
                text: message.text,
                isFinal: message.is_final,
                confidence: message.confidence,
                language: message.language,
                segments: message.segments,
                processingTime: message.processing_time,
                timestamp: message.timestamp,
                sequenceId: message.sequence_id
            });
        }
    }
    
    handleStatusUpdate(message) {
        this.log('info', `Status update: ${message.state} - ${message.message}`);
        
        if (this.onStatusUpdate) {
            this.onStatusUpdate({
                state: message.state,
                message: message.message,
                details: message.details
            });
        }
    }
    
    handleSessionInfo(message) {
        if (this.onSessionInfo) {
            this.onSessionInfo(message);
        }
    }
    
    handleErrorMessage(message) {
        const error = new Error(message.message);
        error.code = message.error_code;
        error.type = message.error_type;
        error.details = message.details;
        
        this.log('error', `Server error: ${message.message} (Code: ${message.error_code})`);
        this.handleError(error);
    }
    
    handleHeartbeat(message) {
        // 心跳响应，保持连接活跃
        this.log('debug', 'Heartbeat received');
    }
    
    handleDisconnectResponse(message) {
        this.log('info', 'Disconnect acknowledged by server');
        this.updateConnectionState('disconnecting');
    }
    
    handleClose(event) {
        this.updateConnectionState('disconnected');
        this.stopHeartbeat();
        
        this.log('info', `WebSocket closed: ${event.code} - ${event.reason}`);
        
        // 尝试重连（如果不是主动断开）
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            this.log('info', `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                if (this.connectionState === 'disconnected') {
                    this.connect(this.lastConfig);
                }
            }, 2000 * this.reconnectAttempts);
        }
    }
    
    handleError(error) {
        this.log('error', `WebSocket error: ${error.message}`);
        
        if (this.onError) {
            this.onError(error);
        }
        
        if (this.connectReject) {
            this.connectReject(error);
            this.connectReject = null;
        }
    }
    
    sendAudioData(audioData, isFinal = false) {
        if (this.connectionState !== 'connected') {
            throw new Error('WebSocket not connected');
        }
        
        const message = {
            type: 'audio_data',
            session_id: this.sessionId,
            sequence_id: this.sequenceId++,
            audio_data: audioData, // base64 encoded
            is_final: isFinal,
            timestamp: Date.now()
        };
        
        this.send(message);
    }
    
    startRecording() {
        if (this.connectionState !== 'connected') {
            throw new Error('WebSocket not connected');
        }
        
        const message = {
            type: 'start_recording',
            session_id: this.sessionId,
            timestamp: Date.now()
        };
        
        this.send(message);
    }
    
    stopRecording() {
        if (this.connectionState !== 'connected') {
            throw new Error('WebSocket not connected');
        }
        
        const message = {
            type: 'stop_recording',
            session_id: this.sessionId,
            timestamp: Date.now()
        };
        
        this.send(message);
    }
    
    disconnect() {
        if (this.websocket && this.connectionState === 'connected') {
            const message = {
                type: 'disconnect_request',
                session_id: this.sessionId,
                timestamp: Date.now()
            };
            
            this.send(message);
            
            // 等待服务器响应后关闭
            setTimeout(() => {
                if (this.websocket) {
                    this.websocket.close(1000, 'Client disconnect');
                }
            }, 1000);
        } else if (this.websocket) {
            this.websocket.close(1000, 'Client disconnect');
        }
    }
    
    send(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        } else {
            throw new Error('WebSocket not ready');
        }
    }
    
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatInterval = setInterval(() => {
            if (this.connectionState === 'connected') {
                try {
                    const message = {
                        type: 'heartbeat',
                        session_id: this.sessionId,
                        timestamp: Date.now()
                    };
                    this.send(message);
                } catch (error) {
                    this.log('error', 'Heartbeat failed: ' + error.message);
                }
            }
        }, 30000); // 30秒心跳
    }
    
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    updateConnectionState(newState) {
        if (this.connectionState !== newState) {
            this.connectionState = newState;
            
            if (this.onConnectionStateChange) {
                this.onConnectionStateChange(newState);
            }
        }
    }
    
    log(level, message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
        
        // 触发日志事件（如果有监听器）
        if (this.onLog) {
            this.onLog(level, message, timestamp);
        }
    }
    
    getConnectionState() {
        return this.connectionState;
    }
    
    getSessionId() {
        return this.sessionId;
    }
}
