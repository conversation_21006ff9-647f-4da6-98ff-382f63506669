"""
配置验证器测试
"""

import pytest
import tempfile
import os
from pathlib import Path
from utils.config.validator import ConfigValidator


class TestConfigValidator:
    """测试配置验证器"""
    
    def test_validate_server_config_valid(self):
        """测试有效的服务器配置"""
        config = {
            "server": {
                "port": 8080,
                "host": "0.0.0.0",
                "workers": 1
            },
            "audio": {
                "sample_rate": 16000,
                "channels": 1,
                "chunk_duration": 0.4,
                "max_audio_duration": 60
            },
            "websocket": {
                "heartbeat_interval": 30,
                "max_connections": 100,
                "connection_timeout": 300
            }
        }
        
        errors = ConfigValidator.validate_server_config(config)
        assert len(errors) == 0
    
    def test_validate_server_config_invalid_port(self):
        """测试无效端口配置"""
        config = {
            "server": {
                "port": 80  # 端口太小
            }
        }
        
        errors = ConfigValidator.validate_server_config(config)
        assert len(errors) > 0
        assert any("port" in error.lower() for error in errors)
    
    def test_validate_server_config_invalid_sample_rate(self):
        """测试无效采样率配置"""
        config = {
            "audio": {
                "sample_rate": 12000  # 不支持的采样率
            }
        }
        
        errors = ConfigValidator.validate_server_config(config)
        assert len(errors) > 0
        assert any("sample rate" in error.lower() for error in errors)
    
    def test_validate_language_config_valid(self):
        """测试有效的语种配置"""
        # 创建临时模型文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.onnx') as f:
            model_path = f.name
        
        try:
            config = {
                "name": "Chinese",
                "separator": "，。！？；：",
                "silence_threshold": 0.35,
                "model": {
                    "model_path": model_path,
                    "vocabulary_path": "",
                    "hotwords_path": ""
                },
                "features": {
                    "enable_punctuation": True,
                    "enable_itn": False,
                    "enable_hotwords": False
                }
            }
            
            errors = ConfigValidator.validate_language_config("zh", config)
            assert len(errors) == 0
        finally:
            os.unlink(model_path)
    
    def test_validate_language_config_missing_model(self):
        """测试缺少模型文件的语种配置"""
        config = {
            "name": "Chinese",
            "separator": "，。！？；：",
            "model": {
                "model_path": "/nonexistent/path/model.onnx"
            }
        }
        
        errors = ConfigValidator.validate_language_config("zh", config)
        assert len(errors) > 0
        assert any("not found" in error.lower() for error in errors)
    
    def test_validate_lid_config_valid(self):
        """测试有效的LID配置"""
        # 创建临时模型文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.onnx') as f:
            model_path = f.name
        
        try:
            config = {
                "enable": True,
                "model": {
                    "model_path": model_path
                },
                "confidence_threshold": 0.8,
                "min_audio_duration": 0.4,
                "max_audio_duration": 2.4,
                "progressive_steps": [0.4, 0.8, 2.4],
                "language_mapping": {
                    0: "zh",
                    1: "en",
                    2: "ru"
                }
            }
            
            errors = ConfigValidator.validate_lid_config(config)
            assert len(errors) == 0
        finally:
            os.unlink(model_path)
    
    def test_validate_lid_config_invalid_threshold(self):
        """测试无效置信度阈值的LID配置"""
        config = {
            "enable": True,
            "confidence_threshold": 1.5  # 超出范围
        }
        
        errors = ConfigValidator.validate_lid_config(config)
        assert len(errors) > 0
        assert any("confidence_threshold" in error for error in errors)
    
    def test_validate_lid_config_invalid_progressive_steps(self):
        """测试无效渐进式步骤的LID配置"""
        config = {
            "enable": True,
            "progressive_steps": [2.4, 0.8, 0.4]  # 非递增顺序
        }
        
        errors = ConfigValidator.validate_lid_config(config)
        assert len(errors) > 0
        assert any("ascending order" in error for error in errors)
    
    def test_validate_vad_config_valid(self):
        """测试有效的VAD配置"""
        config = {
            "enable": True,
            "vad_type": "webrtcvad",
            "vad_level": 1,
            "frame_length": 30,
            "window_size": 10,
            "seg_threshold": 0.9
        }
        
        errors = ConfigValidator.validate_vad_config(config)
        assert len(errors) == 0
    
    def test_validate_vad_config_invalid_type(self):
        """测试无效VAD类型配置"""
        config = {
            "enable": True,
            "vad_type": "invalid_type"
        }
        
        errors = ConfigValidator.validate_vad_config(config)
        assert len(errors) > 0
        assert any("vad_type" in error for error in errors)
    
    def test_validate_all_configs(self):
        """测试验证所有配置"""
        server_config = {
            "server": {"port": 8080},
            "audio": {"sample_rate": 16000},
            "websocket": {"max_connections": 100},
            "vad": {"enable": True, "vad_type": "webrtcvad"}
        }
        
        language_configs = {
            "zh": {
                "name": "Chinese",
                "separator": "，。",
                "model": {"model_path": "/nonexistent/path"}
            }
        }
        
        lid_config = {
            "enable": True,
            "confidence_threshold": 0.8
        }
        
        results = ConfigValidator.validate_all_configs(
            server_config, language_configs, lid_config
        )
        
        assert "server" in results
        assert "languages" in results
        assert "lid" in results
        assert "vad" in results
        
        # 应该有一些错误（因为模型路径不存在）
        assert ConfigValidator.has_errors(results)
    
    def test_format_validation_errors(self):
        """测试格式化验证错误"""
        validation_results = {
            "server": ["Server error 1", "Server error 2"],
            "languages": {
                "zh": ["Language error 1"],
                "en": []
            },
            "lid": [],
            "vad": ["VAD error 1"]
        }
        
        formatted = ConfigValidator.format_validation_errors(validation_results)
        
        assert "Server configuration errors:" in formatted
        assert "Language 'zh' configuration errors:" in formatted
        assert "Vad configuration errors:" in formatted
        assert "Language 'en'" not in formatted  # 没有错误的不应该出现


if __name__ == "__main__":
    pytest.main([__file__])
