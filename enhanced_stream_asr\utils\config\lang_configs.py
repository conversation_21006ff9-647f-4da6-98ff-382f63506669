"""
语种配置管理
定义各语种的特定配置参数
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class LanguageConfig:
    """语种配置数据类"""
    
    code: str                           # 语种代码
    name: str                           # 语种名称
    separator: str                      # 分隔符
    silence_threshold: float            # 静音阈值（秒）
    model_path: str                     # 模型路径
    vocabulary_path: str                # 词汇表路径
    hotwords_path: Optional[str] = None # 热词文件路径
    enable_punctuation: bool = True     # 是否启用标点符号
    enable_itn: bool = True             # 是否启用逆文本标准化
    chunk_size: int = 16                # 解码块大小
    left_chunks: int = 16               # 左侧上下文块数
    decoding_window: int = 67           # 解码窗口大小
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'code': self.code,
            'name': self.name,
            'separator': self.separator,
            'silence_threshold': self.silence_threshold,
            'model': {
                'model_path': self.model_path,
                'vocabulary_path': self.vocabulary_path,
                'hotwords_path': self.hotwords_path,
                'chunk_size': self.chunk_size,
                'left_chunks': self.left_chunks,
                'decoding_window': self.decoding_window
            },
            'features': {
                'enable_punctuation': self.enable_punctuation,
                'enable_itn': self.enable_itn
            }
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LanguageConfig':
        """从字典创建实例"""
        model_config = data.get('model', {})
        features_config = data.get('features', {})
        
        return cls(
            code=data['code'],
            name=data['name'],
            separator=data['separator'],
            silence_threshold=data['silence_threshold'],
            model_path=model_config.get('model_path', ''),
            vocabulary_path=model_config.get('vocabulary_path', ''),
            hotwords_path=model_config.get('hotwords_path'),
            enable_punctuation=features_config.get('enable_punctuation', True),
            enable_itn=features_config.get('enable_itn', True),
            chunk_size=model_config.get('chunk_size', 16),
            left_chunks=model_config.get('left_chunks', 16),
            decoding_window=model_config.get('decoding_window', 67)
        )


class LanguageConfigManager:
    """语种配置管理器"""
    
    def __init__(self):
        """初始化语种配置管理器"""
        self.configs: Dict[str, LanguageConfig] = {}
        self.load_default_configs()
        
    def load_default_configs(self):
        """加载默认语种配置"""
        default_configs = [
            # 中文配置
            LanguageConfig(
                code='zh',
                name='中文',
                separator='，',
                silence_threshold=0.35,
                model_path='models/zh/encoder.onnx',
                vocabulary_path='models/zh/units.txt',
                hotwords_path='models/zh/hotwords.txt',
                enable_punctuation=True,
                enable_itn=True
            ),
            
            # 英文配置
            LanguageConfig(
                code='en',
                name='English',
                separator=', ',
                silence_threshold=0.4,
                model_path='models/en/encoder.onnx',
                vocabulary_path='models/en/units.txt',
                enable_punctuation=True,
                enable_itn=True
            ),
            
            # 俄语配置
            LanguageConfig(
                code='ru',
                name='Русский',
                separator=', ',
                silence_threshold=0.4,
                model_path='models/ru/encoder.onnx',
                vocabulary_path='models/ru/units.txt',
                enable_punctuation=True,
                enable_itn=False
            ),
            
            # 维语配置
            LanguageConfig(
                code='ug',
                name='ئۇيغۇرچە',
                separator='، ',
                silence_threshold=0.35,
                model_path='models/ug/encoder.onnx',
                vocabulary_path='models/ug/units.txt',
                enable_punctuation=False,
                enable_itn=False
            ),
            
            # 哈萨克语配置
            LanguageConfig(
                code='kk',
                name='Қазақша',
                separator=', ',
                silence_threshold=0.4,
                model_path='models/kk/encoder.onnx',
                vocabulary_path='models/kk/units.txt',
                enable_punctuation=False,
                enable_itn=False
            )
        ]
        
        for config in default_configs:
            self.configs[config.code] = config
            
        logger.info(f"Loaded {len(default_configs)} default language configurations")
        
    def get_config(self, lang_code: str) -> Optional[LanguageConfig]:
        """
        获取语种配置
        
        Args:
            lang_code: 语种代码
            
        Returns:
            语种配置对象，如果不存在返回None
        """
        return self.configs.get(lang_code)
        
    def add_config(self, config: LanguageConfig):
        """
        添加语种配置
        
        Args:
            config: 语种配置对象
        """
        self.configs[config.code] = config
        logger.info(f"Added language configuration: {config.code}")
        
    def remove_config(self, lang_code: str) -> bool:
        """
        移除语种配置
        
        Args:
            lang_code: 语种代码
            
        Returns:
            是否成功移除
        """
        if lang_code in self.configs:
            del self.configs[lang_code]
            logger.info(f"Removed language configuration: {lang_code}")
            return True
        return False
        
    def get_supported_languages(self) -> list:
        """获取支持的语种列表"""
        return list(self.configs.keys())
        
    def is_supported(self, lang_code: str) -> bool:
        """检查语种是否支持"""
        return lang_code in self.configs
        
    def get_separator(self, lang_code: str, custom_separator: Optional[str] = None) -> str:
        """
        获取语种分隔符
        
        Args:
            lang_code: 语种代码
            custom_separator: 自定义分隔符（优先级最高）
            
        Returns:
            分隔符字符串
        """
        # 优先使用自定义分隔符
        if custom_separator:
            return custom_separator
            
        # 从配置中获取
        config = self.get_config(lang_code)
        if config:
            return config.separator
            
        # 默认分隔符
        return ', '
        
    def get_silence_threshold(self, lang_code: str) -> float:
        """
        获取语种静音阈值
        
        Args:
            lang_code: 语种代码
            
        Returns:
            静音阈值（秒）
        """
        config = self.get_config(lang_code)
        return config.silence_threshold if config else 0.35
        
    def update_config(self, lang_code: str, updates: Dict[str, Any]) -> bool:
        """
        更新语种配置
        
        Args:
            lang_code: 语种代码
            updates: 更新的配置项
            
        Returns:
            是否成功更新
        """
        config = self.get_config(lang_code)
        if not config:
            return False
            
        # 更新配置项
        for key, value in updates.items():
            if hasattr(config, key):
                setattr(config, key, value)
                
        logger.info(f"Updated language configuration: {lang_code}")
        return True
        
    def get_all_configs(self) -> Dict[str, LanguageConfig]:
        """获取所有语种配置"""
        return self.configs.copy()
        
    def export_configs(self) -> Dict[str, Dict[str, Any]]:
        """导出所有配置为字典格式"""
        return {
            code: config.to_dict() 
            for code, config in self.configs.items()
        }
        
    def import_configs(self, configs_data: Dict[str, Dict[str, Any]]):
        """
        导入配置数据
        
        Args:
            configs_data: 配置数据字典
        """
        for code, config_data in configs_data.items():
            try:
                config = LanguageConfig.from_dict(config_data)
                self.configs[code] = config
                logger.info(f"Imported language configuration: {code}")
            except Exception as e:
                logger.error(f"Failed to import config for {code}: {e}")
                
    def validate_config(self, config: LanguageConfig) -> tuple[bool, Optional[str]]:
        """
        验证语种配置
        
        Args:
            config: 语种配置对象
            
        Returns:
            (是否有效, 错误消息)
        """
        if not config.code:
            return False, "Language code is required"
            
        if not config.name:
            return False, "Language name is required"
            
        if not config.model_path:
            return False, "Model path is required"
            
        if not config.vocabulary_path:
            return False, "Vocabulary path is required"
            
        if config.silence_threshold <= 0:
            return False, "Silence threshold must be positive"
            
        return True, None
