#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  client.py
# Time    :  2025/04/03 15:12:17
# Author  :  lh
# Version :  1.0
# Description:  用于测试 server.py 收发消息、流速控制
import base64
import asyncio
import websockets
import time
import os
import json
from pydub import AudioSegment
from datetime import datetime
from glob import glob
from jiwer import cer, wer
import sys
import soundfile as sf

# 全局配置参数：
# 服务端解析二进制音频流所必须的参数（已经约定固定参数, 不可更改）
EXPECTED_SAMPLE_RATE = 16000  # 期望的采样率
EXPECTED_SAMPLE_WIDTH = 2     # 期望的采样位数（16bit=2字节） 16位音频：每个样本占用2个字节, 24位音频：每个样本占用3个字节, 32位音频：每个样本占用4个字节
EXPECTED_SAMPLE_CHANNELS = 1         # 期望的声道数（单声道）
PACKET_INTERVAL = 0.4   # 数据包发送间隔，模拟录制音频流所用时间

# 客户端参数：
PING_INTERVAL = 60  # 设置心跳包间隔（60秒）
PING_TIMEOUT = 30    # 心跳包超时时间（30秒）

def get_current_time():
    # 获取当前时间（精确到微秒）
    now = datetime.now()
    return now.strftime("%Y-%m-%d %H:%M:%S.%f")

def read_pcm(audio_file):
    # 从文件读取并处理音频文件, 可根据实际，从麦克风读取音频流等
    audio_segment = AudioSegment.from_file(audio_file)
    
    # 转换为 PCM 数据，并标准化
    if audio_segment.channels != EXPECTED_SAMPLE_CHANNELS:
        audio_segment = audio_segment.set_channels(EXPECTED_SAMPLE_CHANNELS)
    if audio_segment.frame_rate != EXPECTED_SAMPLE_RATE:
        audio_segment = audio_segment.set_frame_rate(EXPECTED_SAMPLE_RATE)
    if audio_segment.sample_width != EXPECTED_SAMPLE_WIDTH:
        audio_segment = audio_segment.set_sample_width(EXPECTED_SAMPLE_WIDTH)
    
    # 获取音频参数
    sample_rate = audio_segment.frame_rate
    sample_width = audio_segment.sample_width # 每个音频样本在文件中占用的字节数
    channels = audio_segment.channels

    # 验证音频参数
    if (sample_rate != EXPECTED_SAMPLE_RATE or 
        sample_width != EXPECTED_SAMPLE_WIDTH or 
        channels != EXPECTED_SAMPLE_CHANNELS):
        raise ValueError(f"音频参数不匹配: "
                            f"采样率({sample_rate}), "
                            f"位深({sample_width}), "
                            f"声道({channels})")
    
    # 获取 PCM 数据
    pcm_data = audio_segment.raw_data

    # 计算数据包参数
    samples_per_packet = int(EXPECTED_SAMPLE_RATE * 0.4)        # 要求发送16000采样率下的400ms数据， 即 16000 * 0.4 = 6400 个样本点
    bytes_per_sample = sample_width * channels                  # 每个样本点的位宽 * 通道数 = 每个样本的字节数，即 2 * 1 = 2 字节 
    bytes_per_packet = samples_per_packet * bytes_per_sample    # 需要发送的样本数 * 每个样本的字节数 = 每个数据包的字节数，即 6400 * 2 = 12800
        
    return pcm_data, sample_rate, bytes_per_packet


async def send_audio(params):
    voice_id = params[0]
    url = params[1]
    audio_file = params[2]
    duration = params[3]
    ref = params[4]
    async with websockets.connect(url,
                                ping_interval=PING_INTERVAL,  # 设置心跳包间隔（60秒）
                                ping_timeout=PING_TIMEOUT    # 心跳包超时时间（30秒）
                                ) as websocket:
        print(f"{voice_id} | Connected to {url}")
        
        # 读取pcm数据流
        pcm_data , sample_rate, bytes_per_packet = read_pcm(audio_file)
        
        # 分包处理
        packets = [pcm_data[i:i+bytes_per_packet] for i in range(0, len(pcm_data), bytes_per_packet)]
        packets_num = len(packets)

        # 启动异步任务接收服务端消息
        receive_task = asyncio.create_task(receive_messages(websocket, voice_id, duration, packets_num, ref, audio_file))
        
        # 发送数据包
        send_start_time = time.time()
        for idx, packet in enumerate(packets):
           
            # 将 PCM 数据编码为 Base64
            audio_base64 = base64.b64encode(packet).decode('utf-8')

            # 构建 JSON 请求体
            request_data = {
                "index": idx,                 # 不可省略
                "audio_data": audio_base64,   # 不可省略 
                "sample_rate": sample_rate,   # 不可省略， 服务端解析二进制音频流所必须的参数（已经约定固定参数，保险起见还是传送一下）
                "is_final": True if idx+1 == len(packets) else False   # 不可省略
            }

            # 发送请求
            await websocket.send(json.dumps(request_data))
            print(f"{get_current_time()} | voice_id: {voice_id} | Sent packet {idx+1}/{len(packets)} size={len(packet)}")
            
            # 控制发送速率（400ms间隔）模拟音频时间
            await asyncio.sleep(PACKET_INTERVAL)

        print(f"{get_current_time()} | voice_id: {voice_id} | All {len(packets)} packets sent! cost time: {time.time()-send_start_time:.2f} s")

        # 阻塞 直到协程 receive_task 完成 （收到最后一个响应包，或者被意外关闭了连接）
        await receive_task
        
        
        # 打印信息
        print(receive_task._result)
        cost = (time.time() - send_start_time) - (packets_num * PACKET_INTERVAL)    # 处理时长 = 接收最后一个消息的时间 - 发送第一个消息的时间 - 模拟的音频录制时间
        print(f"{get_current_time()} | voice_id: {voice_id} | 模拟音频流：{audio_file}, 处理时长: {cost:.2f} s, 音频时长: {duration:.2f} s, RTF: { (cost / duration):.4f}")
        print()


async def receive_messages(websocket, voice_id, duration, packets_num, ref, audio_file):
    try:
        async for message in websocket:
            response = json.loads(message)
           
            if 'index' in response:
                print(f"{get_current_time()} | voice_id: {voice_id} | Received: index {response['index']} transcript \"{response['result']}\"")
            else:
                print(f"{get_current_time()} | voice_id: {voice_id} | Received: ", response)
           
            # 检测最终响应
            if response.get("final") == 1:   # 退出情况1：收到最后一个响应包
                with open(audio_file.replace('.wav', '.hyp.txt'), 'r', encoding='utf-8')as fr:
                    hyp_batch_onnx = fr.read().strip()

                hyp = response['result']
                if LANG in ['en', 'ru']:
                    scores = wer(ref, hyp)
                    scores = f"wer: {scores:.2f}"
                else:
                    scores = cer(ref, hyp, return_dict=True)
                    scores = f"cer: {scores['cer']:.2f} | detail: {scores}"   

                info = f"{get_current_time()} | voice_id: {voice_id} | Received final response, closing connection...\n"
                info += f"{get_current_time()} | voice_id: {voice_id} | 最终结果: \"{response['result']}\"\n"
                info += f"{get_current_time()} | voice_id: {voice_id} | 参考答案: \"{ref}\"\n"
                info += f"{get_current_time()} | voice_id: {voice_id} | {scores}"
                return info
                
    except websockets.exceptions.ConnectionClosed:   # 退出情况2： 被意外关闭了连接
        return f"{voice_id} | Connection closed by server"


def read_data_list(wav_dir):
    key_wavs = {}
    for wav in glob(f"{wav_dir}/*wav"):
        wav = os.path.abspath(wav)
        key = os.path.basename(wav).replace('.wav', '')
        key_wavs[key] = wav

    data_list = {}
    with open(f"{wav_dir}/text", 'r', encoding='utf-8')as fr:
        for line in fr.readlines():
            items = line.strip().split(' ')
            key = items[0]
            ref = ' '.join(items[1:])

            if key in key_wavs:
                wav = key_wavs[key]
                data_list[wav] = ref
    return data_list



if __name__ == "__main__":
    LANG = sys.argv[1]
    PORT = sys.argv[2]

    print(f"Expected parameters: {EXPECTED_SAMPLE_RATE}Hz, "
          f"{EXPECTED_SAMPLE_WIDTH*8}bit, "
          f"{'Mono' if EXPECTED_SAMPLE_CHANNELS == 1 else 'Stereo'}")
    print("每次发送数据包音频时长0.4s, 发送间隔0.4s, 模拟麦克风实时录制")

    data_list = read_data_list(f"/ws/WAVS/{LANG}")

    tasks = []
    task_num = 10
    ids = [str(i)*3 for i in range(task_num)]
    
    i = 0
    for wav, ref in data_list.items():
        if i < task_num:
            client_id = ids[i]
            dur = sf.info(wav).duration
            tasks.append((
                client_id,
                f"ws://asr_server:{PORT}/ws/{client_id}",
                wav,
                dur,
                ref)
            )
        i += 1

    print("\n\n单路连接")
    asyncio.get_event_loop().run_until_complete(
        send_audio(tasks[0])
    )

    print(f"\n\n4路连接")
    asyncio.get_event_loop().run_until_complete(
        asyncio.gather(
            send_audio(tasks[0]),
            send_audio(tasks[1]),
            send_audio(tasks[2]),
            send_audio(tasks[3]),
            # send_audio(tasks[4]),
            # send_audio(tasks[5]),
            # send_audio(tasks[6]),
            # send_audio(tasks[7]),
            # send_audio(tasks[8]),
            # send_audio(tasks[9]),
        )
    )

