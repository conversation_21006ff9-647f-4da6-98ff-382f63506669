"""
音频处理工具类
提供音频格式转换、验证等工具函数
"""

import logging
import base64
from typing import Union, Tuple, Optional
import numpy as np
import torch

logger = logging.getLogger(__name__)


class AudioUtils:
    """音频处理工具类"""
    
    @staticmethod
    def validate_audio_format(
        audio_data: bytes, 
        expected_size: int, 
        sample_rate: int = 16000,
        channels: int = 1,
        sample_width: int = 2
    ) -> bool:
        """
        验证音频数据格式
        
        Args:
            audio_data: 音频字节数据
            expected_size: 期望的数据大小
            sample_rate: 采样率
            channels: 声道数
            sample_width: 采样位宽（字节）
            
        Returns:
            bool: 是否符合格式要求
        """
        try:
            # 检查数据大小
            if len(audio_data) != expected_size:
                logger.warning(f"Audio data size mismatch: expected {expected_size}, got {len(audio_data)}")
                return False
                
            # 检查是否能正确解析为音频数组
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # 计算期望的样本数
            expected_samples = expected_size // sample_width
            if len(audio_array) != expected_samples:
                logger.warning(f"Sample count mismatch: expected {expected_samples}, got {len(audio_array)}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Audio format validation failed: {e}")
            return False
            
    @staticmethod
    def decode_base64_audio(base64_data: str) -> bytes:
        """
        解码Base64编码的音频数据
        
        Args:
            base64_data: Base64编码的音频数据
            
        Returns:
            bytes: 解码后的音频字节数据
        """
        try:
            return base64.b64decode(base64_data)
        except Exception as e:
            logger.error(f"Base64 audio decoding failed: {e}")
            raise ValueError(f"Invalid base64 audio data: {e}")
            
    @staticmethod
    def encode_audio_to_base64(audio_data: bytes) -> str:
        """
        将音频数据编码为Base64
        
        Args:
            audio_data: 音频字节数据
            
        Returns:
            str: Base64编码的音频数据
        """
        try:
            return base64.b64encode(audio_data).decode('utf-8')
        except Exception as e:
            logger.error(f"Audio to base64 encoding failed: {e}")
            raise ValueError(f"Audio encoding failed: {e}")
            
    @staticmethod
    def convert_to_float32(audio_data: Union[np.ndarray, torch.Tensor]) -> Union[np.ndarray, torch.Tensor]:
        """
        将音频数据转换为float32格式并归一化
        
        Args:
            audio_data: 音频数据
            
        Returns:
            Union[np.ndarray, torch.Tensor]: 归一化的float32音频数据
        """
        try:
            if isinstance(audio_data, np.ndarray):
                if audio_data.dtype == np.int16:
                    return audio_data.astype(np.float32) / 32768.0
                elif audio_data.dtype == np.int32:
                    return audio_data.astype(np.float32) / 2147483648.0
                elif audio_data.dtype == np.float32:
                    return audio_data
                else:
                    return audio_data.astype(np.float32)
                    
            elif isinstance(audio_data, torch.Tensor):
                if audio_data.dtype == torch.int16:
                    return audio_data.float() / 32768.0
                elif audio_data.dtype == torch.int32:
                    return audio_data.float() / 2147483648.0
                elif audio_data.dtype == torch.float32:
                    return audio_data
                else:
                    return audio_data.float()
                    
            else:
                raise ValueError("Unsupported audio data type")
                
        except Exception as e:
            logger.error(f"Audio format conversion failed: {e}")
            raise
            
    @staticmethod
    def calculate_audio_duration(
        data_size: int, 
        sample_rate: int = 16000, 
        channels: int = 1, 
        sample_width: int = 2
    ) -> float:
        """
        计算音频时长
        
        Args:
            data_size: 音频数据大小（字节）
            sample_rate: 采样率
            channels: 声道数
            sample_width: 采样位宽（字节）
            
        Returns:
            float: 音频时长（秒）
        """
        try:
            samples = data_size // (channels * sample_width)
            duration = samples / sample_rate
            return duration
        except Exception as e:
            logger.error(f"Audio duration calculation failed: {e}")
            return 0.0
            
    @staticmethod
    def resample_audio(
        audio: Union[np.ndarray, torch.Tensor], 
        orig_sr: int, 
        target_sr: int
    ) -> Union[np.ndarray, torch.Tensor]:
        """
        重采样音频
        
        Args:
            audio: 音频数据
            orig_sr: 原始采样率
            target_sr: 目标采样率
            
        Returns:
            Union[np.ndarray, torch.Tensor]: 重采样后的音频数据
        """
        if orig_sr == target_sr:
            return audio
            
        try:
            if isinstance(audio, np.ndarray):
                # 使用简单的线性插值重采样
                ratio = target_sr / orig_sr
                new_length = int(len(audio) * ratio)
                indices = np.linspace(0, len(audio) - 1, new_length)
                resampled = np.interp(indices, np.arange(len(audio)), audio)
                return resampled.astype(audio.dtype)
                
            elif isinstance(audio, torch.Tensor):
                # 使用torch的重采样
                import torchaudio.transforms as T
                resampler = T.Resample(orig_sr, target_sr)
                if audio.dim() == 1:
                    audio = audio.unsqueeze(0)
                resampled = resampler(audio)
                return resampled.squeeze(0)
                
            else:
                raise ValueError("Unsupported audio data type")
                
        except Exception as e:
            logger.error(f"Audio resampling failed: {e}")
            raise
            
    @staticmethod
    def normalize_audio(audio: Union[np.ndarray, torch.Tensor], target_level: float = 0.9) -> Union[np.ndarray, torch.Tensor]:
        """
        归一化音频到指定电平
        
        Args:
            audio: 音频数据
            target_level: 目标电平
            
        Returns:
            Union[np.ndarray, torch.Tensor]: 归一化后的音频数据
        """
        try:
            if isinstance(audio, np.ndarray):
                max_val = np.abs(audio).max()
                if max_val > 0:
                    return audio * (target_level / max_val)
                return audio
                
            elif isinstance(audio, torch.Tensor):
                max_val = torch.abs(audio).max()
                if max_val > 0:
                    return audio * (target_level / max_val)
                return audio
                
            else:
                raise ValueError("Unsupported audio data type")
                
        except Exception as e:
            logger.error(f"Audio normalization failed: {e}")
            raise
