# export LD_LIBRARY_PATH=/usr/local/cuda-11.8/lib64:/opt/tritonserver/backends/onnxruntime:/usr/local/cuda/compat/lib:/usr/local/nvidia/lib:/usr/local/nvidia/lib64
import onnxruntime as ort
import torch
import time
test_model = "/ws/res/zh/encoder.onnx"

print('torch version:', torch.__version__)
print('cuda version:', torch.version.cuda)
print('cudnn version:', torch.backends.cudnn.version())
# print('onnxruntime version:', ort.__version__)
providers = ['CUDAExecutionProvider'] if torch.cuda.is_available() else ['CPUExecutionProvider']
print("providers:", providers)

try:
    encoder_session = ort.InferenceSession(test_model, providers=providers)
    print([input.name for input in encoder_session.get_inputs()])
    print(f"Use {providers}")
    time.sleep(10)
except:
    print(f"Can not use {providers}")