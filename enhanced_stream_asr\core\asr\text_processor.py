"""
文本后处理器
实现可配置的分隔符系统和话音间隔检测优化
"""

import re
import time
import logging
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TextSegment:
    """文本段"""
    text: str
    start_time: float
    end_time: float
    confidence: float = 0.0
    is_final: bool = False


class TextProcessor:
    """文本后处理器"""
    
    def __init__(self, config_manager):
        """
        初始化文本处理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.segment_history: List[TextSegment] = []
        self.last_segment_time = 0.0
        
    def process_recognition_result(
        self, 
        text: str, 
        language: str, 
        timestamp: float,
        confidence: float = 0.0,
        is_final: bool = False,
        custom_separator: Optional[str] = None
    ) -> str:
        """
        处理识别结果，添加适当的分隔符
        
        Args:
            text: 识别文本
            language: 语种代码
            timestamp: 时间戳
            confidence: 置信度
            is_final: 是否为最终结果
            custom_separator: 自定义分隔符
            
        Returns:
            处理后的文本
        """
        if not text.strip():
            return text
            
        try:
            # 创建文本段
            segment = TextSegment(
                text=text.strip(),
                start_time=self.last_segment_time,
                end_time=timestamp,
                confidence=confidence,
                is_final=is_final
            )
            
            # 检查是否需要添加分隔符
            processed_text = self._add_separator_if_needed(
                segment, language, custom_separator
            )
            
            # 更新历史记录
            if is_final:
                self.segment_history.append(segment)
                self.last_segment_time = timestamp
                
                # 限制历史记录长度
                if len(self.segment_history) > 100:
                    self.segment_history = self.segment_history[-50:]
                    
            return processed_text
            
        except Exception as e:
            logger.error(f"Text processing error: {e}")
            return text
            
    def _add_separator_if_needed(
        self, 
        current_segment: TextSegment, 
        language: str,
        custom_separator: Optional[str] = None
    ) -> str:
        """
        根据时间间隔和语种规则添加分隔符
        
        Args:
            current_segment: 当前文本段
            language: 语种代码
            custom_separator: 自定义分隔符
            
        Returns:
            处理后的文本
        """
        text = current_segment.text
        
        # 如果没有历史记录，直接返回
        if not self.segment_history:
            return text
            
        # 获取最后一个最终结果
        last_final_segment = None
        for segment in reversed(self.segment_history):
            if segment.is_final:
                last_final_segment = segment
                break
                
        if not last_final_segment:
            return text
            
        # 计算时间间隔
        time_gap = current_segment.start_time - last_final_segment.end_time
        
        # 获取静音阈值
        silence_threshold = self.config_manager.get_silence_threshold(language)
        
        # 如果时间间隔超过阈值，添加分隔符
        if time_gap > silence_threshold:
            separator = self.config_manager.get_separator(language, custom_separator)
            
            # 检查是否已经有分隔符
            if not self._ends_with_separator(last_final_segment.text, separator):
                return separator + text
                
        return text
        
    def _ends_with_separator(self, text: str, separator: str) -> bool:
        """
        检查文本是否以分隔符结尾
        
        Args:
            text: 文本
            separator: 分隔符
            
        Returns:
            是否以分隔符结尾
        """
        if not text or not separator:
            return False
            
        # 移除空白字符后检查
        text = text.rstrip()
        separator = separator.strip()
        
        return text.endswith(separator)
        
    def apply_language_specific_rules(self, text: str, language: str) -> str:
        """
        应用语种特定的文本处理规则
        
        Args:
            text: 输入文本
            language: 语种代码
            
        Returns:
            处理后的文本
        """
        if not text:
            return text
            
        try:
            if language == 'zh':
                return self._process_chinese_text(text)
            elif language == 'en':
                return self._process_english_text(text)
            elif language == 'ru':
                return self._process_russian_text(text)
            elif language in ['ug', 'kk']:
                return self._process_turkic_text(text)
            else:
                return text
                
        except Exception as e:
            logger.error(f"Language-specific processing error for {language}: {e}")
            return text
            
    def _process_chinese_text(self, text: str) -> str:
        """处理中文文本"""
        # 移除多余的空格
        text = re.sub(r'\s+', '', text)
        
        # 标点符号标准化
        text = text.replace('，，', '，')
        text = text.replace('。。', '。')
        
        return text
        
    def _process_english_text(self, text: str) -> str:
        """处理英文文本"""
        # 标准化空格
        text = re.sub(r'\s+', ' ', text)
        
        # 首字母大写
        if text and text[0].islower():
            text = text[0].upper() + text[1:]
            
        # 标点符号前的空格处理
        text = re.sub(r'\s+([,.!?])', r'\1', text)
        
        return text.strip()
        
    def _process_russian_text(self, text: str) -> str:
        """处理俄语文本"""
        # 标准化空格
        text = re.sub(r'\s+', ' ', text)
        
        # 首字母大写
        if text and text[0].islower():
            text = text[0].upper() + text[1:]
            
        return text.strip()
        
    def _process_turkic_text(self, text: str) -> str:
        """处理突厥语族文本（维语、哈萨克语等）"""
        # 标准化空格
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
        
    def format_final_result(
        self, 
        segments: List[TextSegment], 
        language: str,
        custom_separator: Optional[str] = None
    ) -> str:
        """
        格式化最终结果
        
        Args:
            segments: 文本段列表
            language: 语种代码
            custom_separator: 自定义分隔符
            
        Returns:
            格式化后的完整文本
        """
        if not segments:
            return ""
            
        try:
            separator = self.config_manager.get_separator(language, custom_separator)
            silence_threshold = self.config_manager.get_silence_threshold(language)
            
            result_parts = []
            
            for i, segment in enumerate(segments):
                text = segment.text.strip()
                if not text:
                    continue
                    
                # 应用语种特定规则
                text = self.apply_language_specific_rules(text, language)
                
                # 检查是否需要添加分隔符
                if i > 0:
                    prev_segment = segments[i-1]
                    time_gap = segment.start_time - prev_segment.end_time
                    
                    if time_gap > silence_threshold:
                        result_parts.append(separator)
                        
                result_parts.append(text)
                
            return ''.join(result_parts)
            
        except Exception as e:
            logger.error(f"Final result formatting error: {e}")
            return ' '.join(segment.text for segment in segments if segment.text.strip())
            
    def reset(self):
        """重置处理器状态"""
        self.segment_history.clear()
        self.last_segment_time = 0.0
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        if not self.segment_history:
            return {
                "total_segments": 0,
                "avg_confidence": 0.0,
                "total_duration": 0.0
            }
            
        total_confidence = sum(seg.confidence for seg in self.segment_history)
        avg_confidence = total_confidence / len(self.segment_history)
        
        total_duration = (
            self.segment_history[-1].end_time - self.segment_history[0].start_time
            if len(self.segment_history) > 1 else 0.0
        )
        
        return {
            "total_segments": len(self.segment_history),
            "avg_confidence": avg_confidence,
            "total_duration": total_duration,
            "segments_per_second": len(self.segment_history) / max(total_duration, 1.0)
        }
