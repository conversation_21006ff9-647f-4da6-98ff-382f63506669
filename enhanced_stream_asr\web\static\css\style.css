/* Enhanced Stream ASR 样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header h1 {
    font-size: 2.5em;
    color: #4a5568;
    margin-bottom: 10px;
}

.header h1 i {
    color: #667eea;
    margin-right: 15px;
}

.header p {
    font-size: 1.1em;
    color: #718096;
}

/* 控制面板 */
.control-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.settings-section, .recording-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.settings-section h3, .recording-section h3 {
    font-size: 1.3em;
    color: #4a5568;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.settings-section h3 i, .recording-section h3 i {
    margin-right: 10px;
    color: #667eea;
}

/* 设置组 */
.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #4a5568;
}

.setting-group select, .setting-group input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.setting-group select:focus, .setting-group input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
}

.setting-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

/* 录音控制按钮 */
.recording-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-start {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.btn-start:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
}

.btn-stop {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
}

.btn-stop:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.4);
}

.btn-clear {
    background: linear-gradient(135deg, #a0aec0, #718096);
    color: white;
}

.btn-clear:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(160, 174, 192, 0.4);
}

/* 状态显示 */
.status-section {
    margin-bottom: 20px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.status-label {
    font-weight: 500;
    color: #4a5568;
}

.status-value {
    font-weight: 600;
}

.status-value.connected {
    color: #48bb78;
}

.status-value.disconnected {
    color: #f56565;
}

.status-value.recording {
    color: #ed8936;
}

/* 音量指示器 */
.volume-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.volume-label {
    font-weight: 500;
    color: #4a5568;
    min-width: 50px;
}

.volume-bar {
    flex: 1;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.volume-level {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #ed8936, #f56565);
    width: 0%;
    transition: width 0.1s ease;
}

/* 结果显示区域 */
.results-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.results-section h3 {
    font-size: 1.3em;
    color: #4a5568;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.results-section h3 i {
    margin-right: 10px;
    color: #667eea;
}

.results-container {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    background: #f7fafc;
}

.no-results {
    text-align: center;
    color: #a0aec0;
    padding: 50px 20px;
}

.no-results i {
    font-size: 3em;
    margin-bottom: 15px;
    display: block;
}

.result-item {
    margin-bottom: 15px;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.result-item.intermediate {
    border-left-color: #ed8936;
    opacity: 0.8;
}

.result-item.final {
    border-left-color: #48bb78;
}

.result-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9em;
    color: #718096;
}

.result-text {
    font-size: 1.1em;
    line-height: 1.5;
    color: #2d3748;
}

/* 会话信息和日志 */
.session-info, .logs-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.session-info h3, .logs-section h3 {
    font-size: 1.3em;
    color: #4a5568;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.session-info h3 i, .logs-section h3 i {
    margin-right: 10px;
    color: #667eea;
}

.session-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: #f7fafc;
    border-radius: 6px;
}

.info-label {
    font-weight: 500;
    color: #4a5568;
}

.info-value {
    font-weight: 600;
    color: #2d3748;
}

/* 日志样式 */
.btn-toggle {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 1em;
}

.logs-container {
    margin-top: 15px;
}

.logs-list {
    max-height: 200px;
    overflow-y: auto;
    background: #1a202c;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    margin-bottom: 10px;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.log-entry.info {
    color: #63b3ed;
}

.log-entry.warning {
    color: #f6e05e;
}

.log-entry.error {
    color: #fc8181;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.9em;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .control-panel {
        grid-template-columns: 1fr;
    }
    
    .recording-controls {
        flex-direction: column;
    }
    
    .session-details {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 2em;
    }
}
