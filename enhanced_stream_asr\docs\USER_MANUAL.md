# Enhanced Stream ASR 用户手册

## 📖 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [系统要求](#系统要求)
4. [安装部署](#安装部署)
5. [配置说明](#配置说明)
6. [使用指南](#使用指南)
7. [Web界面使用](#web界面使用)
8. [API接口](#api接口)
9. [监控和告警](#监控和告警)
10. [故障排除](#故障排除)
11. [常见问题](#常见问题)
12. [技术支持](#技术支持)

---

## 🎯 系统概述

Enhanced Stream ASR 是一个高性能的实时语音识别系统，支持多语种流式语音识别。系统采用先进的深度学习技术，提供低延迟、高准确率的语音转文字服务。

### 主要特性

- **🌍 多语种支持**: 支持中文、英文、俄文、维吾尔文、哈萨克文等多种语言
- **⚡ 实时流式处理**: 低延迟的实时语音识别，支持边说边识别
- **🤖 智能语种识别**: 自动检测输入语音的语种
- **🎙️ 语音活动检测**: 智能检测语音起止，过滤静音段
- **🌐 Web界面**: 现代化的Web用户界面，支持实时录音和识别
- **📊 性能监控**: 完善的性能监控和告警系统
- **🔧 灵活配置**: 丰富的配置选项，支持个性化定制
- **📱 多端支持**: 支持WebSocket API，可集成到各种应用中

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 界面      │    │   WebSocket     │    │   HTTP API      │
│                 │    │   接口          │    │   接口          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   会话管理器    │
                    │                 │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   VAD 处理器    │    │   LID 引擎      │    │   ASR 引擎      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   ONNX 引擎池   │
                    │                 │
                    └─────────────────┘
```

---

## 🚀 快速开始

### 1. 环境准备

确保您的系统满足以下要求：
- Python 3.8+
- 至少 4GB 内存
- 支持音频输入的设备

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd enhanced_stream_asr

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 准备模型文件

将模型文件放置到 `models/` 目录下：

```
models/
├── zh/                 # 中文模型
│   ├── encoder.onnx
│   ├── ctc.onnx
│   └── units.txt
├── en/                 # 英文模型
│   ├── encoder.onnx
│   ├── ctc.onnx
│   └── units.txt
└── lid/                # 语种识别模型
    └── lid_model.onnx
```

### 4. 启动服务

```bash
# 启动服务器
python server.py

# 或使用配置文件启动
python server.py --config configs/server_config.yaml
```

### 5. 访问Web界面

打开浏览器访问：`http://localhost:8080`

---

## 💻 系统要求

### 硬件要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| CPU | 2核心 2.0GHz | 4核心 3.0GHz+ |
| 内存 | 4GB | 8GB+ |
| 存储 | 2GB 可用空间 | 10GB+ SSD |
| 网络 | 100Mbps | 1Gbps |

### 软件要求

| 软件 | 版本要求 |
|------|----------|
| Python | 3.8+ |
| PyTorch | 1.9+ |
| ONNX Runtime | 1.10+ |
| FastAPI | 0.68+ |
| WebRTC VAD | 2.0+ |

### 浏览器支持

| 浏览器 | 最低版本 |
|--------|----------|
| Chrome | 88+ |
| Firefox | 85+ |
| Safari | 14+ |
| Edge | 88+ |

---

## 🛠️ 安装部署

### 方式一：直接安装

1. **克隆代码**
```bash
git clone <repository-url>
cd enhanced_stream_asr
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置模型路径**
```bash
python scripts/fix_model_paths.py
```

5. **启动服务**
```bash
python server.py
```

### 方式二：Docker部署

1. **构建镜像**
```bash
docker build -t enhanced-stream-asr .
```

2. **运行容器**
```bash
docker run -d \
  --name asr-server \
  -p 8080:8080 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/configs:/app/configs \
  enhanced-stream-asr
```

### 方式三：生产环境部署

1. **使用Gunicorn**
```bash
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8080 \
  --timeout 300 \
  server:app
```

2. **使用Nginx反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## ⚙️ 配置说明

### 服务器配置 (`configs/server_config.yaml`)

```yaml
# 服务器基本配置
server:
  host: "0.0.0.0"           # 监听地址
  port: 8080                # 监听端口
  workers: 1                # 工作进程数
  log_level: "INFO"         # 日志级别

# 音频配置
audio:
  sample_rate: 16000        # 采样率
  channels: 1               # 声道数
  chunk_duration: 0.4       # 音频块时长(秒)
  max_audio_duration: 60    # 最大音频时长(秒)

# WebSocket配置
websocket:
  heartbeat_interval: 30    # 心跳间隔(秒)
  max_connections: 100      # 最大连接数
  connection_timeout: 300   # 连接超时(秒)

# VAD配置
vad:
  enable: true              # 是否启用VAD
  vad_type: "webrtcvad"     # VAD类型
  vad_level: 1              # VAD敏感度(0-3)
  frame_length: 30          # 帧长度(ms)
  window_size: 10           # 窗口大小
  seg_threshold: 0.9        # 分段阈值
```

### 语种配置 (`configs/lang_configs/{language}.yaml`)

```yaml
# 语种基本信息
code: "zh"                  # 语种代码
name: "中文"                # 语种名称

# 分隔符设置
separator: "，。！？；："    # 句子分隔符
silence_threshold: 0.35     # 静音阈值

# 模型配置
model:
  model_path: "models/zh"   # 模型目录
  dict_path: "models/zh/units.txt"      # 词典文件
  hotwords_path: "models/zh/hotwords.txt"  # 热词文件

  # 解码参数
  chunk_size: 16            # 块大小
  left_chunks: 16           # 左上下文块数
  decoding_window: 67       # 解码窗口
  subsampling_rate: 4       # 子采样率
  right_context: 7          # 右上下文

# 特征配置
features:
  enable_punctuation: true  # 启用标点符号
  enable_itn: true         # 启用逆文本标准化
  enable_hotwords: true    # 启用热词增强
```

### LID配置 (`configs/lid_config.yaml`)

```yaml
# LID基本配置
enable: true                # 是否启用LID
confidence_threshold: 0.8   # 置信度阈值
min_audio_duration: 0.4     # 最小音频时长
max_audio_duration: 2.4     # 最大音频时长

# 模型配置
model:
  model_path: "models/lid/lid_model.onnx"

# 渐进式识别
progressive_steps: [0.4, 0.8, 2.4]  # 渐进式步骤

# 语种映射
language_mapping:
  0: "zh"
  1: "en"
  2: "ru"
  3: "ug"
  4: "kk"
```

---

## 📱 使用指南

### Web界面使用

1. **访问界面**
   - 打开浏览器访问 `http://localhost:8080`
   - 首次访问会要求麦克风权限，请点击"允许"

2. **开始识别**
   - 选择目标语种（或选择"自动检测"）
   - 点击"开始录音"按钮
   - 对着麦克风说话
   - 实时查看识别结果

3. **功能说明**
   - **语种选择**: 手动选择识别语种或启用自动检测
   - **音量指示**: 实时显示音频输入音量
   - **识别结果**: 显示实时和最终识别结果
   - **会话管理**: 支持暂停、继续、重新开始

### WebSocket API使用

#### 连接建立

```javascript
const ws = new WebSocket('ws://localhost:8080/ws/stream');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');

    // 发送握手消息
    const handshake = {
        type: 'handshake',
        client_id: 'your-client-id',
        language: 'zh',  // 或 'auto' 自动检测
        auto_language_detection: false,
        audio_config: {
            sample_rate: 16000,
            channels: 1,
            chunk_duration: 0.4
        }
    };

    ws.send(JSON.stringify(handshake));
};
```

#### 发送音频数据

```javascript
// 发送音频数据
function sendAudioData(audioBuffer, isLast = false) {
    const message = {
        type: 'audio_data',
        sequence_id: sequenceId++,
        audio_data: Array.from(new Int16Array(audioBuffer)),
        is_last: isLast,
        timestamp: Date.now()
    };

    ws.send(JSON.stringify(message));
}
```

#### 处理识别结果

```javascript
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);

    switch(data.type) {
        case 'handshake_response':
            if (data.success) {
                console.log('握手成功');
            } else {
                console.error('握手失败:', data.error);
            }
            break;

        case 'recognition_result':
            if (data.success) {
                console.log('识别结果:', data.text);
                console.log('是否最终结果:', data.is_final);
                console.log('置信度:', data.confidence);
            }
            break;

        case 'language_detected':
            console.log('检测到语种:', data.language);
            break;

        case 'error':
            console.error('识别错误:', data.error);
            break;
    }
};
```

### HTTP API使用

#### 获取支持的语种

```bash
curl -X GET "http://localhost:8080/api/languages"
```

响应示例：
```json
{
    "languages": {
        "zh": "中文",
        "en": "English",
        "ru": "Русский"
    },
    "default": "zh"
}
```

#### 获取服务器状态

```bash
curl -X GET "http://localhost:8080/api/status"
```

#### 健康检查

```bash
curl -X GET "http://localhost:8080/api/monitoring/health"
```

---

## 📊 监控和告警

### 监控仪表板

访问 `http://localhost:8080/api/monitoring/dashboard` 查看实时监控仪表板，包括：

- **系统健康状态**: CPU、内存、磁盘使用率
- **性能指标**: 请求数量、响应时间、成功率
- **错误统计**: 最近错误记录和趋势
- **告警状态**: 活跃告警和历史记录

### 监控API

#### 获取性能统计

```bash
# 获取所有操作的性能统计
curl "http://localhost:8080/api/monitoring/performance/stats?time_window=3600"

# 获取特定操作的统计
curl "http://localhost:8080/api/monitoring/performance/stats?operation=asr_process_chunk&time_window=3600"
```

#### 获取活跃告警

```bash
curl "http://localhost:8080/api/monitoring/alerts/active"
```

#### 获取系统状态

```bash
curl "http://localhost:8080/api/monitoring/system/status"
```

### 告警配置

系统内置以下告警规则：

| 告警名称 | 触发条件 | 级别 | 持续时间 |
|----------|----------|------|----------|
| high_cpu_usage | CPU > 80% | WARNING | 2分钟 |
| critical_cpu_usage | CPU > 95% | CRITICAL | 1分钟 |
| high_memory_usage | 内存 > 85% | WARNING | 2分钟 |
| critical_memory_usage | 内存 > 95% | CRITICAL | 1分钟 |
| high_error_rate | 错误率 > 10% | WARNING | 5分钟 |
| slow_response_time | 响应时间 > 5秒 | WARNING | 3分钟 |

---

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 服务启动失败

**问题**: 服务器启动时报错
```
Error: Failed to load model
```

**解决方案**:
1. 检查模型文件是否存在
2. 运行模型路径修复脚本：
   ```bash
   python scripts/fix_model_paths.py
   ```
3. 检查模型文件权限

#### 2. WebSocket连接失败

**问题**: 前端无法连接WebSocket
```
WebSocket connection failed
```

**解决方案**:
1. 检查防火墙设置
2. 确认端口8080未被占用
3. 检查代理服务器配置

#### 3. 音频识别准确率低

**问题**: 识别结果不准确

**解决方案**:
1. 检查音频质量（采样率、噪音）
2. 调整VAD敏感度
3. 确认使用正确的语种模型
4. 检查热词配置

#### 4. 内存使用过高

**问题**: 系统内存占用持续增长

**解决方案**:
1. 检查会话数量限制
2. 调整模型缓存大小
3. 重启服务释放内存

#### 5. 响应时间过长

**问题**: 识别延迟较高

**解决方案**:
1. 检查CPU使用率
2. 调整音频块大小
3. 优化模型配置参数

### 日志分析

#### 查看系统日志

```bash
# 查看最新日志
tail -f logs/server.log

# 查看错误日志
grep "ERROR" logs/server.log

# 查看特定时间段日志
grep "2024-01-01" logs/server.log
```

#### 日志级别说明

| 级别 | 说明 | 示例 |
|------|------|------|
| DEBUG | 调试信息 | 详细的处理步骤 |
| INFO | 一般信息 | 服务启动、会话创建 |
| WARNING | 警告信息 | 配置问题、性能警告 |
| ERROR | 错误信息 | 处理失败、连接错误 |
| CRITICAL | 严重错误 | 系统崩溃、服务不可用 |

### 性能调优

#### 1. 系统级优化

```bash
# 增加文件描述符限制
ulimit -n 65536

# 调整TCP参数
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 1024' >> /etc/sysctl.conf
sysctl -p
```

#### 2. 应用级优化

- **调整工作进程数**: 根据CPU核心数设置
- **优化音频块大小**: 平衡延迟和准确率
- **配置模型缓存**: 根据内存大小调整
- **启用模型量化**: 减少内存占用

---

## ❓ 常见问题

### Q1: 支持哪些音频格式？
A: 系统支持PCM 16位音频，采样率16kHz，单声道。Web界面会自动转换音频格式。

### Q2: 可以同时识别多种语言吗？
A: 可以启用自动语种检测功能，系统会自动识别输入语音的语种。

### Q3: 如何添加新的语种支持？
A: 需要准备对应语种的ONNX模型文件，并在配置文件中添加相应配置。

### Q4: 系统支持离线使用吗？
A: 是的，系统完全支持离线部署和使用，不需要互联网连接。

### Q5: 如何提高识别准确率？
A:
- 确保音频质量良好
- 使用合适的语种模型
- 配置热词文件
- 调整VAD参数

### Q6: 支持实时流式识别吗？
A: 是的，系统专为实时流式识别设计，支持边说边识别。

### Q7: 如何扩展系统容量？
A:
- 增加服务器资源（CPU、内存）
- 部署多个实例进行负载均衡
- 使用容器化部署

### Q8: 系统安全性如何？
A:
- 支持HTTPS/WSS加密传输
- 音频数据不会被存储
- 支持访问控制和认证

---

## 📞 技术支持

### 获取帮助

1. **文档资源**
   - [开发者手册](DEVELOPER_MANUAL.md)
   - [API文档](API_REFERENCE.md)
   - [配置参考](CONFIG_REFERENCE.md)

2. **社区支持**
   - GitHub Issues
   - 技术论坛
   - 用户群组

3. **商业支持**
   - 技术咨询
   - 定制开发
   - 培训服务

### 报告问题

提交问题时请包含以下信息：

1. **系统环境**
   - 操作系统版本
   - Python版本
   - 依赖包版本

2. **问题描述**
   - 详细的错误信息
   - 重现步骤
   - 期望行为

3. **日志文件**
   - 相关的错误日志
   - 系统监控数据

4. **配置文件**
   - 相关的配置文件内容
   - 模型文件信息

---

## 📄 版本信息

- **当前版本**: v1.0.0
- **发布日期**: 2024-01-01
- **更新日志**: 查看 [CHANGELOG.md](CHANGELOG.md)

---

## 📜 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

---

*感谢使用 Enhanced Stream ASR！如有任何问题或建议，欢迎联系我们。*