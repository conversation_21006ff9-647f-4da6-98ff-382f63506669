"""
语种识别引擎
基于ONNX模型的语种识别实现
"""

import os
import logging
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Any
import onnxruntime as ort

from ..engines.base_engine import BaseEngine
from ..audio import FeatureExtractor

logger = logging.getLogger(__name__)


class LIDEngine(BaseEngine):
    """语种识别引擎"""
    
    def __init__(self, model_path: str, config: Dict[str, Any]):
        """
        初始化LID引擎
        
        Args:
            model_path: LID模型路径
            config: 引擎配置
        """
        super().__init__(model_path, config)
        
        self.session = None
        self.feature_extractor = None
        self.language_mapping = config.get('language_mapping', {})
        self.supported_languages = config.get('supported_languages', [])
        self.confidence_threshold = config.get('confidence_threshold', 0.8)
        
        # 设备配置
        self.device = config.get('device', 'cpu')
        self.device_id = config.get('device_id', 0)
        
    def load_model(self) -> bool:
        """加载LID模型"""
        try:
            if not os.path.exists(self.model_path):
                logger.error(f"LID model not found: {self.model_path}")
                return False
                
            # 设置ONNX Runtime提供者
            providers = self._get_providers()
            
            # 加载模型
            self.session = ort.InferenceSession(self.model_path, providers=providers)
            
            # 初始化特征提取器
            feature_config = {
                'feat_type': 'fbank',
                'num_mel_bins': 80,
                'frame_length': 25,
                'frame_shift': 10,
                'sample_rate': 16000
            }
            self.feature_extractor = FeatureExtractor(feature_config)
            
            self.is_loaded = True
            logger.info(f"LID model loaded successfully: {self.model_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load LID model: {e}")
            return False
            
    def unload_model(self) -> bool:
        """卸载LID模型"""
        try:
            if self.session:
                del self.session
                self.session = None
                
            if self.feature_extractor:
                self.feature_extractor = None
                
            self.is_loaded = False
            logger.info("LID model unloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload LID model: {e}")
            return False
            
    def infer(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行语种识别推理
        
        Args:
            inputs: 输入数据，包含音频数据
            
        Returns:
            识别结果
        """
        if not self.is_ready():
            raise RuntimeError("LID engine is not ready")
            
        try:
            # 获取音频数据
            audio_data = inputs.get('audio')
            if audio_data is None:
                raise ValueError("Missing 'audio' in inputs")
                
            # 提取特征
            features = self._extract_features(audio_data)
            
            # 执行推理
            predictions = self._run_inference(features)
            
            # 解析结果
            result = self._parse_predictions(predictions)
            
            return {
                'language': result['language'],
                'confidence': result['confidence'],
                'all_predictions': result['all_predictions'],
                'success': True
            }
            
        except Exception as e:
            logger.error(f"LID inference failed: {e}")
            return {
                'error': str(e),
                'success': False
            }
            
    def is_ready(self) -> bool:
        """检查引擎是否就绪"""
        return self.is_loaded and self.session is not None
        
    def _get_providers(self) -> List[str]:
        """获取ONNX Runtime提供者"""
        if self.device == "gpu":
            return [
                ('CUDAExecutionProvider', {
                    'device_id': self.device_id,
                    'arena_extend_strategy': 'kNextPowerOfTwo',
                    'gpu_mem_limit': 1 * 1024 * 1024 * 1024,  # 1GB
                }),
                'CPUExecutionProvider'
            ]
        else:
            return ['CPUExecutionProvider']
            
    def _extract_features(self, audio_data: np.ndarray) -> np.ndarray:
        """提取音频特征"""
        try:
            # 转换为torch tensor
            if isinstance(audio_data, np.ndarray):
                waveform = torch.from_numpy(audio_data).float()
            else:
                waveform = audio_data
                
            # 确保是正确的形状
            if waveform.dim() == 1:
                waveform = waveform.unsqueeze(0)
                
            # 提取特征
            features = self.feature_extractor.extract_features(waveform)
            
            # 添加batch维度
            if features.dim() == 2:
                features = features.unsqueeze(0)  # [1, T, F]
                
            return features.numpy()
            
        except Exception as e:
            logger.error(f"Feature extraction failed: {e}")
            raise
            
    def _run_inference(self, features: np.ndarray) -> np.ndarray:
        """运行推理"""
        try:
            # 获取输入输出名称
            input_name = self.session.get_inputs()[0].name
            output_name = self.session.get_outputs()[0].name
            
            # 执行推理
            result = self.session.run([output_name], {input_name: features})
            
            return result[0]
            
        except Exception as e:
            logger.error(f"Inference execution failed: {e}")
            raise
            
    def _parse_predictions(self, predictions: np.ndarray) -> Dict[str, Any]:
        """解析预测结果"""
        try:
            # 应用softmax
            if predictions.ndim == 2:
                predictions = predictions[0]  # 移除batch维度
                
            exp_preds = np.exp(predictions - np.max(predictions))
            probabilities = exp_preds / np.sum(exp_preds)
            
            # 获取最高概率的语种
            max_idx = np.argmax(probabilities)
            max_confidence = float(probabilities[max_idx])
            
            # 映射到语种代码
            predicted_language = self._map_to_language_code(max_idx)
            
            # 构建所有预测结果
            all_predictions = []
            for i, prob in enumerate(probabilities):
                lang_code = self._map_to_language_code(i)
                if lang_code:
                    all_predictions.append({
                        'language': lang_code,
                        'confidence': float(prob)
                    })
                    
            # 按置信度排序
            all_predictions.sort(key=lambda x: x['confidence'], reverse=True)
            
            return {
                'language': predicted_language,
                'confidence': max_confidence,
                'all_predictions': all_predictions
            }
            
        except Exception as e:
            logger.error(f"Prediction parsing failed: {e}")
            raise
            
    def _map_to_language_code(self, class_idx: int) -> Optional[str]:
        """将类别索引映射到语种代码"""
        # 从配置中读取映射关系
        mapping = self.config.get('language_mapping', {
            0: 'zh',
            1: 'en', 
            2: 'ru',
            3: 'ug',
            4: 'kk'
        })

        return mapping.get(class_idx)
        
    def detect_language(
        self, 
        audio_data: np.ndarray, 
        min_confidence: Optional[float] = None
    ) -> Tuple[Optional[str], float]:
        """
        检测音频的语种
        
        Args:
            audio_data: 音频数据
            min_confidence: 最小置信度阈值
            
        Returns:
            (语种代码, 置信度)
        """
        if min_confidence is None:
            min_confidence = self.confidence_threshold
            
        try:
            result = self.infer({'audio': audio_data})
            
            if not result['success']:
                return None, 0.0
                
            language = result['language']
            confidence = result['confidence']
            
            # 检查置信度是否满足要求
            if confidence >= min_confidence:
                return language, confidence
            else:
                logger.debug(f"LID confidence too low: {confidence} < {min_confidence}")
                return None, confidence
                
        except Exception as e:
            logger.error(f"Language detection failed: {e}")
            return None, 0.0
            
    def get_supported_languages(self) -> List[str]:
        """获取支持的语种列表"""
        return self.supported_languages.copy()
        
    def is_language_supported(self, language: str) -> bool:
        """检查语种是否支持"""
        return language in self.supported_languages
