#!/bin/bash
help_message="Usage: ./start_backend.sh [options]
Options:
  --port <int>        指定服务端口
  --gpus <string>     指定GPU, 多张卡用英文逗号分隔
  --model_dir <dir>   指定模型目录
  --log_dir <dir>     指定日志目录
  --log_name <string> 主日志文件名
  --config <file>     指定配置文件, 以上参数可在配置文件中定义, 优先级：命令行参数 > 指定配置文件参数 > 默认参数 "

# Default config
port=10090
gpus=0             # 留空则使用全部可用gpu
model_dir="/ws/model/zh"
log_dir="/ws/log/zh"
log_name="sever.log"
log_verbose=0      # 生产环境为0，避免日志量过大影响性能
config=            # 指定，则覆盖默认参数；留空，则使用默认参数

# Overwite config by $config parameters and cmd parameters
. tools/parse_options.sh || exit 1;

# Create log_dir
day=`date +%Y-%m-%d`
if [ -d "$log_dir" ]; then
    if [ "$(ls -A "$log_dir")" ];then
        log_backup="/ws/log/"${day}
        echo "INFO: To avoid overwite, backup '$log_dir' to '$log_backup'"
        if [ -d "$log_backup" ]; then
            mv ${log_dir}/* ${log_backup}/
        else
            mkdir -p ${log_backup}
            mv ${log_dir}/* ${log_backup}/
        fi
    fi
else
    mkdir -p ${log_dir}
fi

# Parse gpus
gpu_info=$(nvidia-smi -L | grep '^GPU' | awk '{print($2)}' | sed 's/:/ /' | xargs)
if [ -n "$gpu_info" ]; then
        if [ -z "$gpus" ]; then
                gpus=$(echo $gpu_info | sed 's/ /,/g')
        fi
        echo "INFO: Start server on GPU, gpus=$gpus"
else
        echo "Error: cannot find GPU device, set to CPU mode."
        exit 1
fi

# Start server
export LOGDIR=$log_dir
export PYTHONIOENCODING=utf-8
export CUDA_VISIBLE_DEVICES="$gpus"
echo "INFO: CUDA_VISIBLE_DEVICES=$gpus"
export PYTHONPATH=$PWD/tools:$PYTHONPATH      # 后续整理到 lib/
# export PYTHONPATH=$PWD/lib/python3.8/site-packages:$PYTHONPATH    # itn.so、webrtcvad
echo "INFO: PYTHONPATH=$PYTHONPATH"
port_params=$(echo ${port} | awk '{printf("--http-port %d --grpc-port %d --metrics-port %d", $1, $1+1, $1+2)}')
echo "INFO: $port_params"
params="
        --log-verbose ${log_verbose} \
        --log-warning true \
        --log-error true \
        --model-repository=${model_dir} \
        --pinned-memory-pool-byte-size=1024000000 \
        --cuda-memory-pool-byte-size=0:1024000000 \
        --http-thread-count 1 \
        $port_params
"
tritonserver $params
nohup tritonserver $params > ${log_dir}/${log_name} 2>&1 &
tail -fn 10 ${log_dir}/${log_name}