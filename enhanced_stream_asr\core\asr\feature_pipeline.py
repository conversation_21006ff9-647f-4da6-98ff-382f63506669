"""
特征提取管道
基于Individual版本的特征提取实现，支持多种特征类型
"""

import logging
import numpy as np
import torch
import torch.nn.functional as F
from typing import Union, Tuple, Optional, Dict, Any

logger = logging.getLogger(__name__)

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    logger.warning("librosa not available, some features may not work")

try:
    import torchaudio.compliance.kaldi as kaldi
    KALDI_AVAILABLE = True
except ImportError:
    KALDI_AVAILABLE = False
    logger.warning("torchaudio.compliance.kaldi not available, fbank features may not work")


class FeaturePipeline:
    """
    音频特征提取管道类，支持多种特征类型
    支持的特征类型包括fbank、mfcc和log_mel_spectrogram
    """
    
    def __init__(self, configs: Dict[str, Any]):
        """
        初始化特征提取管道
        
        Args:
            configs: 特征提取配置参数字典，包括：
                - 'feat_type': str, 支持'fbank', 'mfcc', 'log_mel_spectrogram'
                - 'num_mel_bins': int, Mel频带数量
                - 'frame_length': int, 帧长度(ms)
                - 'frame_shift': int, 帧移位(ms)
                - 'dither': float, 抗混叠噪声强度
                - 'n_fft': int, FFT点数
                - 'hop_length': int, 跳跃长度
                - 'sample_rate': int, 采样率
        """
        logger.info("Initializing FeaturePipeline")
        
        self.configs = configs
        self.feat_type = configs.get('feat_type', 'fbank')
        self.num_mel_bins = configs.get('num_mel_bins', 80)
        self.frame_length = configs.get('frame_length', 25)  # ms
        self.frame_shift = configs.get('frame_shift', 10)    # ms
        self.dither = configs.get('dither', 1.0)
        self.n_fft = configs.get('n_fft', 400)
        self.hop_length = configs.get('hop_length', 160)
        self.sample_rate = configs.get('sample_rate', 16000)
        
        # 选择特征提取函数
        if self.feat_type == "fbank":
            if not KALDI_AVAILABLE:
                raise ImportError("torchaudio.compliance.kaldi is required for fbank features")
            self.feat_func = self.compute_fbank
        elif self.feat_type == 'mfcc':
            if not KALDI_AVAILABLE:
                raise ImportError("torchaudio.compliance.kaldi is required for mfcc features")
            self.feat_func = self.compute_mfcc
        elif self.feat_type == 'log_mel_spectrogram':
            if not LIBROSA_AVAILABLE:
                raise ImportError("librosa is required for log_mel_spectrogram features")
            self.feat_func = self.compute_log_mel_spectrogram
        else:
            raise ValueError(f"Unsupported feature type: {self.feat_type}")
            
        logger.info(f"FeaturePipeline initialized with {self.feat_type} features")
        
    def extract_features(self, audio_data: Union[bytes, np.ndarray, torch.Tensor]) -> torch.Tensor:
        """
        提取音频特征
        
        Args:
            audio_data: 音频数据，可以是bytes、numpy数组或torch张量
            
        Returns:
            torch.Tensor: 提取的特征，形状为[num_frames, num_features]
        """
        try:
            # 转换为波形张量
            if isinstance(audio_data, bytes):
                waveform = self.to_waveform(audio_data)
            elif isinstance(audio_data, np.ndarray):
                waveform = torch.from_numpy(audio_data.astype(np.float32))
                if waveform.max() > 1.0:  # 如果不是归一化的
                    waveform = waveform / 32768.0
            elif isinstance(audio_data, torch.Tensor):
                waveform = audio_data.float()
                if waveform.max() > 1.0:  # 如果不是归一化的
                    waveform = waveform / 32768.0
            else:
                raise ValueError(f"Unsupported audio data type: {type(audio_data)}")
                
            # 确保是1维张量
            if waveform.dim() > 1:
                waveform = waveform.squeeze()
                
            # 提取特征
            features = self.feat_func(waveform, self.sample_rate)
            
            return features
            
        except Exception as e:
            logger.error(f"Feature extraction failed: {e}")
            raise
            
    def to_waveform(self, pcm_bytes: bytes) -> torch.Tensor:
        """
        将PCM字节数据转换为归一化的波形张量
        
        Args:
            pcm_bytes: PCM格式的音频数据，16位整数
            
        Returns:
            torch.Tensor: 归一化后的波形张量，形状为[sample]
        """
        try:
            # 将bytes转换为numpy数组
            audio_array = np.frombuffer(pcm_bytes, dtype=np.int16).copy()
            
            # 转换为浮点数并归一化到[-1, 1]
            max_val = np.iinfo(np.int16).max
            audio_signal = audio_array.astype(np.float32) / max_val
            
            # 转换为torch张量
            waveform = torch.from_numpy(audio_signal)
            
            return waveform
            
        except Exception as e:
            logger.error(f"PCM to waveform conversion failed: {e}")
            raise ValueError("Invalid PCM data format")
            
    def compute_fbank(self, waveform: torch.Tensor, sample_rate: int) -> torch.Tensor:
        """
        计算FBANK特征
        
        Args:
            waveform: 输入波形，形状为[sample]
            sample_rate: 采样率
            
        Returns:
            torch.Tensor: FBANK特征张量，形状为[num_frames, num_mel_bins]
        """
        try:
            # 放大到16位整数范围（kaldi期望的格式）
            waveform = waveform * (1 << 15)
            
            # 添加batch维度
            if waveform.dim() == 1:
                waveform = waveform.unsqueeze(0)
                
            # 计算fbank特征
            feat = kaldi.fbank(
                waveform,
                num_mel_bins=self.num_mel_bins,
                frame_length=self.frame_length,
                frame_shift=self.frame_shift,
                dither=self.dither,
                energy_floor=0.0,
                sample_frequency=sample_rate
            )
            
            return feat
            
        except Exception as e:
            logger.error(f"FBANK computation failed: {e}")
            raise
            
    def compute_mfcc(self, waveform: torch.Tensor, sample_rate: int,
                     num_ceps: int = 40, high_freq: float = 0.0, 
                     low_freq: float = 20.0) -> torch.Tensor:
        """
        计算MFCC特征
        
        Args:
            waveform: 输入波形，形状为[sample]
            sample_rate: 采样率
            num_ceps: MFCC系数数量
            high_freq: 最高频率
            low_freq: 最低频率
            
        Returns:
            torch.Tensor: MFCC特征张量，形状为[num_frames, num_ceps]
        """
        try:
            # 放大到16位整数范围
            waveform = waveform * (1 << 15)
            
            # 添加batch维度
            if waveform.dim() == 1:
                waveform = waveform.unsqueeze(0)
                
            # 计算MFCC特征
            feat = kaldi.mfcc(
                waveform,
                num_mel_bins=self.num_mel_bins,
                frame_length=self.frame_length,
                frame_shift=self.frame_shift,
                dither=self.dither,
                num_ceps=num_ceps,
                high_freq=high_freq,
                low_freq=low_freq,
                sample_frequency=sample_rate
            )
            
            return feat
            
        except Exception as e:
            logger.error(f"MFCC computation failed: {e}")
            raise
            
    def compute_log_mel_spectrogram(self, waveform: torch.Tensor, sample_rate: int,
                                    padding: int = 0, pad_or_trim: bool = False,
                                    max_duration: int = 30) -> torch.Tensor:
        """
        计算对数梅尔频谱图
        
        Args:
            waveform: 输入波形，形状为[sample]
            sample_rate: 采样率
            padding: 填充长度
            pad_or_trim: 是否进行填充或裁剪
            max_duration: 最大持续时间(秒)
            
        Returns:
            torch.Tensor: 对数梅尔频谱图，形状为[num_frames, num_mel_bins]
        """
        try:
            # 填充处理
            if padding > 0:
                waveform = F.pad(waveform, (0, padding))
                
            if pad_or_trim:
                length = max_duration * sample_rate
                if waveform.size(0) >= length:
                    waveform = waveform[:length]
                else:
                    waveform = F.pad(waveform, (0, length - waveform.size(0)))
                    
            # 计算STFT
            window = torch.hann_window(self.n_fft)
            stft = torch.stft(
                waveform,
                self.n_fft,
                self.hop_length,
                window=window,
                return_complex=True
            )
            
            # 计算幅度谱
            magnitudes = stft[..., :-1].abs() ** 2
            
            # 应用Mel滤波器组
            filters = torch.from_numpy(
                librosa.filters.mel(
                    sr=sample_rate, 
                    n_fft=self.n_fft, 
                    n_mels=self.num_mel_bins
                )
            )
            mel_spec = filters @ magnitudes
            
            # 转换为对数域
            log_spec = torch.clamp(mel_spec, min=1e-10).log10()
            log_spec = torch.maximum(log_spec, log_spec.max() - 8.0)
            log_spec = (log_spec + 4.0) / 4.0
            
            # 转置为[num_frames, num_mel_bins]
            return log_spec.transpose(0, 1)
            
        except Exception as e:
            logger.error(f"Log mel spectrogram computation failed: {e}")
            raise
