"""
健康监控器
监控服务组件健康状态和性能指标
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheck:
    """健康检查项"""
    name: str
    check_func: Callable
    interval: float
    timeout: float
    critical: bool = False
    last_check_time: float = 0.0
    last_status: HealthStatus = HealthStatus.UNKNOWN
    last_message: str = ""
    error_count: int = 0
    max_errors: int = 3


@dataclass
class ComponentHealth:
    """组件健康状态"""
    name: str
    status: HealthStatus
    message: str
    last_check_time: float
    response_time_ms: float = 0.0
    error_count: int = 0
    uptime_seconds: float = 0.0
    details: Optional[Dict[str, Any]] = None


class HealthMonitor:
    """健康监控器"""
    
    def __init__(self, check_interval: float = 30.0):
        """
        初始化健康监控器
        
        Args:
            check_interval: 默认检查间隔（秒）
        """
        self.check_interval = check_interval
        self.health_checks: Dict[str, HealthCheck] = {}
        self.component_health: Dict[str, ComponentHealth] = {}
        
        # 监控状态
        self.is_running = False
        self.monitor_task = None
        self.start_time = time.time()
        
        # 全局健康状态
        self.overall_status = HealthStatus.UNKNOWN
        
    async def start(self):
        """启动健康监控"""
        if self.is_running:
            return
            
        self.is_running = True
        self.start_time = time.time()
        
        # 启动监控任务
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        
        logger.info("Health monitor started")
        
    async def stop(self):
        """停止健康监控"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
                
        logger.info("Health monitor stopped")
        
    def register_check(
        self,
        name: str,
        check_func: Callable,
        interval: float = None,
        timeout: float = 10.0,
        critical: bool = False,
        max_errors: int = 3
    ):
        """
        注册健康检查项
        
        Args:
            name: 检查项名称
            check_func: 检查函数（返回 (status, message) 或 (status, message, details)）
            interval: 检查间隔（秒）
            timeout: 检查超时（秒）
            critical: 是否为关键检查项
            max_errors: 最大错误次数
        """
        if interval is None:
            interval = self.check_interval
            
        self.health_checks[name] = HealthCheck(
            name=name,
            check_func=check_func,
            interval=interval,
            timeout=timeout,
            critical=critical,
            max_errors=max_errors
        )
        
        logger.info(f"Registered health check: {name}")
        
    def unregister_check(self, name: str):
        """取消注册健康检查项"""
        if name in self.health_checks:
            del self.health_checks[name]
            logger.info(f"Unregistered health check: {name}")
            
        if name in self.component_health:
            del self.component_health[name]
            
    async def _monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 执行所有健康检查
                await self._run_health_checks()
                
                # 更新全局健康状态
                self._update_overall_status()
                
                # 等待下次检查
                await asyncio.sleep(min(check.interval for check in self.health_checks.values()) 
                                  if self.health_checks else self.check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
                await asyncio.sleep(self.check_interval)
                
    async def _run_health_checks(self):
        """运行健康检查"""
        current_time = time.time()
        
        for check in self.health_checks.values():
            # 检查是否需要执行
            if current_time - check.last_check_time < check.interval:
                continue
                
            # 执行健康检查
            await self._execute_health_check(check)
            
    async def _execute_health_check(self, check: HealthCheck):
        """执行单个健康检查"""
        start_time = time.time()
        
        try:
            # 执行检查函数
            if asyncio.iscoroutinefunction(check.check_func):
                result = await asyncio.wait_for(
                    check.check_func(),
                    timeout=check.timeout
                )
            else:
                result = await asyncio.wait_for(
                    asyncio.get_event_loop().run_in_executor(
                        None, check.check_func
                    ),
                    timeout=check.timeout
                )
                
            # 解析结果
            if isinstance(result, tuple):
                if len(result) == 2:
                    status, message = result
                    details = None
                elif len(result) == 3:
                    status, message, details = result
                else:
                    raise ValueError("Invalid check result format")
            else:
                status = result
                message = ""
                details = None
                
            # 确保status是HealthStatus类型
            if isinstance(status, str):
                status = HealthStatus(status)
            elif isinstance(status, bool):
                status = HealthStatus.HEALTHY if status else HealthStatus.CRITICAL
                
            # 更新检查状态
            check.last_check_time = time.time()
            check.last_status = status
            check.last_message = message
            
            # 重置错误计数（如果成功）
            if status in [HealthStatus.HEALTHY, HealthStatus.WARNING]:
                check.error_count = 0
                
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000
            
            # 更新组件健康状态
            self.component_health[check.name] = ComponentHealth(
                name=check.name,
                status=status,
                message=message,
                last_check_time=check.last_check_time,
                response_time_ms=response_time,
                error_count=check.error_count,
                uptime_seconds=time.time() - self.start_time,
                details=details
            )
            
        except asyncio.TimeoutError:
            self._handle_check_error(check, "Health check timeout")
        except Exception as e:
            self._handle_check_error(check, f"Health check error: {e}")
            
    def _handle_check_error(self, check: HealthCheck, error_message: str):
        """处理检查错误"""
        check.error_count += 1
        check.last_check_time = time.time()
        check.last_message = error_message
        
        # 根据错误次数确定状态
        if check.error_count >= check.max_errors:
            check.last_status = HealthStatus.CRITICAL
        else:
            check.last_status = HealthStatus.WARNING
            
        # 更新组件健康状态
        self.component_health[check.name] = ComponentHealth(
            name=check.name,
            status=check.last_status,
            message=error_message,
            last_check_time=check.last_check_time,
            error_count=check.error_count,
            uptime_seconds=time.time() - self.start_time
        )
        
        logger.warning(f"Health check failed: {check.name} - {error_message}")
        
    def _update_overall_status(self):
        """更新全局健康状态"""
        if not self.component_health:
            self.overall_status = HealthStatus.UNKNOWN
            return
            
        # 检查是否有关键组件处于CRITICAL状态
        critical_components = [
            comp for comp in self.component_health.values()
            if comp.status == HealthStatus.CRITICAL and 
            self.health_checks.get(comp.name, HealthCheck("", None, 0, 0)).critical
        ]
        
        if critical_components:
            self.overall_status = HealthStatus.CRITICAL
            return
            
        # 检查是否有任何组件处于CRITICAL状态
        if any(comp.status == HealthStatus.CRITICAL for comp in self.component_health.values()):
            self.overall_status = HealthStatus.CRITICAL
            return
            
        # 检查是否有组件处于WARNING状态
        if any(comp.status == HealthStatus.WARNING for comp in self.component_health.values()):
            self.overall_status = HealthStatus.WARNING
            return
            
        # 所有组件都健康
        self.overall_status = HealthStatus.HEALTHY
        
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        return {
            "overall_status": self.overall_status.value,
            "uptime_seconds": time.time() - self.start_time,
            "last_check_time": time.time(),
            "components": {
                name: {
                    "status": comp.status.value,
                    "message": comp.message,
                    "last_check_time": comp.last_check_time,
                    "response_time_ms": comp.response_time_ms,
                    "error_count": comp.error_count,
                    "uptime_seconds": comp.uptime_seconds,
                    "details": comp.details
                }
                for name, comp in self.component_health.items()
            },
            "registered_checks": len(self.health_checks)
        }
        
    def get_component_status(self, component_name: str) -> Optional[ComponentHealth]:
        """获取指定组件的健康状态"""
        return self.component_health.get(component_name)
        
    def is_healthy(self) -> bool:
        """检查整体是否健康"""
        return self.overall_status == HealthStatus.HEALTHY
        
    def get_critical_components(self) -> List[ComponentHealth]:
        """获取处于CRITICAL状态的组件"""
        return [
            comp for comp in self.component_health.values()
            if comp.status == HealthStatus.CRITICAL
        ]
        
    def get_warning_components(self) -> List[ComponentHealth]:
        """获取处于WARNING状态的组件"""
        return [
            comp for comp in self.component_health.values()
            if comp.status == HealthStatus.WARNING
        ]
