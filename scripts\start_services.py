#!/usr/bin/env python3
"""
启动ASR服务的脚本
同时启动HTTP服务和WebSocket流式服务
"""

import os
import sys
import time
import signal
import subprocess
import threading
import logging
from pathlib import Path

# Load configuration
from config_loader import (
    triton_config, http_config, stream_config,
    setup_logging, get_config_summary
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        
    def start_service(self, name: str, command: list, cwd: str = None):
        """启动服务"""
        try:
            logger.info(f"Starting {name}...")
            process = subprocess.Popen(
                command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes[name] = process
            
            # Start output monitoring threads
            threading.Thread(
                target=self._monitor_output,
                args=(name, process.stdout, "INFO"),
                daemon=True
            ).start()
            
            threading.Thread(
                target=self._monitor_output,
                args=(name, process.stderr, "ERROR"),
                daemon=True
            ).start()
            
            logger.info(f"{name} started with PID {process.pid}")
            return process
            
        except Exception as e:
            logger.error(f"Failed to start {name}: {e}")
            return None
    
    def _monitor_output(self, service_name: str, stream, level: str):
        """监控服务输出"""
        try:
            for line in iter(stream.readline, ''):
                if line.strip():
                    if level == "ERROR":
                        logger.error(f"[{service_name}] {line.strip()}")
                    else:
                        logger.info(f"[{service_name}] {line.strip()}")
        except Exception as e:
            logger.error(f"Error monitoring {service_name} output: {e}")
    
    def stop_all_services(self):
        """停止所有服务"""
        logger.info("Stopping all services...")
        self.running = False
        
        for name, process in self.processes.items():
            try:
                logger.info(f"Stopping {name}...")
                process.terminate()
                
                # Wait for graceful shutdown
                try:
                    process.wait(timeout=10)
                    logger.info(f"{name} stopped gracefully")
                except subprocess.TimeoutExpired:
                    logger.warning(f"{name} did not stop gracefully, killing...")
                    process.kill()
                    process.wait()
                    logger.info(f"{name} killed")
                    
            except Exception as e:
                logger.error(f"Error stopping {name}: {e}")
        
        self.processes.clear()
    
    def check_services(self):
        """检查服务状态"""
        status = {}
        for name, process in self.processes.items():
            if process.poll() is None:
                status[name] = "running"
            else:
                status[name] = f"stopped (exit code: {process.returncode})"
        return status
    
    def restart_service(self, name: str):
        """重启服务"""
        if name in self.processes:
            process = self.processes[name]
            logger.info(f"Restarting {name}...")
            
            # Stop the service
            try:
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            
            # Remove from processes
            del self.processes[name]
            
            # Restart based on service type
            if name == "HTTP Service":
                self.start_http_service()
            elif name == "Stream Service":
                self.start_stream_service()
            elif name == "Triton Server":
                self.start_triton_server()
    
    def start_triton_server(self):
        """启动Triton服务器"""
        model_repo = triton_config.MODEL_REPOSITORY

        if not os.path.exists(model_repo):
            logger.error(f"Model repository not found: {model_repo}")
            return None

        command = [
            "tritonserver",
            f"--model-repository={model_repo}",
            f"--http-port={triton_config.HTTP_PORT}",
            f"--grpc-port={triton_config.GRPC_PORT}",
            f"--metrics-port={triton_config.METRICS_PORT}",
            f"--log-verbose={triton_config.LOG_VERBOSE}"
        ]

        return self.start_service("Triton Server", command)
    
    def start_http_service(self):
        """启动HTTP服务"""
        command = [
            sys.executable, "-m", "uvicorn",
            "app:app",
            "--host", http_config.HOST,
            "--port", str(http_config.PORT),
            "--reload"
        ]

        return self.start_service("HTTP Service", command)
    
    def start_stream_service(self):
        """启动流式服务"""
        command = [
            sys.executable, "-m", "uvicorn",
            "app_stream:app",
            "--host", stream_config.HOST,
            "--port", str(stream_config.PORT),
            "--reload"
        ]

        return self.start_service("Stream Service", command)
    
    def wait_for_services(self):
        """等待服务启动"""
        import requests
        import time
        
        services = [
            ("Triton Server", f"http://localhost:{triton_config.HTTP_PORT}/v2/health/ready"),
            ("HTTP Service", f"http://localhost:{http_config.PORT}/health"),
            ("Stream Service", f"http://localhost:{stream_config.PORT}/health")
        ]
        
        for name, url in services:
            logger.info(f"Waiting for {name} to be ready...")
            for i in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        logger.info(f"{name} is ready!")
                        break
                except:
                    pass
                time.sleep(1)
            else:
                logger.warning(f"{name} may not be ready after 30 seconds")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}, shutting down...")
    if 'manager' in globals():
        manager.stop_all_services()
    sys.exit(0)

def main():
    """主函数"""
    global manager
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("Starting ASR Service Manager...")
    
    # Check dependencies
    required_files = ["app.py", "app_stream.py", "model/zh/unified_asr"]
    for file_path in required_files:
        if not os.path.exists(file_path):
            logger.error(f"Required file/directory not found: {file_path}")
            sys.exit(1)
    
    manager = ServiceManager()
    
    try:
        # Start services in order
        logger.info("Starting services...")
        
        # 1. Start Triton Server first
        triton_process = manager.start_triton_server()
        if not triton_process:
            logger.error("Failed to start Triton Server")
            sys.exit(1)
        
        # Wait a bit for Triton to initialize
        time.sleep(10)
        
        # 2. Start HTTP Service
        http_process = manager.start_http_service()
        if not http_process:
            logger.error("Failed to start HTTP Service")
            manager.stop_all_services()
            sys.exit(1)
        
        # 3. Start Stream Service
        stream_process = manager.start_stream_service()
        if not stream_process:
            logger.error("Failed to start Stream Service")
            manager.stop_all_services()
            sys.exit(1)
        
        # Wait for services to be ready
        time.sleep(5)
        manager.wait_for_services()
        
        logger.info("All services started successfully!")
        logger.info("Service URLs:")
        logger.info(f"  - Triton Server: http://localhost:{triton_config.HTTP_PORT}")
        logger.info(f"  - HTTP API: http://localhost:{http_config.PORT}")
        logger.info(f"  - Stream API: http://localhost:{stream_config.PORT}")
        logger.info(f"  - Stream Demo: http://localhost:{stream_config.PORT}/demo")
        
        # Monitor services
        while manager.running:
            time.sleep(10)
            
            # Check service status
            status = manager.check_services()
            failed_services = [name for name, stat in status.items() if not stat.startswith("running")]
            
            if failed_services:
                logger.warning(f"Failed services detected: {failed_services}")
                
                # Optionally restart failed services
                for service_name in failed_services:
                    logger.info(f"Attempting to restart {service_name}")
                    manager.restart_service(service_name)
                    time.sleep(5)
    
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        manager.stop_all_services()
        logger.info("Service Manager stopped")

if __name__ == "__main__":
    main()
