# -*- coding:utf-8 -*

import os
import sys

class Lat2ug:
    def __init__(self, map_file="/ws/res/map_uyg2lat.txt"):
        assert map_file is not None
        assert os.path.exists(map_file)
        self.map_dict = self.load_lat2ug_dict(map_file)

    def load_lat2ug_dict(self, map_file):
        lat2ug_dict = {}
        with open(map_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                tmp = line.split('\t')
                if len(tmp) == 2:
                    uyghur = tmp[0].strip()
                    latin = tmp[1].strip()
                    if latin not in lat2ug_dict:
                        lat2ug_dict[latin] = uyghur
        return lat2ug_dict

    def lat2ug(self, sent):
        words = sent.strip().split()
        sent = ''
        for subwords in words:
            word = ''
            for lat in subwords:
                if lat in self.map_dict:
                    word += self.map_dict[lat]
                else:
                    # print(lat)
                    continue
            sent = sent + word + ' '
        return sent.strip()


if __name__ =="__main__":
    map_file = "/ws/res/map_uyg2lat.txt"
    l2u = Lat2ug(map_file)

    for line in sys.stdin:
        sent = line.strip().replace("▁", " ")
        ug = l2u.lat2ug(sent)
        print(ug)
