# 俄语语种配置

code: "ru"
name: "Русский"

# 分隔符设置
separator: ", "                   # 俄语半角逗号+空格
silence_threshold: 0.4           # 静音阈值（秒）

# 模型配置
model:
  model_path: "models/ru/encoder.onnx"
  ctc_path: "models/ru/ctc.onnx"
  decoder_path: "models/ru/decoder.onnx"
  vocabulary_path: "models/ru/units.txt"
  
  # 解码参数
  chunk_size: 16
  left_chunks: 16
  decoding_window: 67
  subsampling_rate: 4
  right_context: 7
  
  # 设备设置
  device: "cpu"
  quantized: true

# 特征配置
features:
  enable_punctuation: true      # 启用标点符号预测
  enable_itn: false            # 俄语暂不支持ITN
  enable_hotwords: false       # 俄语暂不支持热词
  enable_modal_particle_filter: false

# 后处理配置
postprocess:
  enable_text_normalization: true
  enable_number_conversion: false
  enable_time_conversion: false
  enable_date_conversion: false

# 性能配置
performance:
  max_sentence_silence: 500    # 最大句子静音时长（毫秒）
  blank_interval: 0.5          # 空白间隔（秒）
