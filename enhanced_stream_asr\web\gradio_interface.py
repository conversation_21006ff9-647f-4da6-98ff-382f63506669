"""
Gradio Web界面
使用Gradio框架提供更稳定的麦克风录音和Web界面
"""

import gradio as gr
import asyncio
import logging
import time
import json
import numpy as np
from typing import Dict, Any, Optional, Tuple, List
import websockets
import base64
import io
import wave

from ..utils.config import ConfigManager
from ..core.audio import AudioUtils

logger = logging.getLogger(__name__)


class GradioInterface:
    """Gradio Web界面"""
    
    def __init__(self, config_manager: ConfigManager, websocket_url: str = "ws://localhost:8080/ws/stream"):
        """
        初始化Gradio界面
        
        Args:
            config_manager: 配置管理器
            websocket_url: WebSocket服务器URL
        """
        self.config_manager = config_manager
        self.websocket_url = websocket_url
        
        # 界面状态
        self.is_recording = False
        self.websocket = None
        self.session_id = None
        self.recognition_results = []
        
        # 音频配置
        self.sample_rate = config_manager.get("audio.sample_rate", 16000)
        self.chunk_duration = config_manager.get("audio.chunk_duration", 0.4)
        
        # 支持的语种
        self.supported_languages = config_manager.get_supported_languages()
        self.language_names = {
            'zh': '中文',
            'en': 'English', 
            'ru': 'Русский',
            'ug': 'ئۇيغۇرچە',
            'kk': 'Қазақша'
        }
        
    def create_interface(self) -> gr.Blocks:
        """创建Gradio界面"""
        
        with gr.Blocks(
            title="Enhanced Stream ASR",
            theme=gr.themes.Soft(),
            css=self._get_custom_css()
        ) as interface:
            
            # 标题和描述
            gr.Markdown("""
            # 🎤 Enhanced Stream ASR
            ## 增强流式语音识别系统
            
            支持多语种自动识别、实时语音转文字、可配置分隔符等功能
            """)
            
            with gr.Row():
                with gr.Column(scale=1):
                    # 配置面板
                    gr.Markdown("### ⚙️ 配置设置")
                    
                    language_mode = gr.Radio(
                        choices=["auto", "manual"],
                        value="auto",
                        label="语种模式",
                        info="自动识别或手动选择语种"
                    )
                    
                    selected_language = gr.Dropdown(
                        choices=[(self.language_names.get(lang, lang), lang) for lang in self.supported_languages],
                        value="zh",
                        label="选择语种",
                        visible=False,
                        info="手动模式下选择识别语种"
                    )
                    
                    custom_separator = gr.Textbox(
                        label="自定义分隔符",
                        placeholder="留空使用默认分隔符",
                        info="自定义语音间隔的分隔符"
                    )
                    
                    enable_intermediate = gr.Checkbox(
                        value=True,
                        label="显示中间结果",
                        info="显示实时识别的中间结果"
                    )
                    
                    enable_punctuation = gr.Checkbox(
                        value=True,
                        label="标点符号预测",
                        info="启用标点符号自动预测"
                    )
                    
                with gr.Column(scale=2):
                    # 录音控制面板
                    gr.Markdown("### 🎙️ 录音控制")
                    
                    with gr.Row():
                        start_btn = gr.Button("🎤 开始录音", variant="primary", size="lg")
                        stop_btn = gr.Button("⏹️ 停止录音", variant="secondary", size="lg")
                        clear_btn = gr.Button("🗑️ 清空结果", variant="secondary")
                    
                    # 状态显示
                    with gr.Row():
                        connection_status = gr.Textbox(
                            value="未连接",
                            label="连接状态",
                            interactive=False
                        )
                        recording_status = gr.Textbox(
                            value="未录音",
                            label="录音状态", 
                            interactive=False
                        )
                        detected_language = gr.Textbox(
                            value="-",
                            label="检测语种",
                            interactive=False
                        )
                    
                    # 音频输入组件
                    audio_input = gr.Audio(
                        sources=["microphone"],
                        type="numpy",
                        label="麦克风输入",
                        streaming=True,
                        show_label=False
                    )
            
            # 识别结果显示
            gr.Markdown("### 📝 识别结果")
            
            results_display = gr.Textbox(
                label="识别文本",
                lines=10,
                max_lines=20,
                interactive=False,
                placeholder="点击'开始录音'开始语音识别..."
            )
            
            # 会话信息
            with gr.Row():
                session_info = gr.JSON(
                    label="会话信息",
                    value={}
                )
                
                system_stats = gr.JSON(
                    label="系统统计",
                    value={}
                )
            
            # 事件绑定
            language_mode.change(
                fn=self._toggle_language_selection,
                inputs=[language_mode],
                outputs=[selected_language]
            )
            
            start_btn.click(
                fn=self._start_recording,
                inputs=[language_mode, selected_language, custom_separator, enable_intermediate, enable_punctuation],
                outputs=[connection_status, recording_status, start_btn, stop_btn]
            )
            
            stop_btn.click(
                fn=self._stop_recording,
                outputs=[recording_status, start_btn, stop_btn, session_info]
            )
            
            clear_btn.click(
                fn=self._clear_results,
                outputs=[results_display, session_info]
            )
            
            # 音频流处理
            audio_input.stream(
                fn=self._process_audio_stream,
                inputs=[audio_input],
                outputs=[results_display, detected_language],
                stream_every=0.5  # 每0.5秒处理一次
            )
            
            # 定期更新统计信息
            interface.load(
                fn=self._update_stats,
                outputs=[system_stats],
                every=5  # 每5秒更新一次
            )
            
        return interface
        
    def _get_custom_css(self) -> str:
        """获取自定义CSS样式"""
        return """
        .gradio-container {
            max-width: 1200px !important;
        }
        
        .recording-active {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .status-connected {
            color: #10b981 !important;
        }
        
        .status-recording {
            color: #f59e0b !important;
        }
        
        .status-error {
            color: #ef4444 !important;
        }
        """
        
    def _toggle_language_selection(self, language_mode: str) -> gr.Dropdown:
        """切换语种选择显示"""
        return gr.Dropdown(visible=(language_mode == "manual"))
        
    async def _start_recording(
        self,
        language_mode: str,
        selected_language: str,
        custom_separator: str,
        enable_intermediate: bool,
        enable_punctuation: bool
    ) -> Tuple[str, str, gr.Button, gr.Button]:
        """开始录音"""
        try:
            # 连接WebSocket
            await self._connect_websocket()
            
            # 发送握手请求
            handshake_request = {
                "type": "handshake_request",
                "client_id": f"gradio_{int(time.time())}",
                "auto_language_detection": (language_mode == "auto"),
                "language": selected_language if language_mode == "manual" else None,
                "sample_rate": self.sample_rate,
                "channels": 1,
                "enable_intermediate_result": enable_intermediate,
                "enable_punctuation": enable_punctuation,
                "custom_separator": custom_separator if custom_separator.strip() else None
            }
            
            await self.websocket.send(json.dumps(handshake_request))
            
            # 等待握手响应
            response = await self.websocket.recv()
            response_data = json.loads(response)
            
            if response_data.get("status") == "success":
                self.session_id = response_data.get("session_id")
                self.is_recording = True
                
                return (
                    "已连接",
                    "录音中",
                    gr.Button(interactive=False),  # 禁用开始按钮
                    gr.Button(interactive=True)    # 启用停止按钮
                )
            else:
                error_msg = response_data.get("message", "握手失败")
                logger.error(f"Handshake failed: {error_msg}")
                return (
                    f"连接失败: {error_msg}",
                    "未录音",
                    gr.Button(interactive=True),
                    gr.Button(interactive=False)
                )
                
        except Exception as e:
            logger.error(f"Start recording failed: {e}")
            return (
                f"连接错误: {str(e)}",
                "未录音",
                gr.Button(interactive=True),
                gr.Button(interactive=False)
            )
            
    async def _stop_recording(self) -> Tuple[str, gr.Button, gr.Button, Dict[str, Any]]:
        """停止录音"""
        try:
            if self.websocket and self.session_id:
                # 发送停止录音请求
                stop_request = {
                    "type": "stop_recording",
                    "session_id": self.session_id
                }
                await self.websocket.send(json.dumps(stop_request))
                
                # 发送断开连接请求
                disconnect_request = {
                    "type": "disconnect_request",
                    "session_id": self.session_id
                }
                await self.websocket.send(json.dumps(disconnect_request))
                
                # 关闭连接
                await self.websocket.close()
                
            self.is_recording = False
            self.websocket = None
            
            # 生成会话信息
            session_info = {
                "session_id": self.session_id,
                "recognition_count": len(self.recognition_results),
                "total_text_length": sum(len(r.get("text", "")) for r in self.recognition_results),
                "session_duration": "已结束"
            }
            
            return (
                "未录音",
                gr.Button(interactive=True),   # 启用开始按钮
                gr.Button(interactive=False),  # 禁用停止按钮
                session_info
            )
            
        except Exception as e:
            logger.error(f"Stop recording failed: {e}")
            return (
                "停止失败",
                gr.Button(interactive=True),
                gr.Button(interactive=False),
                {}
            )
            
    def _clear_results(self) -> Tuple[str, Dict[str, Any]]:
        """清空结果"""
        self.recognition_results.clear()
        return ("", {})
        
    async def _process_audio_stream(self, audio_data) -> Tuple[str, str]:
        """处理音频流"""
        if not self.is_recording or not self.websocket or not audio_data:
            return gr.update(), gr.update()
            
        try:
            # 转换音频数据
            sample_rate, audio_array = audio_data
            
            # 重采样到目标采样率（如果需要）
            if sample_rate != self.sample_rate:
                audio_array = self._resample_audio(audio_array, sample_rate, self.sample_rate)
                
            # 转换为PCM16格式
            if audio_array.dtype != np.int16:
                audio_array = (audio_array * 32767).astype(np.int16)
                
            # 编码为base64
            audio_bytes = audio_array.tobytes()
            audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
            
            # 发送音频数据
            audio_message = {
                "type": "audio_data",
                "session_id": self.session_id,
                "sequence_id": len(self.recognition_results),
                "audio_data": audio_base64,
                "is_final": False
            }
            
            await self.websocket.send(json.dumps(audio_message))
            
            # 接收识别结果
            try:
                response = await asyncio.wait_for(self.websocket.recv(), timeout=1.0)
                response_data = json.loads(response)
                
                if response_data.get("type") in ["recognition_result", "intermediate_result"]:
                    text = response_data.get("text", "")
                    language = response_data.get("language", "")
                    is_final = response_data.get("is_final", False)
                    
                    if text:
                        # 更新识别结果
                        if is_final:
                            self.recognition_results.append(response_data)
                            
                        # 构建显示文本
                        display_text = self._build_display_text()
                        
                        # 显示检测到的语种
                        language_display = self.language_names.get(language, language) if language else "-"
                        
                        return display_text, language_display
                        
            except asyncio.TimeoutError:
                # 没有收到响应，继续
                pass
                
        except Exception as e:
            logger.error(f"Audio stream processing error: {e}")
            
        return gr.update(), gr.update()
        
    def _resample_audio(self, audio: np.ndarray, source_rate: int, target_rate: int) -> np.ndarray:
        """重采样音频"""
        if source_rate == target_rate:
            return audio
            
        # 简单的重采样（生产环境建议使用librosa或scipy）
        ratio = target_rate / source_rate
        new_length = int(len(audio) * ratio)
        
        # 线性插值重采样
        indices = np.linspace(0, len(audio) - 1, new_length)
        resampled = np.interp(indices, np.arange(len(audio)), audio)
        
        return resampled.astype(audio.dtype)
        
    def _build_display_text(self) -> str:
        """构建显示文本"""
        if not self.recognition_results:
            return ""
            
        # 合并所有最终结果
        texts = []
        for result in self.recognition_results:
            if result.get("is_final", False):
                text = result.get("text", "").strip()
                if text:
                    texts.append(text)
                    
        return " ".join(texts)
        
    async def _connect_websocket(self):
        """连接WebSocket"""
        try:
            self.websocket = await websockets.connect(self.websocket_url)
            logger.info(f"Connected to WebSocket: {self.websocket_url}")
        except Exception as e:
            logger.error(f"WebSocket connection failed: {e}")
            raise
            
    def _update_stats(self) -> Dict[str, Any]:
        """更新系统统计信息"""
        try:
            # 这里可以调用实际的API获取统计信息
            return {
                "current_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "supported_languages": len(self.supported_languages),
                "sample_rate": self.sample_rate,
                "websocket_url": self.websocket_url
            }
        except Exception as e:
            logger.error(f"Failed to update stats: {e}")
            return {}
            
    def launch(self, **kwargs):
        """启动Gradio界面"""
        interface = self.create_interface()
        
        # 默认启动参数
        launch_kwargs = {
            "server_name": "0.0.0.0",
            "server_port": 7860,
            "share": False,
            "debug": False,
            **kwargs
        }
        
        logger.info(f"Launching Gradio interface on {launch_kwargs['server_name']}:{launch_kwargs['server_port']}")
        
        return interface.launch(**launch_kwargs)
