#!/usr/bin/env python3
"""
FastAPI HTTP服务接口 - ASR文件转写服务
基于竞品功能实现的语音识别HTTP API
"""

import asyncio
import json
import logging
import os
import sys
import tempfile
import time
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiofiles
import numpy as np
import requests
import uvicorn
from fastapi import BackgroundTasks, FastAPI, File, Form, HTTPException, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Add tools to path
sys.path.append('src/utils')
from audio_utils import set_temp_dir, load_from_local_path
from model_repo_utils import TritonGRPCClient

# Load configuration
from config_loader import http_config, setup_logging, triton_config

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Ensure upload directory exists
os.makedirs(http_config.UPLOAD_DIR, exist_ok=True)
set_temp_dir(http_config.UPLOAD_DIR)

# Global Triton client
triton_client = TritonGRPCClient(triton_config.GRPC_URL, triton_config.MODEL_NAME, logger)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    生命周期管理函数, 在应用启动时初始化资源, 在关闭时释放资源。
    Args:
        app: FastAPI 应用实例
    Returns:
        异步上下文管理器
    """
    logger.info("Starting ASR HTTP Service...")
    # 启动
    try:
        triton_client.connect()
        logger.info("ASR HTTP Service started successfully")
    except Exception as e:
        logger.error(f"Failed to start service: {e}")
    
    yield

    # 关闭
    logger.info("Closing ASR HTTP Service...")

# FastAPI app
version_info = "*********"
app = FastAPI(
    lifespan=lifespan,
    title="ASR文件转写服务",
    description="基于Triton的语音文件识别HTTP API服务",
    version=version_info
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Request/Response models
class TranscriptionRequest(BaseModel):
    """转写请求模型"""
    audio_url: Optional[str] = Field(None, description="音频文件URL")
    language: str = Field("zh", description="语言代码")
    enable_words: bool = Field(False, description="是否返回词级别时间戳")
    enable_confidence: bool = Field(True, description="是否返回置信度")
    enable_volume: bool = Field(True, description="是否返回音量信息")
    hotwords: Optional[List[str]] = Field(None, description="热词列表")
    forbidden_words: Optional[List[str]] = Field(None, description="敏感词列表")
    correction_words: Optional[Dict[str, str]] = Field(None, description="强制替换词典")

class TranscriptionResponse(BaseModel):
    """转写响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态: created/processing/completed/failed")
    text: Optional[str] = Field(None, description="识别结果文本")
    segments: Optional[List[Dict]] = Field(None, description="分段结果")
    duration: Optional[float] = Field(None, description="音频时长(秒)")
    confidence: Optional[float] = Field(None, description="整体置信度")
    volume: Optional[int] = Field(None, description="音量(0-100)")
    language: Optional[str] = Field(None, description="检测到的语言")
    cost_time: Optional[str] = Field(None, description="处理耗时")
    error_message: Optional[str] = Field(None, description="错误信息")

class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str
    status: str
    progress: Optional[int] = None
    result: Optional[Dict] = None
    error: Optional[str] = None
    created_at: datetime
    updated_at: datetime

# Global task storage (in production, use Redis or database)
task_storage: Dict[str, TaskStatus] = {}

async def load_audio_from_url(url: str) -> np.ndarray:
    """
    从URL加载音频文件

    Args:
        url (str): 音频文件的URL地址

    Returns:
        np.ndarray: 音频数据数组，格式为float32

    Raises:
        HTTPException: 当音频加载失败时抛出400错误
    """
    try:
        # Download audio file
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            tmp_file.write(response.content)
            tmp_path = tmp_file.name
        
        try:
            # Load audio using tools
            waveform, sample_rate = load_from_local_path(tmp_path)
            
            # Convert to numpy array
            if hasattr(waveform, 'numpy'):
                audio_data = waveform.numpy().flatten()
            else:
                audio_data = waveform.flatten()
            
            return audio_data.astype(np.float32)
            
        finally:
            # Clean up temporary file
            os.unlink(tmp_path)
            
    except Exception as e:
        logger.error(f"Failed to load audio from URL {url}: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to load audio: {str(e)}")

async def load_audio_from_file(file_path: str) -> np.ndarray:
    """
    从本地文件加载音频

    Args:
        file_path (str): 音频文件的本地路径

    Returns:
        np.ndarray: 音频数据数组，格式为float32

    Raises:
        HTTPException: 当音频加载失败时抛出400错误
    """
    try:
        
        waveform, sample_rate = load_from_local_path(file_path)
        
        # Convert to numpy array
        if hasattr(waveform, 'numpy'):
            audio_data = waveform.numpy().flatten()
        else:
            audio_data = waveform.flatten()
        
        return audio_data.astype(np.float32)
        
    except Exception as e:
        logger.error(f"Failed to load audio from file {file_path}: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to load audio: {str(e)}")

async def process_transcription_task(task_id: str, audio_data: np.ndarray, request: TranscriptionRequest) -> None:
    """
    处理转写任务（后台异步执行）

    Args:
        task_id (str): 任务ID
        audio_data (np.ndarray): 音频数据
        request (TranscriptionRequest): 转写请求参数
    """
    try:
        # Update task status
        task_storage[task_id].status = "processing"
        task_storage[task_id].progress = 10
        task_storage[task_id].updated_at = datetime.now()
        
        # Perform transcription
        result = await triton_client.transcribe(audio_data, request.language)
        
        # Post-process result
        processed_result = {
            "text": result.get("text", ""),
            "segments": result.get("segments", []),
            "duration": result.get("duration", 0),
            "language": request.language,
            "cost_time": result.get("info", {}).get("cost_time", "0 s")
        }
        
        # Add optional features
        if "confidence" in result:
            processed_result["confidence"] = result["confidence"]
        if "volume" in result:
            processed_result["volume"] = result["volume"]
        
        # Update task with result
        task_storage[task_id].status = "completed"
        task_storage[task_id].progress = 100
        task_storage[task_id].result = processed_result
        task_storage[task_id].updated_at = datetime.now()
        
        logger.info(f"Task {task_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Task {task_id} failed: {e}")
        task_storage[task_id].status = "failed"
        task_storage[task_id].error = str(e)
        task_storage[task_id].updated_at = datetime.now()


@app.get("/")
async def root() -> Dict[str, str]:
    """
    根路径接口

    Returns:
        Dict[str, str]: 服务基本信息
    """
    return {"message": "ASR文件转写服务", "version": version_info, "status": "running"}

@app.get("/health")
async def health_check() -> Dict[str, str]:
    """
    健康检查接口

    Returns:
        Dict[str, str]: 服务健康状态
    """
    try:
        if triton_client.client and triton_client.client.is_model_ready(triton_config.MODEL_NAME):
            return {"status": "healthy", "triton": "connected", "model": "ready"}
        else:
            return {"status": "unhealthy", "triton": "disconnected", "model": "not_ready"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}

@app.post("/transcribe/file", response_model=TranscriptionResponse)
async def transcribe_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    language: str = Form("zh"),
    enable_words: bool = Form(False),
    enable_confidence: bool = Form(True),
    enable_volume: bool = Form(True),
    hotwords: Optional[str] = Form(None),
    forbidden_words: Optional[str] = Form(None),
    correction_words: Optional[str] = Form(None)
) -> TranscriptionResponse:
    """
    文件上传转写接口（异步处理）

    Args:
        background_tasks (BackgroundTasks): FastAPI后台任务管理器
        file (UploadFile): 上传的音频文件
        language (str): 语言代码，默认"zh"
        enable_words (bool): 是否返回词级别时间戳，默认False
        enable_confidence (bool): 是否返回置信度，默认True
        enable_volume (bool): 是否返回音量信息，默认True
        hotwords (Optional[str]): 热词列表（JSON字符串格式）
        forbidden_words (Optional[str]): 敏感词列表（JSON字符串格式）
        correction_words (Optional[str]): 强制替换词典（JSON字符串格式）

    Returns:
        TranscriptionResponse: 转写响应，包含task_id和初始状态

    Raises:
        HTTPException: 文件格式不支持、文件过大或处理失败时抛出错误
    """

    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in http_config.SUPPORTED_FORMATS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported: {http_config.SUPPORTED_FORMATS}"
        )

    # Check file size
    file_size = 0
    content = await file.read()
    file_size = len(content)

    if file_size > http_config.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File too large. Max size: {http_config.MAX_FILE_SIZE // 1024 // 1024}MB"
        )

    # Generate task ID
    task_id = str(uuid.uuid4())

    # Save uploaded file
    file_path = os.path.join(http_config.UPLOAD_DIR, f"{task_id}_{file.filename}")
    async with aiofiles.open(file_path, 'wb') as f:
        await f.write(content)

    try:
        # Load audio
        audio_data = await load_audio_from_file(file_path)

        # Parse optional parameters
        request_data = {
            "language": language,
            "enable_words": enable_words,
            "enable_confidence": enable_confidence,
            "enable_volume": enable_volume
        }

        if hotwords:
            request_data["hotwords"] = json.loads(hotwords) if isinstance(hotwords, str) else hotwords
        if forbidden_words:
            request_data["forbidden_words"] = json.loads(forbidden_words) if isinstance(forbidden_words, str) else forbidden_words
        if correction_words:
            request_data["correction_words"] = json.loads(correction_words) if isinstance(correction_words, str) else correction_words

        request = TranscriptionRequest(**request_data)

        # Create task
        task_storage[task_id] = TaskStatus(
            task_id=task_id,
            status="created",
            progress=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        # Start background processing
        background_tasks.add_task(process_transcription_task, task_id, audio_data, request)

        return TranscriptionResponse(
            task_id=task_id,
            status="processing"
        )

    except Exception as e:
        # Clean up file
        if os.path.exists(file_path):
            os.unlink(file_path)
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # Clean up uploaded file
        if os.path.exists(file_path):
            os.unlink(file_path)

@app.post("/transcribe/url", response_model=TranscriptionResponse)
async def transcribe_url(
    background_tasks: BackgroundTasks,
    request: TranscriptionRequest
) -> TranscriptionResponse:
    """
    URL转写接口（异步处理）

    Args:
        background_tasks (BackgroundTasks): FastAPI后台任务管理器
        request (TranscriptionRequest): 转写请求参数，必须包含audio_url

    Returns:
        TranscriptionResponse: 转写响应，包含task_id和初始状态

    Raises:
        HTTPException: URL无效、音频加载失败或处理失败时抛出错误
    """

    if not request.audio_url:
        raise HTTPException(status_code=400, detail="audio_url (url, eg. audio_url=http://example/to/audio/file.wav) is required")

    # Generate task ID
    task_id = str(uuid.uuid4())

    try:
        # Load audio from URL
        assert request.audio_url.startswith('http'), "`/transcribe/url` only suport http://... file"
        audio_data = await load_audio_from_url(request.audio_url)

        # Create task
        task_storage[task_id] = TaskStatus(
            task_id=task_id,
            status="created",
            progress=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        # Start background processing
        background_tasks.add_task(process_transcription_task, task_id, audio_data, request)

        return TranscriptionResponse(
            task_id=task_id,
            status="processing"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/transcribe/result/{task_id}", response_model=TranscriptionResponse)
async def get_transcription_result(task_id: str) -> TranscriptionResponse:
    """
    获取转写结果接口

    Args:
        task_id (str): 任务ID

    Returns:
        TranscriptionResponse: 转写结果，包含状态和结果数据

    Raises:
        HTTPException: 任务不存在时抛出404错误
    """

    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task = task_storage[task_id]

    response_data = {
        "task_id": task_id,
        "status": task.status
    }

    if task.result:
        response_data.update(task.result)

    if task.error:
        response_data["error_message"] = task.error

    return TranscriptionResponse(**response_data)

@app.delete("/transcribe/delete/{task_id}")
async def delete_transcription_result(task_id: str) -> Dict[str, str]:
    """
    删除转写结果接口

    Args:
        task_id (str): 任务ID

    Returns:
        Dict[str, str]: 删除结果消息

    Raises:
        HTTPException: 任务不存在时抛出404错误
    """

    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    del task_storage[task_id]
    return {"message": "Task deleted successfully"}

@app.get("/transcribe/tasks")
async def list_transcription_tasks() -> Dict[str, Any]:
    """
    列出所有转写任务接口

    Returns:
        Dict[str, Any]: 包含任务列表和总数的字典
    """

    tasks = []
    for task_id, task in task_storage.items():
        tasks.append({
            "task_id": task_id,
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at,
            "updated_at": task.updated_at
        })

    return {"tasks": tasks, "total": len(tasks)}

@app.post("/transcribe/sync", response_model=TranscriptionResponse)
async def transcribe_sync(
    file: UploadFile = File(...),
    language: str = Form("zh"),
    enable_confidence: bool = Form(True),
    enable_volume: bool = Form(True)
) -> TranscriptionResponse:
    """
    同步转写接口（立即返回结果）

    Args:
        file (UploadFile): 上传的音频文件
        language (str): 语言代码，默认"zh"
        enable_confidence (bool): 是否返回置信度，默认True
        enable_volume (bool): 是否返回音量信息，默认True

    Returns:
        TranscriptionResponse: 转写结果，状态为completed

    Raises:
        HTTPException: 文件格式不支持、文件过大或处理失败时抛出错误

    Note:
        此接口适用于小文件（<10MB），会立即返回转写结果
    """

    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in http_config.SUPPORTED_FORMATS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported: {http_config.SUPPORTED_FORMATS}"
        )

    # Check file size (smaller limit for sync processing)
    content = await file.read()
    if len(content) > 10 * 1024 * 1024:  # 10MB limit for sync
        raise HTTPException(status_code=400, detail="File too large for sync processing (max 10MB)")

    # Generate task ID
    task_id = str(uuid.uuid4())

    # Save uploaded file temporarily
    file_path = os.path.join(http_config.UPLOAD_DIR, f"sync_{task_id}_{file.filename}")
    async with aiofiles.open(file_path, 'wb') as f:
        await f.write(content)

    try:
        # Load and process audio
        audio_data = await load_audio_from_file(file_path)

        # Perform transcription
        result = await triton_client.transcribe(audio_data, language)

        # Format response
        response_data = {
            "task_id": task_id,
            "status": "completed",
            "text": result.get("text", ""),
            "segments": result.get("segments", []),
            "duration": result.get("duration", 0),
            "language": language,
            "cost_time": result.get("info", {}).get("cost_time", "0 s")
        }

        # Add optional features
        if enable_confidence and "confidence" in result:
            response_data["confidence"] = result["confidence"]
        if enable_volume and "volume" in result:
            response_data["volume"] = result["volume"]

        logger.info(f"Task (sync) {task_id} completed successfully")
        
        return TranscriptionResponse(**response_data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # Clean up temporary file
        if os.path.exists(file_path):
            os.unlink(file_path)

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host=http_config.HOST,
        port=http_config.PORT,
        reload=True,
        log_level="info"
    )
