<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Stream ASR - 增强流式语音识别</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-microphone"></i> Enhanced Stream ASR</h1>
            <p>增强流式语音识别系统 - 支持自动语种识别</p>
        </header>

        <!-- 主控制面板 -->
        <div class="control-panel">
            <div class="settings-section">
                <h3><i class="fas fa-cog"></i> 设置</h3>
                
                <!-- 语种设置 -->
                <div class="setting-group">
                    <label for="languageMode">语种模式:</label>
                    <select id="languageMode">
                        <option value="auto">自动识别</option>
                        <option value="manual">手动选择</option>
                    </select>
                </div>
                
                <div class="setting-group" id="manualLanguageGroup" style="display: none;">
                    <label for="selectedLanguage">选择语种:</label>
                    <select id="selectedLanguage">
                        <option value="zh">中文</option>
                        <option value="en">English</option>
                        <option value="ru">Русский</option>
                        <option value="ug">ئۇيغۇرچە</option>
                        <option value="kk">Қазақша</option>
                    </select>
                </div>
                
                <!-- 音频设置 -->
                <div class="setting-group">
                    <label for="sampleRate">采样率:</label>
                    <select id="sampleRate">
                        <option value="16000" selected>16000 Hz</option>
                        <option value="44100">44100 Hz</option>
                        <option value="48000">48000 Hz</option>
                    </select>
                </div>
                
                <!-- 识别设置 -->
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="enableIntermediate" checked>
                        显示中间结果
                    </label>
                </div>
                
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="enablePunctuation" checked>
                        标点符号预测
                    </label>
                </div>
                
                <div class="setting-group">
                    <label for="customSeparator">自定义分隔符:</label>
                    <input type="text" id="customSeparator" placeholder="留空使用默认">
                </div>
            </div>
            
            <!-- 录音控制 -->
            <div class="recording-section">
                <h3><i class="fas fa-record-vinyl"></i> 录音控制</h3>
                
                <div class="recording-controls">
                    <button id="startBtn" class="btn btn-start">
                        <i class="fas fa-play"></i> 开始录音
                    </button>
                    <button id="stopBtn" class="btn btn-stop" disabled>
                        <i class="fas fa-stop"></i> 停止录音
                    </button>
                    <button id="clearBtn" class="btn btn-clear">
                        <i class="fas fa-trash"></i> 清空结果
                    </button>
                </div>
                
                <!-- 状态显示 -->
                <div class="status-section">
                    <div class="status-item">
                        <span class="status-label">连接状态:</span>
                        <span id="connectionStatus" class="status-value disconnected">未连接</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">录音状态:</span>
                        <span id="recordingStatus" class="status-value">未录音</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">检测语种:</span>
                        <span id="detectedLanguage" class="status-value">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">音频时长:</span>
                        <span id="audioDuration" class="status-value">0.0s</span>
                    </div>
                </div>
                
                <!-- 音量指示器 -->
                <div class="volume-indicator">
                    <span class="volume-label">音量:</span>
                    <div class="volume-bar">
                        <div id="volumeLevel" class="volume-level"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 识别结果显示 -->
        <div class="results-section">
            <h3><i class="fas fa-comments"></i> 识别结果</h3>
            <div id="resultsContainer" class="results-container">
                <div class="no-results">
                    <i class="fas fa-microphone-slash"></i>
                    <p>点击"开始录音"开始语音识别</p>
                </div>
            </div>
        </div>

        <!-- 会话信息 -->
        <div class="session-info">
            <h3><i class="fas fa-info-circle"></i> 会话信息</h3>
            <div id="sessionInfo" class="session-details">
                <div class="info-item">
                    <span class="info-label">会话ID:</span>
                    <span id="sessionId" class="info-value">-</span>
                </div>
                <div class="info-item">
                    <span class="info-label">识别次数:</span>
                    <span id="recognitionCount" class="info-value">0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">平均延迟:</span>
                    <span id="avgLatency" class="info-value">-</span>
                </div>
                <div class="info-item">
                    <span class="info-label">总音频时长:</span>
                    <span id="totalAudioTime" class="info-value">0.0s</span>
                </div>
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="logs-section">
            <h3>
                <i class="fas fa-terminal"></i> 系统日志
                <button id="toggleLogs" class="btn-toggle">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </h3>
            <div id="logsContainer" class="logs-container" style="display: none;">
                <div id="logsList" class="logs-list"></div>
                <button id="clearLogs" class="btn btn-small">清空日志</button>
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="js/websocket-client.js"></script>
    <script src="js/audio-recorder.js"></script>
    <script src="js/ui-controller.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
