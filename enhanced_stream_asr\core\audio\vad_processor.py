"""
VAD语音活动检测处理器
基于webrtcvad的语音检测功能，优化用于流式处理
"""

import logging
import collections
from typing import Optional, Tuple, List
import numpy as np
import torch
import webrtcvad

from ...utils.exceptions import VADError, ErrorCodes, ErrorHandler
from ...utils.monitoring.performance_monitor import PerformanceContext, global_performance_monitor

logger = logging.getLogger(__name__)


class Frame:
    """音频帧对象"""
    
    def __init__(self, bytes_data: bytes, timestamp: float, duration: float):
        self.bytes = bytes_data
        self.timestamp = timestamp
        self.duration = duration


class VADProcessor:
    """VAD语音活动检测处理器"""
    
    def __init__(self, config: dict):
        """
        初始化VAD处理器
        
        Args:
            config: VAD配置参数
        """
        self.vad_type = config.get('vad_type', 'webrtcvad')
        self.vad_level = config.get('vad_level', 1)
        self.frame_length = config.get('frame_length', 30)  # ms
        self.window_size = config.get('window_size', 10)
        self.seg_threshold = config.get('seg_threshold', 0.9)
        self.sample_rate = config.get('sample_rate', 16000)
        
        # 初始化VAD
        if self.vad_type == 'webrtcvad':
            self.vad = webrtcvad.Vad(self.vad_level)
        else:
            raise ValueError(f"Unsupported VAD type: {self.vad_type}")
            
        logger.info(f"VAD processor initialized with level {self.vad_level}")
        
    def is_speech_chunk(self, audio_chunk: np.ndarray, sample_rate: int = None) -> bool:
        """
        检测音频块是否包含语音
        
        Args:
            audio_chunk: 音频数据 (numpy array)
            sample_rate: 采样率
            
        Returns:
            bool: 是否包含语音
        """
        if sample_rate is None:
            sample_rate = self.sample_rate
            
        try:
            # 转换为torch tensor如果需要
            if isinstance(audio_chunk, torch.Tensor):
                audio_chunk = audio_chunk.numpy()
                
            # 确保是1维数组
            if audio_chunk.ndim > 1:
                audio_chunk = audio_chunk.flatten()
                
            # 转换为int16格式
            if audio_chunk.dtype != np.int16:
                if audio_chunk.max() <= 1.0:
                    # 假设是归一化的浮点数
                    audio_chunk = (audio_chunk * 32767).astype(np.int16)
                else:
                    audio_chunk = audio_chunk.astype(np.int16)
                    
            # 检查音频长度是否符合要求
            frame_samples = int(sample_rate * self.frame_length / 1000)
            if len(audio_chunk) < frame_samples:
                # 如果音频太短，填充零
                padded_audio = np.zeros(frame_samples, dtype=np.int16)
                padded_audio[:len(audio_chunk)] = audio_chunk
                audio_chunk = padded_audio
            elif len(audio_chunk) > frame_samples:
                # 如果音频太长，截取
                audio_chunk = audio_chunk[:frame_samples]
                
            # 转换为bytes
            audio_bytes = audio_chunk.tobytes()
            
            # 使用WebRTC VAD检测
            return self.vad.is_speech(audio_bytes, sample_rate)
            
        except Exception as e:
            ErrorHandler.handle_and_log_exception(
                logger, e, ErrorCodes.AUDIO_DECODING_FAILED,
                context="VAD speech detection"
            )
            return False
            
    def detect_speech_segments(self, audio: np.ndarray, sample_rate: int = None) -> List[Tuple[float, float]]:
        """
        检测音频中的语音段
        
        Args:
            audio: 完整音频数据
            sample_rate: 采样率
            
        Returns:
            List[Tuple[float, float]]: 语音段的开始和结束时间戳列表 (秒)
        """
        if sample_rate is None:
            sample_rate = self.sample_rate

        session_id = getattr(self, 'session_id', 'unknown')
        with PerformanceContext(global_performance_monitor, session_id, "vad_detect_segments") as perf_ctx:
            try:
                # 生成音频帧
                frames = list(self._frame_generator(audio, sample_rate))

                # 检测语音段
                segments = list(self._vad_collector(frames, sample_rate))

                # 转换为时间戳格式
                speech_segments = []
                for _, start_time, end_time in segments:
                    speech_segments.append((start_time / 1000.0, end_time / 1000.0))

                return speech_segments

            except Exception as e:
                perf_ctx.mark_error(str(e))
                logger.error(f"Speech segment detection failed: {e}")
                return []
            
    def get_first_speech_timestamp(self, audio: np.ndarray, sample_rate: int = None) -> Optional[float]:
        """
        获取第一个语音起始点的时间戳
        
        Args:
            audio: 音频数据
            sample_rate: 采样率
            
        Returns:
            Optional[float]: 第一个语音起始时间戳(秒)，如果没有检测到语音返回None
        """
        if sample_rate is None:
            sample_rate = self.sample_rate
            
        try:
            frames = self._frame_generator(audio, sample_rate)
            ring_buffer = collections.deque(maxlen=self.window_size)
            
            for frame in frames:
                is_speech = self.vad.is_speech(frame.bytes, sample_rate)
                ring_buffer.append((frame, is_speech))
                
                num_voiced = len([f for f, speech in ring_buffer if speech])
                if num_voiced > self.seg_threshold * ring_buffer.maxlen:
                    return ring_buffer[0][0].timestamp / 1000.0
                    
            return None
            
        except Exception as e:
            logger.error(f"First speech timestamp detection failed: {e}")
            return None
            
    def _frame_generator(self, audio: np.ndarray, sample_rate: int):
        """生成音频帧"""
        # 确保音频是正确的格式
        if isinstance(audio, torch.Tensor):
            audio = audio.numpy()
            
        if audio.ndim > 1:
            audio = audio.flatten()
            
        # 转换为int16
        if audio.dtype != np.int16:
            if audio.max() <= 1.0:
                audio = (audio * 32767).astype(np.int16)
            else:
                audio = audio.astype(np.int16)
                
        frame_length = int(sample_rate * (self.frame_length / 1000.0))
        offset, timestamp = 0, 0
        
        while offset + frame_length <= len(audio):
            frame_data = audio[offset:offset + frame_length]
            frame_bytes = frame_data.tobytes()
            yield Frame(frame_bytes, timestamp, self.frame_length)
            timestamp += self.frame_length
            offset += frame_length
            
    def _vad_collector(self, frames, sample_rate: int):
        """收集语音段"""
        triggered = False
        voiced_frames = []
        ring_buffer = collections.deque(maxlen=self.window_size)
        max_segment_frames = 30000  # 限制单个语音段的最大帧数，防止内存泄漏

        for frame in frames:
            is_speech = self.vad.is_speech(frame.bytes, sample_rate)

            if not triggered:
                ring_buffer.append((frame, is_speech))
                num_voiced = len([f for f, speech in ring_buffer if speech])
                if num_voiced > self.seg_threshold * ring_buffer.maxlen:
                    triggered = True
                    for f, s in ring_buffer:
                        voiced_frames.append(f)
                    ring_buffer.clear()
            else:
                voiced_frames.append(frame)
                ring_buffer.append((frame, is_speech))
                num_unvoiced = len([f for f, speech in ring_buffer if not speech])

                # 检查是否需要结束当前语音段
                should_end_segment = (
                    num_unvoiced > self.seg_threshold * ring_buffer.maxlen or
                    len(voiced_frames) >= max_segment_frames  # 防止单个段过长
                )

                if should_end_segment:
                    triggered = False
                    yield (
                        b''.join([f.bytes for f in voiced_frames]),
                        voiced_frames[0].timestamp,
                        voiced_frames[-1].timestamp + voiced_frames[-1].duration
                    )
                    ring_buffer.clear()
                    voiced_frames = []
                    
        if voiced_frames:
            yield (
                b''.join([f.bytes for f in voiced_frames]),
                voiced_frames[0].timestamp,
                voiced_frames[-1].timestamp + voiced_frames[-1].duration
            )
