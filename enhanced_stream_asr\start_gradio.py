#!/usr/bin/env python3
"""
Gradio Web界面启动脚本
提供更稳定的麦克风录音体验
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import ConfigManager
from utils.logger import setup_logger
from web.gradio_interface import GradioInterface


def check_gradio_installation():
    """检查Gradio是否安装"""
    try:
        import gradio as gr
        print(f"✅ Gradio已安装 (版本: {gr.__version__})")
        return True
    except ImportError:
        print("❌ Gradio未安装")
        print("请运行以下命令安装Gradio:")
        print("pip install gradio")
        return False


def check_websockets_installation():
    """检查websockets是否安装"""
    try:
        import websockets
        print(f"✅ websockets已安装")
        return True
    except ImportError:
        print("❌ websockets未安装")
        print("请运行以下命令安装websockets:")
        print("pip install websockets")
        return False


def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║              Enhanced Stream ASR - Gradio界面                ║
║                  增强流式语音识别Web界面                       ║
║                                                              ║
║  🎤 稳定麦克风录音  🌍 多语种识别  🤖 自动语种检测           ║
║  📱 响应式设计     📊 实时监控    🔧 灵活配置               ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Enhanced Stream ASR Gradio Web Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python start_gradio.py                           # 使用默认配置启动
  python start_gradio.py --port 7860               # 指定端口
  python start_gradio.py --host 0.0.0.0            # 指定主机
  python start_gradio.py --websocket-url ws://localhost:8080/ws/stream  # 指定WebSocket URL
  python start_gradio.py --share                   # 启用公共分享链接
  python start_gradio.py --debug                   # 调试模式
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        default='configs',
        help='配置文件目录 (默认: configs)'
    )
    
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='Gradio服务器主机地址 (默认: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=7860,
        help='Gradio服务器端口 (默认: 7860)'
    )
    
    parser.add_argument(
        '--websocket-url',
        default='ws://localhost:8080/ws/stream',
        help='WebSocket服务器URL (默认: ws://localhost:8080/ws/stream)'
    )
    
    parser.add_argument(
        '--share',
        action='store_true',
        help='启用Gradio公共分享链接'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    # 检查依赖
    if not check_gradio_installation():
        sys.exit(1)
        
    if not check_websockets_installation():
        sys.exit(1)
    
    try:
        # 加载配置
        print("⚙️  加载配置...")
        config_manager = ConfigManager(args.config)
        
        # 设置日志
        logger = setup_logger(
            level=args.log_level,
            log_file=config_manager.get("logging.file") if not args.debug else None
        )
        
        # 创建Gradio界面
        print("🌐 创建Gradio界面...")
        gradio_interface = GradioInterface(
            config_manager=config_manager,
            websocket_url=args.websocket_url
        )
        
        # 打印启动信息
        print(f"🌐 Gradio界面地址: http://{args.host}:{args.port}")
        print(f"📡 WebSocket服务器: {args.websocket_url}")
        print(f"🌍 支持语种: {', '.join(config_manager.get_supported_languages())}")
        
        if args.share:
            print("🔗 公共分享链接: 启用")
            
        print()
        print("🚀 启动Gradio界面...")
        print("按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动界面
        gradio_interface.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            debug=args.debug,
            show_error=args.debug,
            quiet=not args.debug
        )
        
    except KeyboardInterrupt:
        print("\n👋 Gradio界面已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
