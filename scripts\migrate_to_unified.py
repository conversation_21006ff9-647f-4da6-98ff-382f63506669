#!/usr/bin/env python3
"""
Migration script to help transition from original architecture to unified ASR
"""

import os
import shutil
import json
import argparse
from pathlib import Path

def backup_original_models(backup_dir="backup_original_models"):
    """Backup original model files"""
    print(f"Creating backup of original models in {backup_dir}...")
    
    original_models = [
        "model/zh/audio_trans",
        "model/zh/audio_file_trans", 
        "model/zh/attention_rescoring",
        "model/zh/feature_extractor",
        "model/zh/encoder",
        "model/zh/decoder",
        "model/zh/scoring"
    ]
    
    os.makedirs(backup_dir, exist_ok=True)
    
    for model_path in original_models:
        if os.path.exists(model_path):
            model_name = os.path.basename(model_path)
            backup_path = os.path.join(backup_dir, model_name)
            
            if os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            
            shutil.copytree(model_path, backup_path)
            print(f"  ✓ Backed up: {model_path} -> {backup_path}")
        else:
            print(f"  ⚠ Model not found: {model_path}")

def extract_config_from_original():
    """Extract configuration parameters from original models"""
    print("Extracting configuration from original models...")
    
    config = {}
    
    # Extract from audio_trans config
    audio_trans_config = "model/zh/audio_trans/config.pbtxt"
    if os.path.exists(audio_trans_config):
        with open(audio_trans_config, 'r') as f:
            content = f.read()
        
        # Parse parameters (simple parsing)
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'key:' in line and 'value:' in lines[i+1]:
                key = line.split('"')[1] if '"' in line else None
                value = lines[i+1].split('"')[1] if '"' in lines[i+1] else None
                if key and value:
                    config[key] = value
        
        print(f"  ✓ Extracted {len(config)} parameters from audio_trans")
    
    # Extract from feature_extractor config
    feature_config = "model/zh/feature_extractor/config.pbtxt"
    if os.path.exists(feature_config):
        with open(feature_config, 'r') as f:
            content = f.read()
        
        # Parse feature extraction parameters
        if 'num_mel_bins' in content:
            config['num_mel_bins'] = '80'
        if 'frame_shift_in_ms' in content:
            config['frame_shift_in_ms'] = '10'
        if 'frame_length_in_ms' in content:
            config['frame_length_in_ms'] = '25'
        if 'sample_rate' in content:
            config['sample_rate'] = '16000'
            
        print("  ✓ Extracted feature extraction parameters")
    
    # Extract from scoring config
    scoring_config = "model/zh/scoring/config.pbtxt"
    if os.path.exists(scoring_config):
        with open(scoring_config, 'r') as f:
            content = f.read()
        
        # Parse scoring parameters
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'key:' in line and i+1 < len(lines) and 'value:' in lines[i+1]:
                key = line.split('"')[1] if '"' in line else None
                value = lines[i+1].split('"')[1] if '"' in lines[i+1] else None
                if key and value:
                    config[key] = value
        
        print("  ✓ Extracted scoring parameters")
    
    return config

def update_unified_config(extracted_config):
    """Update unified ASR config with extracted parameters"""
    print("Updating unified ASR configuration...")
    
    config_path = "model/zh/unified_asr/config.pbtxt"
    if not os.path.exists(config_path):
        print(f"  ✗ Unified config not found: {config_path}")
        return False
    
    with open(config_path, 'r') as f:
        content = f.read()
    
    # Update parameters based on extracted config
    updated_params = 0
    for key, value in extracted_config.items():
        # Find and replace parameter values
        old_pattern = f'key: "{key}"'
        if old_pattern in content:
            # Find the corresponding value line
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if old_pattern in line and i+1 < len(lines):
                    if 'value:' in lines[i+1]:
                        lines[i+1] = f'    value: {{ string_value: "{value}"}}'
                        updated_params += 1
                        break
            content = '\n'.join(lines)
    
    # Write updated config
    with open(config_path, 'w') as f:
        f.write(content)
    
    print(f"  ✓ Updated {updated_params} parameters in unified config")
    return True

def validate_unified_model():
    """Validate the unified model setup"""
    print("Validating unified model setup...")
    
    required_files = [
        "model/zh/unified_asr/config.pbtxt",
        "model/zh/unified_asr/1/model.py"
    ]
    
    all_valid = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✓ Found: {file_path}")
        else:
            print(f"  ✗ Missing: {file_path}")
            all_valid = False
    
    # Check model file structure
    model_file = "model/zh/unified_asr/1/model.py"
    if os.path.exists(model_file):
        with open(model_file, 'r') as f:
            content = f.read()
        
        required_methods = [
            'class TritonPythonModel',
            'def initialize',
            'def execute',
            'def _extract_features',
            'def _run_encoder',
            'def _decode_ctc'
        ]
        
        for method in required_methods:
            if method in content:
                print(f"    ✓ Found method: {method}")
            else:
                print(f"    ✗ Missing method: {method}")
                all_valid = False
    
    return all_valid

def create_deployment_script():
    """Create a deployment script for the unified model"""
    print("Creating deployment script...")
    
    script_content = '''#!/bin/bash
# Deployment script for Unified ASR Model

echo "Deploying Unified ASR Model..."

# Check if Triton server is running
if pgrep -x "tritonserver" > /dev/null; then
    echo "Triton server is running. Please stop it before deployment."
    exit 1
fi

# Set model repository path
MODEL_REPO=${1:-"/opt/tritonserver/models"}

echo "Using model repository: $MODEL_REPO"

# Copy unified model to repository
if [ -d "model/zh/unified_asr" ]; then
    echo "Copying unified_asr model..."
    cp -r model/zh/unified_asr $MODEL_REPO/
    echo "✓ Model copied successfully"
else
    echo "✗ Unified ASR model not found"
    exit 1
fi

# Copy tools directory
if [ -d "tools" ]; then
    echo "Copying tools directory..."
    cp -r tools $MODEL_REPO/unified_asr/
    echo "✓ Tools copied successfully"
else
    echo "⚠ Tools directory not found"
fi

echo "Deployment complete!"
echo "Start Triton server with: tritonserver --model-repository=$MODEL_REPO"
'''
    
    with open("deploy_unified_asr.sh", 'w') as f:
        f.write(script_content)
    
    os.chmod("deploy_unified_asr.sh", 0o755)
    print("  ✓ Created deployment script: deploy_unified_asr.sh")

def main():
    parser = argparse.ArgumentParser(description="Migrate to Unified ASR Model")
    parser.add_argument("--backup", action="store_true", help="Backup original models")
    parser.add_argument("--extract-config", action="store_true", help="Extract config from original models")
    parser.add_argument("--validate", action="store_true", help="Validate unified model")
    parser.add_argument("--deploy-script", action="store_true", help="Create deployment script")
    parser.add_argument("--all", action="store_true", help="Run all migration steps")
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        args.all = True
    
    print("=" * 60)
    print("UNIFIED ASR MIGRATION TOOL")
    print("=" * 60)
    
    if args.all or args.backup:
        backup_original_models()
        print()
    
    if args.all or args.extract_config:
        extracted_config = extract_config_from_original()
        if extracted_config:
            update_unified_config(extracted_config)
        print()
    
    if args.all or args.validate:
        is_valid = validate_unified_model()
        if is_valid:
            print("  ✓ Unified model validation passed")
        else:
            print("  ✗ Unified model validation failed")
        print()
    
    if args.all or args.deploy_script:
        create_deployment_script()
        print()
    
    print("=" * 60)
    print("MIGRATION COMPLETE")
    print("=" * 60)
    print()
    print("Next steps:")
    print("1. Review the unified model configuration")
    print("2. Test the unified model with sample data")
    print("3. Deploy using: ./deploy_unified_asr.sh [model_repository_path]")
    print("4. Start Triton server and test inference")

if __name__ == "__main__":
    main()
