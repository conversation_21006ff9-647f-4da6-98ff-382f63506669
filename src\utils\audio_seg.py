#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  audio_seg.py
# Time    :  2024/10/31 16:58:44
# Author  :  lh
# Version :  1.0
# Description: 长音频切割
import os
from vad_utils import SpeechVadFrontend
from audio_utils import load_from_local_path, save_to_local_path

if __name__ == '__main__':
    test_audio = "/ws/test_data/yue/16_003.wav"
    output_dir = "/ws/test_data/yue/"

    vad_level = 1
    min_speech_len = 5
    merge_thres = 2

    vad_conf = {
            'vad_type': 'webrtcvad',
            'vad_level': vad_level,
            'frame_length': 30,
            'window_size' : 10,
            'seg_thres' : 0.9, 
            'max_speech_len' : 30,
            'min_speech_len' : min_speech_len,
            'merge_sil_thres' : merge_thres
    }
    vad_frontend = SpeechVadFrontend(**vad_conf)
    audio, sr = load_from_local_path(test_audio)
    segments, segment_lens, segment_ranges = vad_frontend.get_all_speech_segments(audio, sr)
    print([len/1000.0 for len in segment_lens])
    print([(range[0]/1000.0, range[1]/1000.0) for range in segment_ranges])
    for i, (s,e) in enumerate(segment_ranges):
        if output_dir is None:
            output_file = f'{audio_file}_{i}.wav'
        else:
            output_file = os.path.join(output_dir, f'{i}.wav')
        save_to_local_path(output_file, audio[:, s*sr//1000 : (e+1)*sr//1000], sr)
