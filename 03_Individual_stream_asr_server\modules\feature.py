#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  feature.py
# Time    :  2025/04/17 14:20:19
# Author  :  lh
# Version :  1.0
# Description:  

import librosa
import numpy as np
import torch
import torchaudio.compliance.kaldi as kaldi
import torch.nn.functional as F
from typing import Union, Tuple, Optional

class FeaturePipeline:
    """
    用于音频特征提取的管道类,支持多种特征类型。
    支持的特征类型包括fbank、mfcc和log_mel_spectrogram。初始化时需要提供配置参数以设置特征计算的相关参数。
    Attributes:
        configs (dict): 配置参数字典。
        num_mel_bins (int):  Mel频带数量。
        frame_length (int): 帧长度(ms)。
        frame_shift (int): 帧移位(ms)。
        dither (float): 抗混叠噪声强度。
        n_fft (int): 快速傅里叶变换的点数。
        hop_length (int): STFT中的跳跃长度。
        feat_func (callable): 特征计算函数,根据feat_type选择对应的方法。
    Examples:
        configs = {
            'feat_type': 'fbank',
            'num_mel_bins': 80,
            'frame_length': 25,
            'frame_shift': 10,
            'dither': 1.0,
            'n_fft': 400,
            'hop_length': 160
        }
        pipeline = FeaturePipeline(configs)
    """

    def __init__(self, configs: dict):
        """
        初始化特征提取管道。
        Args:
            configs (dict): 包含特征提取配置参数的字典,包括：
                - 'feat_type': str,支持'fbank', 'mfcc', 'log_mel_spectrogram'
                - 'num_mel_bins': int
                - 'frame_length': int (ms)
                - 'frame_shift': int (ms)
                - 'dither': float
                - 'n_fft': int
                - 'hop_length': int
        Raises:
            ValueError: 如果feat_type不在支持的类型中。
        """
        print(f"FeaturePipeline 初始化")

        self.num_mel_bins = configs['num_mel_bins']
        self.frame_length = configs['frame_length']
        self.frame_shift = configs['frame_shift']
        self.dither = configs['dither']
        self.n_fft = configs['n_fft']
        self.hop_length = configs['hop_length']

        feat_type = configs.get('feat_type')
        if feat_type == "fbank":
            self.feat_func = self.compute_fbank
        elif feat_type == 'mfcc':
            self.feat_func = self.compute_mfcc
        elif feat_type == 'log_mel_spectrogram':
            self.feat_func = self.compute_log_mel_spectrogram
        else:
            raise ValueError(f"不支持的特征类型: {feat_type}")

    def to_waveform(self, pcm_data: bytes) -> torch.Tensor:
        """
        将pcm数据转换为归一化的波形张量。
        Args:
            pcm_data (bytes): PCM格式的音频数据,字长为16位整数。
        Returns:
            torch.Tensor: 归一化后的波形张量,形状为(sample,)。
        Raises:
            ValueError: 如果PCM数据格式无效。
        """
        try:
            audio_signal = np.frombuffer(pcm_data, np.int16).copy()
        except Exception as e:
            raise ValueError("无效的PCM数据格式")

        max_val = np.iinfo(np.int16).max
        audio_signal = (audio_signal.astype(np.float32) / max_val) # 归一化到 [-1, 1]
        waveform = torch.from_numpy(audio_signal).squeeze(0)  # (channel=1, sample) -> (sample,)
        return waveform
        
    def compute_fbank(self, waveform: torch.Tensor, sample_rate: int) -> torch.Tensor:
        """
        计算FBANK特征。
        Args:
            waveform (torch.Tensor): 输入波形,形状为(sample,)
            sample_rate (int): 采样率
        Returns:
            torch.Tensor: FBANK特征张量,形状为(num_frames, num_mel_bins)
        """
        waveform = waveform * (1 << 15)
        waveform = waveform.unsqueeze(0)
        feat = kaldi.fbank(waveform,
                      num_mel_bins=self.num_mel_bins,
                      frame_length=self.frame_length,
                      frame_shift=self.frame_shift,
                      dither=self.dither,
                      energy_floor=0.0,
                      sample_frequency=sample_rate)
        return feat

    def compute_mfcc(self, waveform: torch.Tensor, sample_rate: int,
                     num_mel_bins: int = 23, num_ceps: int = 40,
                     high_freq: float = 0.0, low_freq: float = 20.0) -> torch.Tensor:
        """
        计算MFCC特征。
        Args:
            waveform (torch.Tensor): 输入波形,形状为(sample,)
            sample_rate (int): 采样率
            num_mel_bins (int, optional): Mel频带数量,默认为23
            num_ceps (int, optional): CEPstral系数数量,默认为40
            high_freq (float, optional): 最高频率,默认为0.0
            low_freq (float, optional): 最低频率,默认为20.0
        Returns:
            torch.Tensor: MFCC特征张量,形状为(num_frames, num_ceps)
        """
        waveform = waveform * (1 << 15)
        feat = kaldi.mfcc(waveform,
                        num_mel_bins=self.num_mel_bins,
                        frame_length=self.frame_length,
                        frame_shift=self.frame_shift,
                        dither=self.dither,
                        num_ceps=num_ceps,
                        high_freq=high_freq,
                        low_freq=low_freq,
                        sample_frequency=sample_rate)
        return feat

    def compute_log_mel_spectrogram(self, waveform: torch.Tensor, sample_rate: int,
                                    padding: int = 0, pad_or_trim: bool = False,
                                    max_duration: int = 30) -> torch.Tensor:
        """
        计算对数梅尔频谱图。
        Args:
            waveform (torch.Tensor): 输入波形,形状为(sample,)
            sample_rate (int): 采样率
            padding (int, optional): 填充长度,默认为0
            pad_or_trim (bool, optional): 是否进行填充或裁剪,默认为False
            max_duration (int, optional): 最大持续时间(秒),默认为30
        Returns:
            torch.Tensor: 对数梅尔频谱图,形状为(num_frames, num_mel_bins)
        """
        if padding > 0:
            waveform = F.pad(waveform, (0, padding))
        if pad_or_trim:
            length = max_duration * sample_rate
            if waveform.size(0) >= length:
                waveform = waveform[:length]
            else:
                waveform = F.pad(waveform, (0, length - waveform.size(0)))

        window = torch.hann_window(self.n_fft)
        stft = torch.stft(waveform,
                        self.n_fft,
                        self.hop_length,
                        window=window,
                        return_complex=True)
        magnitudes = stft[..., :-1].abs() ** 2

        filters = torch.from_numpy(
            librosa.filters.mel(sr=sample_rate, n_fft=self.n_fft, n_mels=self.num_mel_bins))
        mel_spec = filters @ magnitudes

        log_spec = torch.clamp(mel_spec, min=1e-10).log10()
        log_spec = torch.maximum(log_spec, log_spec.max() - 8.0)
        log_spec = (log_spec + 4.0) / 4.0

        return log_spec.transpose(0, 1)
