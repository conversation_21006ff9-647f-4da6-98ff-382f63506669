#!/usr/bin/env python3
"""
模型路径修复脚本
自动检测和修复模型文件路径配置问题
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.config.model_path_manager import global_model_path_manager
from utils.config.config_manager import ConfigManager
from utils.config.validator import ConfigValidator

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    print("🔧 Enhanced Stream ASR - 模型路径修复工具")
    print("=" * 50)
    
    try:
        # 1. 检查当前配置状态
        print("\n📋 步骤 1: 检查当前配置状态")
        config_manager = ConfigManager("configs")
        
        # 验证当前配置
        validation_results = ConfigValidator.validate_all_configs(
            config_manager.server_config,
            config_manager.language_configs,
            config_manager.lid_config
        )
        
        has_errors = ConfigValidator.has_errors(validation_results)
        if has_errors:
            print("❌ 发现配置错误:")
            error_message = ConfigValidator.format_validation_errors(validation_results)
            print(error_message)
        else:
            print("✅ 当前配置验证通过")
            return
        
        # 2. 创建模型目录结构
        print("\n📁 步骤 2: 创建标准模型目录结构")
        success, messages = global_model_path_manager.create_model_directory_structure()
        
        for message in messages:
            print(f"  {message}")
        
        if not success:
            print("❌ 模型目录创建失败")
            return
        
        # 3. 自动发现模型文件
        print("\n🔍 步骤 3: 自动发现模型文件")
        discovered_models = global_model_path_manager.auto_discover_models()
        
        if discovered_models["languages"]:
            print("✅ 发现的语种模型:")
            for lang, info in discovered_models["languages"].items():
                print(f"  - {lang}: {info['model_path']}")
        else:
            print("⚠️  未发现语种模型文件")
        
        if discovered_models["lid"]:
            print("✅ 发现的LID模型:")
            print(f"  - LID: {discovered_models['lid']['model_path']}")
        else:
            print("⚠️  未发现LID模型文件")
        
        # 4. 生成配置文件
        if discovered_models["languages"] or discovered_models["lid"]:
            print("\n⚙️  步骤 4: 生成配置文件")
            success, messages = global_model_path_manager.fix_model_paths("configs")
            
            for message in messages:
                print(f"  {message}")
            
            if success:
                print("✅ 配置文件生成成功")
            else:
                print("❌ 配置文件生成失败")
                return
        else:
            print("\n⚠️  步骤 4: 跳过配置生成（未发现模型文件）")
            print_model_setup_guide()
            return
        
        # 5. 重新验证配置
        print("\n✅ 步骤 5: 重新验证配置")
        config_manager = ConfigManager("configs")
        
        validation_results = ConfigValidator.validate_all_configs(
            config_manager.server_config,
            config_manager.language_configs,
            config_manager.lid_config
        )
        
        has_errors = ConfigValidator.has_errors(validation_results)
        if has_errors:
            print("⚠️  仍存在一些配置问题:")
            error_message = ConfigValidator.format_validation_errors(validation_results)
            print(error_message)
            print("\n💡 这些问题可能需要手动解决，请检查模型文件是否正确放置。")
        else:
            print("🎉 所有配置验证通过！")
        
        print("\n" + "=" * 50)
        print("✅ 模型路径修复完成")
        
    except Exception as e:
        logger.error(f"模型路径修复失败: {e}")
        print(f"❌ 修复过程中出现错误: {e}")
        sys.exit(1)


def print_model_setup_guide():
    """打印模型设置指南"""
    print("\n📖 模型设置指南")
    print("-" * 30)
    print("""
为了使用Enhanced Stream ASR，您需要准备以下模型文件：

1. 语种模型 (放置在 models/{language}/ 目录下):
   - encoder.onnx: 编码器模型
   - ctc.onnx: CTC模型
   - units.txt: 词汇表文件
   - hotwords.txt: 热词文件 (可选)

2. 语种识别模型 (放置在 models/lid/ 目录下):
   - lid_model.onnx: 语种识别模型

支持的语种目录:
   - models/zh/     # 中文
   - models/en/     # 英文
   - models/ru/     # 俄文
   - models/ug/     # 维吾尔文
   - models/kk/     # 哈萨克文

示例目录结构:
models/
├── zh/
│   ├── encoder.onnx
│   ├── ctc.onnx
│   ├── units.txt
│   └── hotwords.txt
├── en/
│   ├── encoder.onnx
│   ├── ctc.onnx
│   └── units.txt
└── lid/
    └── lid_model.onnx

请将模型文件放置到相应目录后，重新运行此脚本。
""")


if __name__ == "__main__":
    main()
