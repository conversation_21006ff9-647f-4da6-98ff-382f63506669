"""
多语种ASR管理器
动态加载和管理不同语种的ASR解码器
"""

import logging
import threading
from typing import Dict, Optional, List, Any
from concurrent.futures import ThreadPoolExecutor

from .decoder import ASRDecoder, ONNXASRDecoder
from ...utils.config import ConfigManager

logger = logging.getLogger(__name__)


class MultiLangASR:
    """多语种ASR管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化多语种ASR管理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.decoders: Dict[str, ASRDecoder] = {}
        self.decoder_locks: Dict[str, threading.Lock] = {}
        self.loading_locks: Dict[str, threading.Lock] = {}
        
        # 线程池用于并发初始化
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="asr_init")
        
        # 配置参数
        self.supported_languages = config_manager.get_supported_languages()
        self.lazy_loading = config_manager.get('asr.lazy_loading', True)
        self.max_concurrent_decoders = config_manager.get('asr.max_concurrent_decoders', 5)
        
        # 预加载默认语种（如果不是懒加载）
        if not self.lazy_loading:
            self._preload_decoders()
            
        logger.info(f"MultiLangASR initialized with {len(self.supported_languages)} supported languages")
        
    def _preload_decoders(self):
        """预加载所有支持的解码器"""
        logger.info("Preloading ASR decoders...")
        
        for language in self.supported_languages:
            try:
                self._load_decoder(language)
            except Exception as e:
                logger.error(f"Failed to preload decoder for {language}: {e}")
                
    def _load_decoder(self, language: str) -> bool:
        """
        加载指定语种的解码器
        
        Args:
            language: 语种代码
            
        Returns:
            是否加载成功
        """
        if language in self.decoders:
            return True
            
        try:
            # 获取语种配置
            lang_config = self.config_manager.get_language_config(language)
            if not lang_config:
                logger.error(f"No configuration found for language: {language}")
                return False
                
            # 检查并发解码器数量限制
            if len(self.decoders) >= self.max_concurrent_decoders:
                logger.warning(f"Maximum concurrent decoders reached ({self.max_concurrent_decoders})")
                # 可以考虑卸载最少使用的解码器
                self._cleanup_least_used_decoder()
                
            # 创建解码器
            decoder = ONNXASRDecoder(language, lang_config)
            
            # 初始化解码器
            if not decoder.initialize():
                logger.error(f"Failed to initialize decoder for {language}")
                return False
                
            # 保存解码器和锁
            self.decoders[language] = decoder
            self.decoder_locks[language] = threading.Lock()
            
            logger.info(f"ASR decoder loaded for language: {language}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load decoder for {language}: {e}")
            return False
            
    def _cleanup_least_used_decoder(self):
        """清理最少使用的解码器"""
        # 这里可以实现LRU策略，暂时简单移除第一个
        if self.decoders:
            language_to_remove = next(iter(self.decoders))
            self.unload_decoder(language_to_remove)
            logger.info(f"Cleaned up decoder for {language_to_remove} to make room")
            
    def get_decoder(self, language: str) -> Optional[ASRDecoder]:
        """
        获取指定语种的解码器
        
        Args:
            language: 语种代码
            
        Returns:
            ASR解码器实例，如果不存在或加载失败返回None
        """
        # 检查语种是否支持
        if language not in self.supported_languages:
            logger.warning(f"Unsupported language: {language}")
            return None
            
        # 如果解码器已存在，直接返回
        if language in self.decoders:
            return self.decoders[language]
            
        # 懒加载模式下动态加载
        if self.lazy_loading:
            # 使用加载锁防止重复加载
            if language not in self.loading_locks:
                self.loading_locks[language] = threading.Lock()
                
            with self.loading_locks[language]:
                # 双重检查，防止在等待锁的过程中其他线程已经加载了
                if language in self.decoders:
                    return self.decoders[language]
                    
                logger.info(f"Lazy loading ASR decoder for language: {language}")
                if self._load_decoder(language):
                    return self.decoders[language]
                    
        return None
        
    def decode_audio(
        self, 
        language: str, 
        audio_features: Any, 
        is_final: bool = False
    ) -> Dict[str, Any]:
        """
        解码音频特征
        
        Args:
            language: 语种代码
            audio_features: 音频特征
            is_final: 是否为最终块
            
        Returns:
            解码结果
        """
        decoder = self.get_decoder(language)
        if not decoder:
            return {
                "error": f"Failed to get decoder for language: {language}",
                "success": False
            }
            
        # 使用解码器锁确保线程安全
        with self.decoder_locks[language]:
            try:
                if is_final:
                    # 最终解码
                    result = decoder.finalize()
                else:
                    # 块解码
                    result = decoder.decode_chunk(audio_features)
                    
                return result
                
            except Exception as e:
                logger.error(f"Decoding failed for language {language}: {e}")
                return {
                    "error": str(e),
                    "success": False
                }
                
    def reset_decoder(self, language: str) -> bool:
        """
        重置指定语种的解码器
        
        Args:
            language: 语种代码
            
        Returns:
            是否重置成功
        """
        decoder = self.get_decoder(language)
        if not decoder:
            return False
            
        with self.decoder_locks[language]:
            try:
                decoder.reset()
                logger.debug(f"Decoder reset for language: {language}")
                return True
            except Exception as e:
                logger.error(f"Failed to reset decoder for {language}: {e}")
                return False
                
    def unload_decoder(self, language: str) -> bool:
        """
        卸载指定语种的解码器
        
        Args:
            language: 语种代码
            
        Returns:
            是否卸载成功
        """
        if language not in self.decoders:
            return True
            
        try:
            # 获取锁并清理
            with self.decoder_locks[language]:
                decoder = self.decoders[language]
                decoder.cleanup()
                
            # 移除解码器和锁
            del self.decoders[language]
            del self.decoder_locks[language]
            
            # 清理加载锁（如果存在）
            if language in self.loading_locks:
                del self.loading_locks[language]
                
            logger.info(f"ASR decoder unloaded for language: {language}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload decoder for {language}: {e}")
            return False
            
    def get_loaded_languages(self) -> List[str]:
        """获取已加载的语种列表"""
        return list(self.decoders.keys())
        
    def get_supported_languages(self) -> List[str]:
        """获取支持的语种列表"""
        return self.supported_languages.copy()
        
    def is_language_loaded(self, language: str) -> bool:
        """检查语种是否已加载"""
        return language in self.decoders
        
    def is_language_supported(self, language: str) -> bool:
        """检查语种是否支持"""
        return language in self.supported_languages
        
    def get_decoder_statistics(self, language: str) -> Optional[Dict[str, Any]]:
        """
        获取解码器统计信息
        
        Args:
            language: 语种代码
            
        Returns:
            统计信息字典
        """
        decoder = self.get_decoder(language)
        if decoder:
            return decoder.get_statistics()
        return None
        
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        loaded_languages = self.get_loaded_languages()
        
        # 收集每个解码器的统计信息
        decoder_stats = {}
        for language in loaded_languages:
            stats = self.get_decoder_statistics(language)
            if stats:
                decoder_stats[language] = stats
                
        return {
            "supported_languages": len(self.supported_languages),
            "loaded_languages": len(loaded_languages),
            "max_concurrent_decoders": self.max_concurrent_decoders,
            "lazy_loading": self.lazy_loading,
            "loaded_language_list": loaded_languages,
            "decoder_statistics": decoder_stats
        }
        
    def reload_decoder(self, language: str) -> bool:
        """
        重新加载指定语种的解码器
        
        Args:
            language: 语种代码
            
        Returns:
            是否重新加载成功
        """
        try:
            # 先卸载
            self.unload_decoder(language)
            
            # 再加载
            return self._load_decoder(language)
            
        except Exception as e:
            logger.error(f"Failed to reload decoder for {language}: {e}")
            return False
            
    def shutdown(self):
        """关闭多语种ASR管理器"""
        try:
            logger.info("Shutting down MultiLangASR...")
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            # 卸载所有解码器
            languages_to_unload = list(self.decoders.keys())
            for language in languages_to_unload:
                self.unload_decoder(language)
                
            logger.info("MultiLangASR shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during MultiLangASR shutdown: {e}")
            
    def __enter__(self):
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()
