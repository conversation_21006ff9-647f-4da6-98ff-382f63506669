#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  model.py
# Time    :  2024/07/24 14:12:30
# Author  :  lh
# Version :  1.0
# Description:  调用模型解码

import tools.triton_python_backend_utils as pb_utils
import tritonclient.grpc as grpcclient
from tritonclient.utils import np_to_triton_dtype
import soundfile as sf
import numpy as np
import json
import sys
import librosa

model_name = 'model.python' # 'whisper_transcribe'   # audio_file_trans  # audio_trans
audio_file = [sys.argv[1]]
audio_lang = ["zh"]   #sys.argv[2]
print(audio_file)

bz = len(audio_file)
if model_name == "whisper_transcribe":   # audio_file_trans
	inputs = [
				grpcclient.InferInput("WAV", [1, bz], "BYTES",),
				grpcclient.InferInput("LANG", [1, 1], "BYTES")
			]
	inp0 = np.array([x.encode("utf-8") for x in audio_file], dtype=np.object_)
	inp0 = np.expand_dims(inp0, axis=0)
	inp1 = np.array([x.encode("utf-8") for x in audio_lang], dtype=np.object_)
	inp1 = np.expand_dims(inp1, axis=0)
	print("*"*10)
	print(inp0.shape)
	print(inp1.shape)
	inputs[0].set_data_from_numpy(inp0)
	inputs[1].set_data_from_numpy(inp1)

elif model_name == 'attention_rescoring_zh':
	waveform, sample_rate = sf.read(audio_file)
	samples = np.array([waveform], dtype=np.float32)
	lengths = np.array([[len(waveform)]], dtype=np.int32)
	inputs = [
		grpcclient.InferInput("WAV", samples.shape,
										np_to_triton_dtype(samples.dtype)),    # str:'FP32'
		grpcclient.InferInput("WAV_LENS", lengths.shape,
										np_to_triton_dtype(lengths.dtype))    # str:'INT32'
	]
	inputs[0].set_data_from_numpy(samples)  # 填充 `grpcclient.InferInput` 对象中的 _raw_content 属性
	inputs[1].set_data_from_numpy(lengths)

else:
	waveform, sr = librosa.load(audio_file[0], sr=16000)
	samples = np.array([waveform], dtype=np.float32)
	lang = np.array([x.encode("utf-8") for x in audio_lang], dtype=np.object_)
	lang = np.expand_dims(lang, axis=0)

	inputs = [
		grpcclient.InferInput("WAV", samples.shape,
										np_to_triton_dtype(samples.dtype)),    # str:'FP32'
		grpcclient.InferInput("LANG", [1, 1], "BYTES")    # str:'INT32'
	]
	inputs[0].set_data_from_numpy(samples)
	inputs[1].set_data_from_numpy(lang)
	print("*"*10)
	print(samples.shape)
	print(lang.shape)

outputs = [grpcclient.InferRequestedOutput("TRANSCRIPTS")]

triton_client = grpcclient.InferenceServerClient(url="localhost:10091", verbose=False)
results = triton_client.infer(
							model_name=model_name,
							request_id="10086",
							inputs=inputs,
							outputs=outputs).as_numpy("TRANSCRIPTS")

# print(results)
for result in results:
	result = result.decode("utf-8")
	print(json.dumps(eval(result), indent=4, ensure_ascii=False))
