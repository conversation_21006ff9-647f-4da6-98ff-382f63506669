"""
渐进式语种识别
支持0.4s->0.8s->2.4s的渐进式语种识别流程
"""

import logging
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from .lid_engine import LIDEngine

logger = logging.getLogger(__name__)


class LIDState(Enum):
    """LID状态枚举"""
    WAITING_FOR_SPEECH = "waiting_for_speech"
    COLLECTING_AUDIO = "collecting_audio"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class LIDResult:
    """LID识别结果"""
    language: Optional[str]
    confidence: float
    audio_duration: float
    attempt_count: int
    all_predictions: List[Dict[str, Any]]
    is_final: bool = False
    error: Optional[str] = None


class ProgressiveLID:
    """渐进式语种识别器"""
    
    def __init__(self, lid_engine: LIDEngine, vad_processor, config: Dict[str, Any]):
        """
        初始化渐进式LID
        
        Args:
            lid_engine: LID引擎
            vad_processor: VAD处理器
            config: 配置参数
        """
        self.lid_engine = lid_engine
        self.vad_processor = vad_processor
        self.config = config
        
        # 配置参数
        self.progressive_steps = config.get('progressive_steps', [0.4, 0.8, 1.2, 1.6, 2.0, 2.4])
        self.confidence_threshold = config.get('confidence_threshold', 0.8)
        self.early_stop_threshold = config.get('early_stop_threshold', 0.95)
        self.max_attempts = config.get('max_attempts', 6)
        self.min_speech_duration = config.get('min_speech_duration', 0.2)
        self.fallback_language = config.get('fallback_language', 'zh')
        
        # 状态变量
        self.reset()
        
    def reset(self):
        """重置状态"""
        self.state = LIDState.WAITING_FOR_SPEECH
        self.audio_buffer = []
        self.speech_audio_buffer = []
        self.current_attempt = 0
        self.start_time = time.time()
        self.last_result = None
        self.total_audio_duration = 0.0
        self.speech_audio_duration = 0.0
        
    def add_audio_chunk(self, audio_data: np.ndarray, sample_rate: int = 16000) -> Optional[LIDResult]:
        """
        添加音频块并进行渐进式识别
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            
        Returns:
            LID结果（如果有）
        """
        try:
            # 更新总音频时长
            chunk_duration = len(audio_data) / sample_rate
            self.total_audio_duration += chunk_duration
            
            # 添加到音频缓冲区
            self.audio_buffer.append(audio_data)
            
            # 状态机处理
            if self.state == LIDState.WAITING_FOR_SPEECH:
                return self._handle_waiting_for_speech(audio_data, sample_rate)
            elif self.state == LIDState.COLLECTING_AUDIO:
                return self._handle_collecting_audio(sample_rate)
            elif self.state == LIDState.COMPLETED:
                return self.last_result
            else:
                return None
                
        except Exception as e:
            logger.error(f"Progressive LID error: {e}")
            self.state = LIDState.FAILED
            return LIDResult(
                language=None,
                confidence=0.0,
                audio_duration=self.total_audio_duration,
                attempt_count=self.current_attempt,
                all_predictions=[],
                is_final=True,
                error=str(e)
            )
            
    def _handle_waiting_for_speech(self, audio_data: np.ndarray, sample_rate: int) -> Optional[LIDResult]:
        """处理等待语音状态"""
        try:
            # 检测是否包含语音
            is_speech = self.vad_processor.is_speech_chunk(audio_data, sample_rate)
            
            if is_speech:
                logger.debug("Speech detected, starting audio collection")
                self.state = LIDState.COLLECTING_AUDIO
                self.speech_audio_buffer.append(audio_data)
                self.speech_audio_duration += len(audio_data) / sample_rate
                
                # 检查是否达到第一个检测点
                return self._check_detection_point(sample_rate)
            else:
                # 继续等待语音
                return None
                
        except Exception as e:
            logger.error(f"Error in waiting for speech: {e}")
            return None
            
    def _handle_collecting_audio(self, sample_rate: int) -> Optional[LIDResult]:
        """处理音频收集状态"""
        try:
            # 检查最新的音频块是否包含语音
            latest_audio = self.audio_buffer[-1]
            is_speech = self.vad_processor.is_speech_chunk(latest_audio, sample_rate)
            
            if is_speech:
                # 添加到语音缓冲区
                self.speech_audio_buffer.append(latest_audio)
                self.speech_audio_duration += len(latest_audio) / sample_rate
                
            # 检查是否达到检测点
            return self._check_detection_point(sample_rate)
            
        except Exception as e:
            logger.error(f"Error in collecting audio: {e}")
            return None
            
    def _check_detection_point(self, sample_rate: int) -> Optional[LIDResult]:
        """检查是否达到检测点"""
        try:
            # 检查是否有足够的语音数据
            if self.speech_audio_duration < self.min_speech_duration:
                return None
                
            # 检查是否达到当前步骤的时长要求
            if self.current_attempt < len(self.progressive_steps):
                target_duration = self.progressive_steps[self.current_attempt]
                
                if self.speech_audio_duration >= target_duration:
                    return self._perform_detection(sample_rate)
                    
            # 检查是否超过最大时长
            max_duration = self.progressive_steps[-1] if self.progressive_steps else 2.4
            if self.speech_audio_duration >= max_duration:
                return self._perform_detection(sample_rate, is_final=True)
                
            return None
            
        except Exception as e:
            logger.error(f"Error checking detection point: {e}")
            return None

    def _perform_detection(self, sample_rate: int, is_final: bool = False) -> Optional[LIDResult]:
        """执行语种检测"""
        try:
            self.state = LIDState.PROCESSING
            self.current_attempt += 1

            # 合并语音音频数据
            if not self.speech_audio_buffer:
                logger.warning("No speech audio available for detection")
                return self._create_fallback_result(is_final)

            merged_audio = np.concatenate(self.speech_audio_buffer)

            logger.debug(f"Performing LID detection (attempt {self.current_attempt}, "
                        f"duration: {self.speech_audio_duration:.2f}s)")

            # 执行LID推理
            result = self.lid_engine.infer({'audio': merged_audio})

            if not result['success']:
                logger.error(f"LID inference failed: {result.get('error', 'Unknown error')}")
                return self._create_fallback_result(is_final)

            # 创建结果对象
            lid_result = LIDResult(
                language=result['language'],
                confidence=result['confidence'],
                audio_duration=self.speech_audio_duration,
                attempt_count=self.current_attempt,
                all_predictions=result['all_predictions'],
                is_final=False
            )

            # 检查是否满足早停条件
            if result['confidence'] >= self.early_stop_threshold:
                logger.info(f"Early stop triggered: confidence {result['confidence']:.3f} >= {self.early_stop_threshold}")
                lid_result.is_final = True
                self.state = LIDState.COMPLETED
                self.last_result = lid_result
                return lid_result

            # 检查是否满足基本置信度要求
            if result['confidence'] >= self.confidence_threshold:
                # 如果是最后一次尝试或达到最大时长，返回最终结果
                if is_final or self.current_attempt >= self.max_attempts:
                    lid_result.is_final = True
                    self.state = LIDState.COMPLETED
                    self.last_result = lid_result
                    return lid_result
                else:
                    # 继续收集更多音频以提高置信度
                    self.state = LIDState.COLLECTING_AUDIO
                    return lid_result
            else:
                # 置信度不够，继续收集音频
                if is_final or self.current_attempt >= self.max_attempts:
                    # 已达到最大尝试次数，返回最佳结果或回退
                    if result['confidence'] > 0.5:  # 至少有一定置信度
                        lid_result.is_final = True
                        self.state = LIDState.COMPLETED
                        self.last_result = lid_result
                        return lid_result
                    else:
                        return self._create_fallback_result(True)
                else:
                    self.state = LIDState.COLLECTING_AUDIO
                    return None

        except Exception as e:
            logger.error(f"Error performing detection: {e}")
            return self._create_fallback_result(is_final)

    def _create_fallback_result(self, is_final: bool = True) -> LIDResult:
        """创建回退结果"""
        logger.info(f"Using fallback language: {self.fallback_language}")

        result = LIDResult(
            language=self.fallback_language,
            confidence=0.5,  # 中等置信度
            audio_duration=self.speech_audio_duration,
            attempt_count=self.current_attempt,
            all_predictions=[{
                'language': self.fallback_language,
                'confidence': 0.5
            }],
            is_final=is_final
        )

        if is_final:
            self.state = LIDState.COMPLETED
            self.last_result = result

        return result

    def get_current_state(self) -> LIDState:
        """获取当前状态"""
        return self.state

    def get_progress(self) -> Dict[str, Any]:
        """获取进度信息"""
        progress = {
            'state': self.state.value,
            'current_attempt': self.current_attempt,
            'max_attempts': self.max_attempts,
            'total_audio_duration': self.total_audio_duration,
            'speech_audio_duration': self.speech_audio_duration,
            'target_duration': None,
            'progress_percentage': 0.0
        }

        # 计算目标时长和进度
        if self.current_attempt < len(self.progressive_steps):
            target_duration = self.progressive_steps[self.current_attempt]
            progress['target_duration'] = target_duration
            progress['progress_percentage'] = min(100.0,
                (self.speech_audio_duration / target_duration) * 100.0)
        else:
            progress['progress_percentage'] = 100.0

        return progress

    def is_completed(self) -> bool:
        """检查是否已完成"""
        return self.state in [LIDState.COMPLETED, LIDState.FAILED]

    def get_final_result(self) -> Optional[LIDResult]:
        """获取最终结果"""
        if self.is_completed():
            return self.last_result
        return None

    def force_complete(self) -> LIDResult:
        """强制完成检测"""
        if self.state == LIDState.COMPLETED and self.last_result:
            return self.last_result

        if self.speech_audio_buffer:
            # 尝试用当前音频进行检测
            result = self._perform_detection(16000, is_final=True)
            if result:
                return result

        # 返回回退结果
        return self._create_fallback_result(True)

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_attempts': self.current_attempt,
            'total_audio_duration': self.total_audio_duration,
            'speech_audio_duration': self.speech_audio_duration,
            'speech_ratio': (self.speech_audio_duration / max(self.total_audio_duration, 0.001)),
            'processing_time': time.time() - self.start_time,
            'final_result': self.last_result.__dict__ if self.last_result else None
        }
