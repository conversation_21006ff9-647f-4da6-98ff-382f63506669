"""
系统监控器
监控CPU、内存、磁盘、网络等系统资源
"""

import psutil
import logging
import time
import asyncio
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    process_count: int
    load_average: Optional[List[float]] = None


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, collect_interval: float = 30.0, history_size: int = 1000):
        """
        初始化系统监控器
        
        Args:
            collect_interval: 收集间隔（秒）
            history_size: 历史数据保存数量
        """
        self.collect_interval = collect_interval
        self.history_size = history_size
        self.metrics_history: List[SystemMetrics] = []
        
        # 监控状态
        self.is_running = False
        self.monitor_task = None
        
        # 网络基线（用于计算增量）
        self.network_baseline = None
        
        # 告警阈值
        self.cpu_threshold = 80.0
        self.memory_threshold = 85.0
        self.disk_threshold = 90.0
        
    async def start(self):
        """启动监控"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 初始化网络基线
        self._init_network_baseline()
        
        # 启动监控任务
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        
        logger.info("System monitor started")
        
    async def stop(self):
        """停止监控"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
                
        logger.info("System monitor stopped")
        
    def _init_network_baseline(self):
        """初始化网络基线"""
        try:
            net_io = psutil.net_io_counters()
            self.network_baseline = {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'timestamp': time.time()
            }
        except Exception as e:
            logger.warning(f"Failed to initialize network baseline: {e}")
            
    async def _monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 收集系统指标
                metrics = self._collect_system_metrics()
                
                # 添加到历史记录
                self.metrics_history.append(metrics)
                
                # 限制历史记录大小
                if len(self.metrics_history) > self.history_size:
                    self.metrics_history = self.metrics_history[-self.history_size:]
                    
                # 检查告警
                self._check_alerts(metrics)
                
                # 等待下次收集
                await asyncio.sleep(self.collect_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"System monitor error: {e}")
                await asyncio.sleep(self.collect_interval)
                
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / 1024 / 1024
            memory_available_mb = memory.available / 1024 / 1024
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_used_gb = disk.used / 1024 / 1024 / 1024
            disk_free_gb = disk.free / 1024 / 1024 / 1024
            
            # 网络信息
            net_io = psutil.net_io_counters()
            network_bytes_sent = net_io.bytes_sent
            network_bytes_recv = net_io.bytes_recv
            
            # 进程数量
            process_count = len(psutil.pids())
            
            # 负载平均值（Linux/Unix）
            load_average = None
            try:
                load_average = list(psutil.getloadavg())
            except AttributeError:
                # Windows不支持getloadavg
                pass
                
            return SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_percent=disk_percent,
                disk_used_gb=disk_used_gb,
                disk_free_gb=disk_free_gb,
                network_bytes_sent=network_bytes_sent,
                network_bytes_recv=network_bytes_recv,
                process_count=process_count,
                load_average=load_average
            )
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
            # 返回默认值
            return SystemMetrics(
                timestamp=time.time(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                memory_available_mb=0.0,
                disk_percent=0.0,
                disk_used_gb=0.0,
                disk_free_gb=0.0,
                network_bytes_sent=0,
                network_bytes_recv=0,
                process_count=0
            )
            
    def _check_alerts(self, metrics: SystemMetrics):
        """检查告警条件"""
        alerts = []
        
        # CPU告警
        if metrics.cpu_percent > self.cpu_threshold:
            alerts.append(f"High CPU usage: {metrics.cpu_percent:.1f}%")
            
        # 内存告警
        if metrics.memory_percent > self.memory_threshold:
            alerts.append(f"High memory usage: {metrics.memory_percent:.1f}%")
            
        # 磁盘告警
        if metrics.disk_percent > self.disk_threshold:
            alerts.append(f"High disk usage: {metrics.disk_percent:.1f}%")
            
        # 记录告警
        for alert in alerts:
            logger.warning(f"System alert: {alert}")
            
    def get_current_metrics(self) -> Optional[SystemMetrics]:
        """获取当前指标"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None
        
    def get_metrics_history(self, duration_minutes: int = 60) -> List[SystemMetrics]:
        """
        获取指定时间段的历史指标
        
        Args:
            duration_minutes: 时间段（分钟）
            
        Returns:
            历史指标列表
        """
        if not self.metrics_history:
            return []
            
        cutoff_time = time.time() - (duration_minutes * 60)
        return [
            metrics for metrics in self.metrics_history
            if metrics.timestamp >= cutoff_time
        ]
        
    def get_average_metrics(self, duration_minutes: int = 60) -> Dict[str, float]:
        """
        获取指定时间段的平均指标
        
        Args:
            duration_minutes: 时间段（分钟）
            
        Returns:
            平均指标字典
        """
        history = self.get_metrics_history(duration_minutes)
        
        if not history:
            return {}
            
        total_count = len(history)
        
        return {
            "avg_cpu_percent": sum(m.cpu_percent for m in history) / total_count,
            "avg_memory_percent": sum(m.memory_percent for m in history) / total_count,
            "avg_disk_percent": sum(m.disk_percent for m in history) / total_count,
            "avg_process_count": sum(m.process_count for m in history) / total_count,
            "max_cpu_percent": max(m.cpu_percent for m in history),
            "max_memory_percent": max(m.memory_percent for m in history),
            "min_memory_available_mb": min(m.memory_available_mb for m in history)
        }
        
    def get_network_stats(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """
        获取网络统计信息
        
        Args:
            duration_minutes: 时间段（分钟）
            
        Returns:
            网络统计字典
        """
        history = self.get_metrics_history(duration_minutes)
        
        if len(history) < 2:
            return {}
            
        # 计算网络流量增量
        first_metrics = history[0]
        last_metrics = history[-1]
        
        time_diff = last_metrics.timestamp - first_metrics.timestamp
        bytes_sent_diff = last_metrics.network_bytes_sent - first_metrics.network_bytes_sent
        bytes_recv_diff = last_metrics.network_bytes_recv - first_metrics.network_bytes_recv
        
        if time_diff > 0:
            return {
                "bytes_sent_per_sec": bytes_sent_diff / time_diff,
                "bytes_recv_per_sec": bytes_recv_diff / time_diff,
                "total_bytes_sent": bytes_sent_diff,
                "total_bytes_recv": bytes_recv_diff,
                "duration_seconds": time_diff
            }
            
        return {}
        
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统基本信息"""
        try:
            return {
                "platform": psutil.LINUX if hasattr(psutil, 'LINUX') else "unknown",
                "cpu_count": psutil.cpu_count(),
                "cpu_count_logical": psutil.cpu_count(logical=True),
                "memory_total_gb": psutil.virtual_memory().total / 1024 / 1024 / 1024,
                "disk_total_gb": psutil.disk_usage('/').total / 1024 / 1024 / 1024,
                "boot_time": psutil.boot_time(),
                "python_version": psutil.version_info if hasattr(psutil, 'version_info') else None
            }
        except Exception as e:
            logger.error(f"Failed to get system info: {e}")
            return {}
            
    def set_thresholds(self, cpu: float = None, memory: float = None, disk: float = None):
        """设置告警阈值"""
        if cpu is not None:
            self.cpu_threshold = cpu
        if memory is not None:
            self.memory_threshold = memory
        if disk is not None:
            self.disk_threshold = disk
            
        logger.info(f"Alert thresholds updated: CPU={self.cpu_threshold}%, "
                   f"Memory={self.memory_threshold}%, Disk={self.disk_threshold}%")
