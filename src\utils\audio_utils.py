# NOTE torchaudio v2.0 取消了backend的全局设置
# 避免使用 torchaudio.backend.sox_io_backend.load

import hashlib
import os
import random
import threading
import time
from subprocess import PIPE, Popen

import pilk
import torch
import torchaudio

# tid & pid for rename audio file
tid = threading.currentThread().ident
pid = os.getppid()
TEMP_DIR = "/tmp"

def set_temp_dir(temp_dir: str):
    global TEMP_DIR
    os.makedirs(temp_dir, exist_ok=True)
    TEMP_DIR = temp_dir

def save_to_local_path(wav_path, waveform, sr):
    assert sr == 16000
    torchaudio.save(wav_path, waveform, sr, bits_per_sample = 16)

def load_from_local_path(
    wav_path, 
    sample_rate = 16000, 
    num_channel = 1, 
    start = None, 
    end = None, 
    tmp_audio_dir = TEMP_DIR
    ):
    
    # save .silk file
    postfix = os.path.splitext(wav_path)[1]
    if postfix == '.silk':
        wav_new_path = f'{tmp_audio_dir}/' + hashlib.md5(f'{pid}_{tid}_{wav_path}'.encode('utf-8')).hexdigest() + '.wav'
        pilk.silk_to_file(wav_path, wav_new_path, 1, 360, 16000, 16, 1)
        wav_path = wav_new_path
    
    # load file
    if start is not None:
        assert end is not None
        meta = torchaudio.info(wav_path)
        sr, frame_num = meta.sample_rate, meta.num_frames
        start_frame = int(start * sr)
        end_frame = int(end * sr)
        assert start_frame < frame_num
        end_frame = min(frame_num, end_frame)
        waveform, _ = torchaudio.load(
            filepath=wav_path,
            num_frames=end_frame - start_frame,
            frame_offset=start_frame)
    else:
        waveform, sr = torchaudio.load(wav_path)

    # Convert to standard sample_rate
    if sr != sample_rate:
        waveform = torchaudio.transforms.Resample(orig_freq=sr, new_freq=sample_rate)(waveform)
    
    # Convert to mono channel
    assert num_channel == 1
    if waveform.shape[0] > 1:
        waveform = torch.mean(waveform, dim=0, keepdim=True)
    
    return waveform, sample_rate


def load_from_http_url(
    url, 
    sample_rate = 16000, 
    num_channel = 1,
    start = None, 
    end = None, 
    tmp_audio_dir = TEMP_DIR
    ):
    local_file = ""

    pos = url.rfind('.')
    assert pos > 0
    prefix, postfix = url[:pos], url[pos + 1:]
    if postfix == 'wav':
        # download file
        cmd = f'curl -s -L {url}'
        process = Popen(cmd, shell=True, stdout=PIPE)
        local_file = process.stdout
        # waveform, sr = torchaudio.load(file_obj)
        # if start is not None:
        #     assert end is not None
        #     waveform = waveform[:, int(start*sr):int(end*sr)] 
        # if sr != sample_rate:
        #     waveform = torchaudio.transforms.Resample(orig_freq=sr, new_freq=sample_rate)(waveform)
        # assert num_channel == 1
        # if waveform.shape[0] > 1:
        #     waveform = torch.mean(waveform, dim=0, keepdim=True)
    else:
        name = f'{tmp_audio_dir}/' + hashlib.md5(f'{pid}_{tid}_{url}'.encode('utf-8')).hexdigest() + f'.{postfix}'
        cmd = f'wget {url} -O {name}'
        ret = os.system(cmd)
        assert ret == 0
        local_file = name

    waveform, sample_rate = load_from_local_path(local_file, sample_rate = sample_rate, num_channel = num_channel, start = start, end = end)

    return waveform, sample_rate
