产品与服务
语音识别，是将语音音频转为相应语言文字的服务。使用语音识别服务，您可以轻松便捷地将语音识别技术集成到您的应用。

语音识别类包括以下产品：一句话识别、实时语音识别、录音文件转写。

1. 一句话识别：将音频数据以 HTTP 请求或 WebSocket 请求发送到语音识别服务，每次请求只接受不超过 60 秒的音频数据，无自动断句功能。使用 HTTP 方式，最终识别结果将一次性返回；使用 WebSocket 方式，可以在发送数据的同时接收中间识别结果、最终识别结果，用于实时回显。
2. 实时语音识别：对 WebSocket 双工流内的音频数据进行识别，音频时长理论上限 37 小时，可自动断句。实时语音识别可以在发送数据的同时接收中间识别结果、最终识别结果，用于实时回显。
3. 录音文件转写：将音频文件（或音频 URL）以 HTTP 请求发送到语音识别服务，创建异步转写任务。任务创建后，可通过查询接口查询转写进度，如任务正常结束则可获得最终转写结果。录音文件最长不超过 10 个小时（文件大小需控制在 2 GB 以下）。

###### 各产品简要比较如下表所示：
项目/产品	一句话识别	实时语音识别	录音文件转写
功能	识别短音频，一次性返回识别结果，或者边识别边返回识别结果。	识别流式音频，边识别边返回识别结果。	将较长的音频文件转写为文稿或字幕，识别完成后一次性返回结果。
接口协议	HTTP、WebSocket	WebSocket	HTTP
音频限制	60 秒	理论上限约 37 小时	10 小时、2 GB 以下
支持格式	WAV/PCM	WAV/PCM	WAV/PCM/OPUS/MP3/MP4/M4A等
自动断句	无	有	有
词信息/ITN等实用功能	有	有	有
典型使用场景	语音助手	实时字幕	音频文件转写、视频字幕生成


##### 基本术语
###### 采样率（sample rate）
音频采样率是指录音设备在一秒钟内对声音信号的采样次数，采样频率越高声音的还原就越真实越自然。
在使用语音识别服务时，您可以指定音频的采样率，且实际发送的音频采样率须与该参数一致。目前语音识别产品各语种支持 16000 Hz 采样率的音频，汉语（普通话）额外支持 8000 Hz 采样率的音频。
2.5.12 及更高版本中，所有语种都可兼容支持 8000 Hz 采样率的音频（需有对应授权）。

###### 位深度（bit depth）
也叫采样位深、采样位数，是指声卡在采集和播放声音文件时所使用数字声音信号的二进制位数。语音识别各项服务目前仅支持 16 bit 位深的音频。
2.5.12 及更高版本中，支持传入 8/24/32 bit 位深的音频，通过指定 bit_depth 参数，系统将自动转换为 16 bit 音频再进行语音识别处理。

###### 声道（sound channel）
声道（或通道），是指声音在录制或播放时在不同空间位置采集或回放的相互独立的音频信号，所以声道数也就是声音录制时的音源数量或回放时相应的扬声器数量。一句话识别、实时语音识别服务目前仅支持单声道（单通道）音频，录音文件转写服务支持单声道或多声道音频。

###### 语言
服务支持多种语言和方言，您可以指定音频语言（以及国家或地区方言）。支持语言的完整列表请参考下方“语言支持”部分。

###### 中间结果与最终结果
对于流式接口（一句话识别 WebSocket 接口、实时语音识别），识别结果随着语音的输入实时返回。如“今天天气不错”这句话，在识别过程中可能会产生以下识别结果：

```
金
今天
今天天气
今天天气不错
```
其中，前三条称为“中间结果”，最后一条称为“最终结果”。通过设置一句话识别或实时语音识别流式接口的 enable_intermediate_result 参数，可以控制是否返回中间结果。如关闭中间结果，将只返回最终结果，从用户角度上将表现为一次性收到一句话的全部识别结果。开启中间结果，有助于降低用户的等待感，提高用户体验。

注意：
非流式接口（一句话识别 POST 接口、录音文件转写）没有中间结果，仅有最终结果。

###### 词信息
词信息是识别结果的分词内容，分为：中间结果词信息、最终结果词信息，您可以通过连接参数指定是否返回词信息。

项目	中间结果词信息（2.5.5版本开始支持）	最终结果词信息
控制参数	enable_intermediate_words	enable_words
语种支持	仅支持中文相关（语种代码以 zh 开头）的语种	支持全部语种
包含：文本内容、开始时间、结束时间	√	√
包含：词类型（普通/标点/语气词/敏感词）		√
包含：词稳定状态	√	

词稳定状态字段用于表示当前词在中间结果中是否还会发生变化：如果值为 false ，则表示该词在后续的中间结果中仍有可能发生变化；反之则表示该词已经稳定、不会发生变化。

###### 逆文本标准化（ITN）
逆文本标准化（Inverse Text Normalization）是指将语音识别结果中的日期、数字等对象以惯用格式展示，如下表所示：

未开启ITN的结果	开启ITN的结果
百分之二十	20%
一千二百三十四元	1234元
四月三日	4月3日

###### 振幅
音频的振幅决定了音量大小，振幅过大或过小都不利于语音识别的效果，推荐的振幅在 ±10k 左右，此时可以达到语音识别的最佳效果。以下图为例，左侧为正常振幅（范围在 ±10k 左右），右图振幅过小（范围在 ±1k 左右），因此右图中的音频识别效果将受影响。

对于振幅较小的情况，可以从两方面进行改进：一是调整录音方式，比如人与麦克风之间距离、麦克风/录音参数设置等，从原始音频层面解决音量过低的问题；二是使用语音识别服务中的振幅增益参数，对原始音频振幅进行放大，然后再做语音识别处理。


##### 实用功能
###### 逆文本标准化（ITN）
在使用语音识别服务时，您可以通过配置 enable_inverse_text_normalization 参数来指定是否开启 ITN 。

###### 热词优化
对一些特有的人名、地名、产品名、公司名或者某个领域的专有词汇等，可能存在识别准确率不高的情况。对于这些专有词汇，可手动添加作为“热词”，能够显著提升热词列表中的词汇识别准确率。此功能支持的语种，请参考下方“语言支持”部分。
关于热词添加/删除等操作，请查看《语音识别服务热词接口协议》，添加热词后可在识别时配置 hotwords_id 参数传入热词 ID 以使其生效。
2.5.12 及以上版本中，支持一次性热词功能，不必事先创建热词 ID，在一次连接/请求中传入热词列表 hotwords_list 参数即可生效，仅在当次生效。
在使用热词时，可以指定热词的权重。权重越大，对热词列表中的词汇识别准确率越高，但同时也会增加对其他近音词误识别的概率，因此需要权衡配置权重参数。

###### 热词自动提取
2.5.6 及以上版本支持
在创建和修改热词词库时，可以使用热词自动提取功能，从指定的篇章内容中自动抽取人名、地名、机构名等关键词，并用于训练热词。此功能支持的语种，请参考下方“语言支持”部分。

###### 强制替换
对于热词优化仍无法解决的识别错误，可使用强制替换功能，对指定的错误词语进行手动纠正。比如，语音识别服务可能将“5G”误识别为“无忌”，此时可手动添加强制替换规则“无忌→5G”，在识别时配置 correction_words_id 参数传入ID即可实现正确识别。关于强制替换词添加/删除等操作，请查看《语音识别服务强制替换接口协议》。

###### 敏感词过滤
对于一些不希望直接显示的字眼或词汇，可手动添加作为“敏感词”。在识别时配置 forbidden_words_id 参数传入敏感词 ID，能够自动将识别结果（包括中间结果和最终结果）中的敏感词替换成指定字符（默认为星号 *，可通过 service.toml 配置文件设置为其他字符）。关于敏感词的添加/删除等操作，请查看《语音识别服务敏感词接口协议》。此功能仅汉语、英语支持。

###### 语气词过滤
对于口语表达中常见的“嗯”“呃”“这个”等口头语，可手动添加语气词过滤规则，在识别时配置 enable_modal_particle_filter 参数开启语气词过滤后，能够在识别结果（仅最终结果）中自动删去匹配的语气词。

###### 配置方式

模型目录下存在 modal_particle.txt ，可在该文件中添加语气词过滤规则，每条规则占一行，采用正则表达式书写。修改语气词过滤规则后，需要重启服务方可生效。
```
文件路径：
一句话识别/实时语音识别： asr-integrated/model/asr/{lang_type}/post/modal_particle.txt
录音文件转写： asr-integrated/model/asr-file/{lang_type}/post/modal_particle.txt
```
注意
以上几项功能的内部处理顺序为：
热词优化→强制替换→语气词过滤→敏感词过滤

###### 振幅增益
2.5.4 及以上版本支持

对于录音音量（振幅）较小、影响语音识别效果的情况，可以使用 gain 参数调节振幅增益，系统将在内部对原始音频振幅进行放大，然后再做语音识别处理。

###### 流媒体协议支持
2.5.5 及以上版本支持

实时语音识别支持将 RTSP 音视频流作为语音识别的音频源，系统将拉流并对其中的语音数据进行语音识别处理。目前支持 AAC 编码的音频格式。

###### 网络声卡支持
Linux 平台 2.5.7 及以上版本支持

实时语音识别支持将网络声卡（Audio Network Interface）作为语音识别的音频源，系统将从指定声卡和通道编号获取单路（或多路）音频并进行语音识别处理，并在识别结果中返回通道编号。

使用前需在 service.toml 文件中配置 ethname 参数，参数值为网卡名称（可用 ip a 命令查看本机全部网卡）。

###### 当句添加句尾标点/稍后添加句尾标点
2.6.1及以上版本，默认关闭稍后添加句尾标点逻辑。
2.5.5~2.6.0版本，对中文相关（语种代码以 zh 开头）的语种默认启用稍后添加句尾标点逻辑。
2.5.4及之前的版本，仅有当句添加标点的逻辑。

在实时语音识别中，有两种添加句尾标点的逻辑，您可通过 service.toml 配置文件中的 punctuateLater 参数控制是否启用该逻辑：

当句添加标点：将在最终结果（SentenceEnd 事件）中返回句尾标点。但此时因为缺少下一句的文本信息，所以可能导致标点不准确。
稍后添加句尾标点：当前句尾的标点，将根据上下两句综合判断，并在下一句的句首返回，这样有助于提高句尾标点的准确性。仅支持中文相关（语种代码以 zh 开头）的语种。
###### 语音后置静音检测
2.5.6 及以上版本支持

一句话识别支持开启语音后置静音检测功能。设置阈值后，句尾静音时长超过该阈值会自动结束识别。

###### 语音识别结果置信度
2.5.8 及以上版本支持

一句话识别（流式接口）、实时语音识别、录音文件转写（文稿模式）支持返回语音识别结果置信度，表示系统对识别结果的确定性或信任程度。置信度越高，表示系统越有信心确定其返回的识别结果是用户所述内容的准确转录。

###### 录音文件转写输出格式
对于录音文件转写服务，可使用 output 参数配置输出格式。

文稿格式：适用于文稿等长篇文本，根据说话的停顿进行正常断句。
字幕格式：针对字幕进行优化输出，断句结果划分较短，句尾无标点。
###### 录音文件转写自动提取关键词
在上传录音文件时，指定关键词数量上限，系统将自动提取不超过该数量的关键词，并可根据相关性和词频进行排序。此功能仅汉语支持。

###### 录音文件转写语速计算
系统将自动计算平均语速。对于汉语、日语、韩语，单位为字/分钟。对于英语，单位为词/分钟。

###### 返回音量值
2.5.9 及以上版本支持

一句话识别（流式接口）、实时语音识别、录音文件转写支持返回音量（范围 0~100），音量值跟随中间结果、最终结果按句返回，可用于 UI 显示（如音量条等）。

###### 说话人归档
在录音文件转写中，使用说话人归档功能，能够在转写音频内容的同时，识别和区分不同的说话人，在识别结果中返回说话人编号。可手动指定说话人数量，或者由系统自动判断人数。此功能需选配相关模块，2.5.10及以上版本依赖声纹组件（module_vpr），2.5.9 及以下版本依赖说话人归档服务（vpr-file-integrated）。

###### 英语混说和语种标签
2.5.11 及以上版本支持

为了满足用户在不同语言环境下的沟通需求，系统支持英语混说，目前已发布中英混说、日英混说模型。混说模型能够识别在主语言和英语之间自由切换的对话，极大提升了多语言使用者的交流效率。

在开始语音识别前设置 enable_lang_label = true ，切换语种时将自动断句并在 lang_type 参数中返回语种代码。

###### 自动分段
2.5.11 及以上版本支持

系统支持设置 paragraph_condition 参数作为分段的字数条件。当达到设定的字符数时，在下一句中返回新的段落编号。

###### 音频编码
目前，语音识别各产品支持以下音频编码格式。请将 format 字段设置为相应的编码格式。

注意：

目前，一句话识别、实时语音识别支持的音频格式为单声道（mono）格式。录音文件转写支持单声道、多声道格式。

编码格式	说明	一句话识别、实时语音识别	录音文件转写
pcm	非压缩的采样位宽为 16 bit、Little-Endian 的音频。常见于普通的非压缩WAV格式（不包括前 44 字节 WAV 头）。	√	√
wav	采样位宽为 16 bit 的音频。	√	√
opus	封装在 OGG 容器中的 OPUS 格式，framesize 至少为 60 ms。		√
mp3			√
mp4			√
m4a			√
amr			√
3gp			√
aac		√ 实时语音识别RTSP拉流支持	√

###### 语言支持
语言代码采用 language-variant-script-region 的格式。

language：语言（ISO 639-1），全小写，比如中文是 zh，英文 en
variant（可选）：发音或方言（ISO 639-3），全小写，例如普通话 cmn，粤语 yue
script（可选）：书写变体（ISO 15924），首字母大写，例如简体 Hans，繁体 Hant
region：语言使用的地理区域（ISO 3166），全大写，例如中国大陆 CN，中国香港 HK，美国 US
目前，语音识别各产品支持以下语言（需要选配对应语种的模型）。请将 lang_type （或 langType ，由实际使用的 SDK 决定）字段设置为相应的语言代码。

类别	名称	语言代码	热词功能	热词自动提取功能	混说功能
汉语及方言	汉语（普通话）	zh-cmn-Hans-CN	√	√	可选配中英混说
汉语（四川话）	zh-sch-Hans-CN	√	√	支持普通话混说
汉语（重庆话）	zh-cqh-Hans-CN	√	√	支持普通话混说
汉语（贵州话）	zh-gzh-Hans-CN	√	√	支持普通话混说
汉语（湖北武汉话）	zh-hbh-Hans-CN	√	√	支持普通话混说
汉语（河南话）	zh-hnh-Hans-CN	√	√	支持普通话混说
汉语（吴语杭州话）	zh-hnz-Hans-CN	√	√	支持普通话混说
粤语（简体）	zh-yue-Hans-CN			
闽南语	zh-nan-Hans-CN			
汉语（台湾口音）	zh-twh-Hans-CN			
汉语（山东话）	zh-sdh-Hans-CN	√	√	支持普通话混说
汉语（山西话）	zh-cjy-Hans-CN	√	√	支持普通话混说
汉语（陕西话）	zh-sxh-Hans-CN	√	√	支持普通话混说
汉语（云南话）	zh-ynh-Hans-CN	√	√	支持普通话混说
汉语（混合）	zh-mix-Hans-CN	√	√	支持普通话、英语、四川、山东、山西、贵州、河南、武汉、杭州方言混说
中国民族语	藏语	bo-CN			
维吾尔语	ug-CN			
哈萨克语（中国）	kk-CN			
外国语	英语	en-US	√		
日语	ja-JP	√		可选配日英混说
韩语	ko-KR			
俄语	ru-RU			
泰语	th-TH			
越南语	vi-VN			
印地语	hi-IN			
阿拉伯语	ar-SA			
缅甸语	my-MM			
印尼语	id-ID			
马来语	ms-MY			
西班牙语	es-ES			
德语	de-DE			
法语	fr-FR			
意大利语	it-IT			
波兰语	pl-PL			
荷兰语	nl-NL			
葡萄牙语	pt-PT			
哈萨克语（哈萨克斯坦）	kk-KZ			
蒙古语（蒙古国）	mn-MM			
更新时间：2025-04-27