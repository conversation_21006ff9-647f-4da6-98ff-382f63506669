"""
集成监控器
整合系统监控、性能监控和告警管理
"""

import asyncio
import time
import logging
import psutil
from typing import Dict, Any

from .system_monitor import SystemMonitor
from .performance_monitor import global_performance_monitor
from .alert_manager import global_alert_manager

logger = logging.getLogger(__name__)


class IntegratedMonitor:
    """集成监控器"""
    
    def __init__(self):
        self.system_monitor = SystemMonitor()
        self.running = False
        self.update_interval = 30.0  # 更新间隔（秒）
        
    async def start(self):
        """启动集成监控"""
        self.running = True
        logger.info("Integrated monitor started")
        
        # 启动系统监控
        await self.system_monitor.start()
        
        # 启动告警管理器
        asyncio.create_task(global_alert_manager.start())
        
        # 启动指标更新循环
        asyncio.create_task(self._update_metrics_loop())
    
    async def stop(self):
        """停止集成监控"""
        self.running = False
        
        # 停止系统监控
        await self.system_monitor.stop()
        
        # 停止告警管理器
        await global_alert_manager.stop()
        
        logger.info("Integrated monitor stopped")
    
    async def _update_metrics_loop(self):
        """指标更新循环"""
        while self.running:
            try:
                await self._update_system_metrics()
                await self._update_performance_metrics()
                await asyncio.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error updating metrics: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def _update_system_metrics(self):
        """更新系统指标"""
        try:
            # 获取系统指标
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 更新告警管理器的指标
            global_alert_manager.update_metric("cpu_percent", cpu_percent)
            global_alert_manager.update_metric("memory_percent", memory.percent)
            global_alert_manager.update_metric("disk_percent", (disk.used / disk.total) * 100)
            
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
    
    async def _update_performance_metrics(self):
        """更新性能指标"""
        try:
            # 获取性能统计
            stats = global_performance_monitor.get_statistics(time_window=300)  # 5分钟
            
            if stats.get('total_operations', 0) > 0:
                # 计算错误率
                error_rate = 1 - stats.get('success_rate', 1.0)
                global_alert_manager.update_metric("error_rate", error_rate)
                
                # 更新平均响应时间
                avg_duration = stats.get('avg_duration_ms', 0)
                global_alert_manager.update_metric("avg_duration_ms", avg_duration)
                
                # 更新P95响应时间
                p95_duration = stats.get('p95_duration_ms', 0)
                global_alert_manager.update_metric("p95_duration_ms", p95_duration)
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    def get_comprehensive_status(self) -> Dict[str, Any]:
        """获取综合状态"""
        try:
            # 系统状态
            system_metrics = self.system_monitor.get_current_metrics()
            
            # 性能统计
            perf_stats = global_performance_monitor.get_statistics(time_window=3600)
            
            # 告警状态
            active_alerts = global_alert_manager.get_active_alerts()
            alert_stats = global_alert_manager.get_alert_stats()
            
            # 确定整体健康状态
            health_status = "healthy"
            issues = []
            
            # 检查活跃告警
            if active_alerts:
                critical_alerts = [a for a in active_alerts if a.level.value == "critical"]
                warning_alerts = [a for a in active_alerts if a.level.value == "warning"]
                
                if critical_alerts:
                    health_status = "critical"
                    issues.extend([f"Critical: {a.message}" for a in critical_alerts])
                elif warning_alerts:
                    health_status = "warning"
                    issues.extend([f"Warning: {a.message}" for a in warning_alerts])
            
            # 检查系统资源
            if system_metrics:
                if system_metrics.cpu_percent > 90:
                    health_status = "critical" if health_status != "critical" else health_status
                    issues.append(f"High CPU usage: {system_metrics.cpu_percent:.1f}%")
                elif system_metrics.cpu_percent > 80:
                    health_status = "warning" if health_status == "healthy" else health_status
                    issues.append(f"Elevated CPU usage: {system_metrics.cpu_percent:.1f}%")
                
                if system_metrics.memory_percent > 90:
                    health_status = "critical" if health_status != "critical" else health_status
                    issues.append(f"High memory usage: {system_metrics.memory_percent:.1f}%")
                elif system_metrics.memory_percent > 80:
                    health_status = "warning" if health_status == "healthy" else health_status
                    issues.append(f"Elevated memory usage: {system_metrics.memory_percent:.1f}%")
            
            return {
                "health_status": health_status,
                "issues": issues,
                "system_metrics": {
                    "cpu_percent": system_metrics.cpu_percent if system_metrics else 0,
                    "memory_percent": system_metrics.memory_percent if system_metrics else 0,
                    "disk_percent": system_metrics.disk_percent if system_metrics else 0,
                    "timestamp": system_metrics.timestamp if system_metrics else time.time()
                },
                "performance_stats": perf_stats,
                "alert_summary": {
                    "active_count": len(active_alerts),
                    "total_alerts": alert_stats.get("total_alerts", 0),
                    "critical_count": len([a for a in active_alerts if a.level.value == "critical"]),
                    "warning_count": len([a for a in active_alerts if a.level.value == "warning"])
                },
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting comprehensive status: {e}")
            return {
                "health_status": "unknown",
                "issues": [f"Status check failed: {e}"],
                "timestamp": time.time()
            }


# 全局集成监控器实例
global_integrated_monitor = IntegratedMonitor()
