"""
支持Session池的ONNX Runtime推理引擎
解决高并发场景下的session排队问题
"""

import os
import logging
import asyncio
from typing import Dict, Any, Union, List, Optional, Tuple
import numpy as np
import torch
import onnxruntime as ort

from .base_engine import BaseEngine
from .session_pool import ONNXSessionPool

logger = logging.getLogger(__name__)


class PooledONNXEngine(BaseEngine):
    """支持Session池的ONNX Runtime推理引擎"""
    
    def __init__(self, model_path: str, config: Dict[str, Any]):
        """
        初始化ONNX引擎
        
        Args:
            model_path: ONNX模型目录路径
            config: 引擎配置
        """
        super().__init__(model_path, config)
        
        # ONNX模型文件路径
        self.encoder_path = os.path.join(model_path, "encoder.onnx")
        self.ctc_path = os.path.join(model_path, "ctc.onnx")
        self.decoder_path = os.path.join(model_path, "decoder.onnx")
        
        # Session池配置
        pool_config = config.get('pool_config', {})
        self.min_sessions = pool_config.get('min_sessions', 1)
        self.max_sessions = pool_config.get('max_sessions', 5)
        self.session_timeout = pool_config.get('session_timeout', 300.0)
        
        # Session池
        self.encoder_pool: Optional[ONNXSessionPool] = None
        self.ctc_pool: Optional[ONNXSessionPool] = None
        self.decoder_pool: Optional[ONNXSessionPool] = None
        
        # 设备配置
        self.device = config.get("device", "cpu")
        self.device_id = config.get("device_id", 0)
        self.use_quantized = config.get("quantized", False)
        
        # 推理配置
        self.chunk_size = config.get("chunk_size", 16)
        self.left_chunks = config.get("left_chunks", 16)
        self.decoding_window = config.get("decoding_window", 67)
        
    async def load_model(self) -> bool:
        """加载ONNX模型并初始化Session池"""
        try:
            # 设置ONNX Runtime提供者
            providers = self._get_providers()
            
            # 创建编码器Session池
            if os.path.exists(self.encoder_path):
                self.encoder_pool = ONNXSessionPool(
                    model_path=self.encoder_path,
                    providers=providers,
                    min_sessions=self.min_sessions,
                    max_sessions=self.max_sessions,
                    session_timeout=self.session_timeout
                )
                await self.encoder_pool.start()
                logger.info(f"Encoder pool initialized: {self.encoder_path}")
            else:
                logger.error(f"Encoder not found: {self.encoder_path}")
                return False
                
            # 创建CTC Session池
            if os.path.exists(self.ctc_path):
                self.ctc_pool = ONNXSessionPool(
                    model_path=self.ctc_path,
                    providers=providers,
                    min_sessions=self.min_sessions,
                    max_sessions=self.max_sessions,
                    session_timeout=self.session_timeout
                )
                await self.ctc_pool.start()
                logger.info(f"CTC pool initialized: {self.ctc_path}")
            else:
                logger.error(f"CTC not found: {self.ctc_path}")
                return False
                
            # 创建解码器Session池（可选）
            if os.path.exists(self.decoder_path):
                self.decoder_pool = ONNXSessionPool(
                    model_path=self.decoder_path,
                    providers=providers,
                    min_sessions=self.min_sessions,
                    max_sessions=self.max_sessions,
                    session_timeout=self.session_timeout
                )
                await self.decoder_pool.start()
                logger.info(f"Decoder pool initialized: {self.decoder_path}")
                
            self.is_loaded = True
            logger.info("Pooled ONNX engine loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load pooled ONNX engine: {e}")
            return False
            
    async def unload_model(self) -> bool:
        """卸载模型并清理Session池"""
        try:
            if self.encoder_pool:
                await self.encoder_pool.stop()
                self.encoder_pool = None
                
            if self.ctc_pool:
                await self.ctc_pool.stop()
                self.ctc_pool = None
                
            if self.decoder_pool:
                await self.decoder_pool.stop()
                self.decoder_pool = None
                
            self.is_loaded = False
            logger.info("Pooled ONNX engine unloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload pooled ONNX engine: {e}")
            return False
            
    async def infer(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行推理
        
        Args:
            inputs: 输入数据
            
        Returns:
            推理结果
        """
        if not self.is_ready():
            return {"error": "Engine not ready", "success": False}
            
        try:
            # 获取输入特征
            features = inputs.get('features')
            if features is None:
                return {"error": "Missing 'features' in inputs", "success": False}
                
            # 运行编码器
            encoder_outputs = await self._run_encoder_pooled(features)
            if encoder_outputs is None:
                return {"error": "Encoder inference failed", "success": False}
                
            # 运行CTC
            ctc_outputs = await self._run_ctc_pooled(encoder_outputs)
            if ctc_outputs is None:
                return {"error": "CTC inference failed", "success": False}
                
            # 运行解码器（如果有）
            decoder_outputs = None
            if self.decoder_pool:
                decoder_outputs = await self._run_decoder_pooled(encoder_outputs)
                
            return {
                "encoder_outputs": encoder_outputs,
                "ctc_outputs": ctc_outputs,
                "decoder_outputs": decoder_outputs,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Pooled inference failed: {e}")
            return {"error": str(e), "success": False}
            
    async def _run_encoder_pooled(self, features: np.ndarray) -> Optional[np.ndarray]:
        """使用池运行编码器"""
        if not self.encoder_pool:
            return None
            
        session_info = await self.encoder_pool.get_session(timeout=30.0)
        if not session_info:
            logger.error("Failed to get encoder session from pool")
            return None
            
        session_id, session = session_info
        
        try:
            # 运行推理
            input_name = session.get_inputs()[0].name
            output_name = session.get_outputs()[0].name
            
            result = session.run([output_name], {input_name: features})
            
            # 归还session
            await self.encoder_pool.return_session(session_id, success=True)
            
            return result[0]
            
        except Exception as e:
            logger.error(f"Encoder inference error: {e}")
            # 确保在异常情况下也归还session
            try:
                await self.encoder_pool.return_session(session_id, success=False)
            except Exception as return_error:
                logger.error(f"Failed to return encoder session: {return_error}")
            return None
            
    async def _run_ctc_pooled(self, encoder_outputs: np.ndarray) -> Optional[np.ndarray]:
        """使用池运行CTC"""
        if not self.ctc_pool:
            return None
            
        session_info = await self.ctc_pool.get_session(timeout=30.0)
        if not session_info:
            logger.error("Failed to get CTC session from pool")
            return None
            
        session_id, session = session_info
        
        try:
            # 运行推理
            input_name = session.get_inputs()[0].name
            output_name = session.get_outputs()[0].name
            
            result = session.run([output_name], {input_name: encoder_outputs})
            
            # 归还session
            await self.ctc_pool.return_session(session_id, success=True)
            
            return result[0]
            
        except Exception as e:
            logger.error(f"CTC inference error: {e}")
            # 确保在异常情况下也归还session
            try:
                await self.ctc_pool.return_session(session_id, success=False)
            except Exception as return_error:
                logger.error(f"Failed to return CTC session: {return_error}")
            return None
            
    async def _run_decoder_pooled(self, encoder_outputs: np.ndarray) -> Optional[np.ndarray]:
        """使用池运行解码器"""
        if not self.decoder_pool:
            return None
            
        session_info = await self.decoder_pool.get_session(timeout=30.0)
        if not session_info:
            logger.error("Failed to get decoder session from pool")
            return None
            
        session_id, session = session_info
        
        try:
            # 运行推理
            input_name = session.get_inputs()[0].name
            output_name = session.get_outputs()[0].name
            
            result = session.run([output_name], {input_name: encoder_outputs})
            
            # 归还session
            await self.decoder_pool.return_session(session_id, success=True)
            
            return result[0]
            
        except Exception as e:
            logger.error(f"Decoder inference error: {e}")
            # 确保在异常情况下也归还session
            try:
                await self.decoder_pool.return_session(session_id, success=False)
            except Exception as return_error:
                logger.error(f"Failed to return decoder session: {return_error}")
            return None
            
    def _get_providers(self) -> List[str]:
        """获取ONNX Runtime提供者"""
        if self.device == "gpu":
            return [
                ('CUDAExecutionProvider', {
                    'device_id': self.device_id,
                    'arena_extend_strategy': 'kNextPowerOfTwo',
                    'gpu_mem_limit': 2 * 1024 * 1024 * 1024,  # 2GB
                }),
                'CPUExecutionProvider'
            ]
        elif self.device == "npu":
            return ['VitisAIExecutionProvider', 'CPUExecutionProvider']
        else:
            return ['CPUExecutionProvider']
            
    def is_ready(self) -> bool:
        """检查引擎是否就绪"""
        return (self.is_loaded and 
                self.encoder_pool is not None and 
                self.ctc_pool is not None)
                
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            "model_path": self.model_path,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "pools": {}
        }
        
        if self.encoder_pool:
            stats["pools"]["encoder"] = self.encoder_pool.get_statistics()
            
        if self.ctc_pool:
            stats["pools"]["ctc"] = self.ctc_pool.get_statistics()
            
        if self.decoder_pool:
            stats["pools"]["decoder"] = self.decoder_pool.get_statistics()
            
        return stats
