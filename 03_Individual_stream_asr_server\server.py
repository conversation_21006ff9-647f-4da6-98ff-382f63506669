#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  server.py
# Time    :  2025/04/03 12:05:24
# Author  :  lh
# Version :  1.0
# Description:
"""
服务端主程序, 实现基于 WebSocket 的流式语音识别服务。
服务器初始化时会加载 ONNX 模型, 并为每个客户端维护独立的识别状态。
支持多客户端同时连接, 并提供流式识别结果。
注意：使用前需确保已通过 wenet/bin/export_onnx_cpu.py 导出相应模型文件:
encoder.onnx ctc.onnx decoder.onnx
"""

import sys, os
dirpath =  os.path.abspath(os.path.dirname(sys.argv[0]))
envs = [
        f'{dirpath}/modules',
        f'{dirpath}/utils',
        dirpath
    ]
envs_add = [path for path in envs if path not in sys.path]
sys.path = envs_add + sys.path
#print(sys.path)

from typing import Dict, Any, Optional, Set
import asyncio
import torch
import numpy as np
import onnxruntime as ort
import os, time, json, uuid, base64
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from contextlib import asynccontextmanager

from modules.logger import logger  # init logger
from modules.symbol_table import SymbolTable
from modules.config import parse_args, ConfigManager
from modules.feature import FeaturePipeline
from modules.decoder import load_onnx
from modules.connect import ConnectionManager

# 检查授权
from utils.verify_license import verify_license
if not verify_license():
    print(f"License check error.")
    exit(1)
    
# 全局初始化配置
args = parse_args()
HEARTBEAT_INTERVAL = args.heartbeat_interval
# 全局组件: 特征处理管道, 连接管理器
FEAT_PIPE, manager, SYMBOL_TABLE = None, None, None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    生命周期管理函数, 在应用启动时初始化资源, 在关闭时释放资源。
    Args:
        app: FastAPI 应用实例
    Returns:
        异步上下文管理器
    """
    global FEAT_PIPE, manager, SYMBOL_TABLE
    # 加载模型和配置, 全局会话（所有ws连接共享会话, 避免重复加载）
    metadatas = load_onnx(args)
    configs = ConfigManager(args, metadatas).configs
    # 初始化全局组件
    FEAT_PIPE = FeaturePipeline(configs['feat_configs'])  
    SYMBOL_TABLE = SymbolTable(args.onnx_dir, args.dict_path, args.lang_code)
    manager = ConnectionManager(args, configs, FEAT_PIPE, SYMBOL_TABLE)
    

    logger.info("Sever start, init FEAT_PIPE, manager, SYMBOL_TABLE")
    
    yield
    # unload
    del FEAT_PIPE, manager, SYMBOL_TABLE
    FEAT_PIPE, manager, SYMBOL_TABLE = None, None, None
    logger.info("Sever shutdown, delete FEAT_PIPE, manager, SYMBOL_TABLE")


app = FastAPI(
    lifespan=lifespan,
    title="流式语音识别服务",
    description="流式语音识别 WebSocket 服务接口",
    version="1.0"
)

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(
    websocket: WebSocket, 
    client_id: str):
    """
    WebSocket 连接处理函数, 管理客户端连接和消息通信。
    Args:
        websocket: WebSocket 实例
        client_id: 客户端唯一标识
    Returns:
        异步协程
    """
    try:
        # 初始化连接
        await manager.connect(websocket, client_id)
        last_activity = time.time()
        logger.info(f"新建客户连接 \"client_id\": \"{client_id}\"\n")
    
        # 启动协程 receive 用于接受客户消息 并在合适时机向客户发送消息
        receive_task = asyncio.create_task(receive(websocket, client_id, last_activity))

        # 等待任务完成或中断
        done, pending = await asyncio.wait(
            {receive_task}, 
            return_when=asyncio.FIRST_COMPLETED
        )
        for task in pending:
            task.cancel()
    
    except Exception as e:
        logger.warning(f"client_id: {client_id} - 未预期的错误发生: {e}")
        logger.info(f"client_id: {client_id} - 关闭连接，清理资源")
        await manager.disconnect(client_id)


async def receive(
    websocket: WebSocket, 
    client_id: str,
    last_activity: float):
    """
    接收客户端消息并处理数据包。
    Args:
        websocket: WebSocket 实例
        client_id: 客户端唯一标识
        last_activity: 上次活动(即建立连接)的时间
    Returns:
        异步协程
    """
    try:
        while True:
            # 1. 接收 JSON 格式数据包
            json_data = await asyncio.wait_for(
                    websocket.receive_json(),
                    timeout=HEARTBEAT_INTERVAL
            ) # 可能抛出 asyncio.TimeoutError 等待数据包超时 / json.JSONDecodeError (客户端发送了错误格式的数据包, 由客户端造成关闭连接)
            last_activity = time.time()  # 更新最后活动时间
            
            # 2. 解析数据包
            pass_check = await manager.on_check(client_id, json_data)
            if not pass_check:
                logger.warning(f"client_id:{client_id} - 数据包解析失败, 服务端主动断开连接")
                raise WebSocketDisconnect

            # 3. 持续解码和返回结果
            decode_success, decode_something = await manager.on_decode(client_id)
            if not decode_success:
                logger.warning(f"client_id:{client_id} - 服务段解码失败, 服务端主动断开连接")
                raise WebSocketDisconnect

            if decode_something:
                await manager.on_result(decode_something, client_id)   

            # 4. 发送最后结果后关闭连接
            # 正常来说 客户端会关闭连接 就会接收不到客户数据包 这样就触发 WebSocketDisconnect 退出了轮询, 
            # 以防万一 客户端不关闭连接 服务段在发送了最后识别结果之后 关闭ws连接
            if manager.client_states[client_id]['is_final_result']:
                logger.info(f"client_id:{client_id} - 已发送最后一个识别结果, 主动关闭客户连接")
                raise WebSocketDisconnect 
    
    # 关闭连接触发
    except WebSocketDisconnect:
        logger.info(f"client_id: {client_id} - 关闭连接，清理资源")
        await manager.disconnect(client_id)

    # 等待数据包超时
    except asyncio.TimeoutError:
        # 心跳检查
        logger.debug(f"心跳检查 {time.time() - last_activity}")
        if time.time() - last_activity > HEARTBEAT_INTERVAL:
            logger.warning(f"client_id:{client_id} - 心跳超时，未在{HEARTBEAT_INTERVAL}秒内接收到数据包。")
            await manager.on_error(code=4010, message=f"心跳超时，未在{HEARTBEAT_INTERVAL}秒内接收到数据包", client_id=client_id)
            # manager.on_error 中会调用 disconnect, 无需再调用一次

    # receive_json() 触发
    except json.JSONDecodeError as e:
        logger.warning(f"client_id:{client_id} - 接收数据包失败(json.JSONDecodeError), 断开连接: {e}")
        await manager.on_error(code=4011, message="接收数据包失败(json.JSONDecodeError), 断开连接", client_id=client_id)
        # manager.on_error 中会调用 disconnect, 无需再调用一次

def start_server():
    """
    启动 FastAPI 服务器。
    Returns:
        None
    """
    import uvicorn
    logger.info(f"Run server on {args.host}:{args.port}")
    uvicorn.run("server:app", host=args.host, port=args.port, reload=False, workers=1)

if __name__ == '__main__':
    start_server()
