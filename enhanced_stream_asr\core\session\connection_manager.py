"""
连接管理器
管理WebSocket连接的状态和生命周期
"""

import asyncio
import logging
import time
from typing import Dict, Set, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ConnectionState(Enum):
    """连接状态枚举"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"
    ERROR = "error"


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    client_id: str
    remote_address: str
    user_agent: str
    state: ConnectionState
    created_time: float
    last_activity_time: float
    bytes_sent: int = 0
    bytes_received: int = 0
    messages_sent: int = 0
    messages_received: int = 0
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity_time = time.time()
        
    def add_sent_data(self, size: int):
        """添加发送数据统计"""
        self.bytes_sent += size
        self.messages_sent += 1
        self.update_activity()
        
    def add_received_data(self, size: int):
        """添加接收数据统计"""
        self.bytes_received += size
        self.messages_received += 1
        self.update_activity()


class ConnectionManager:
    """连接管理器"""
    
    def __init__(self, max_connections: int = 100):
        """
        初始化连接管理器
        
        Args:
            max_connections: 最大连接数
        """
        self.max_connections = max_connections
        self.connections: Dict[str, ConnectionInfo] = {}
        self.client_connections: Dict[str, Set[str]] = {}  # client_id -> connection_ids
        
        # 统计信息
        self.total_connections_created = 0
        self.total_bytes_sent = 0
        self.total_bytes_received = 0
        
    def can_accept_connection(self) -> bool:
        """检查是否可以接受新连接"""
        return len(self.connections) < self.max_connections
        
    def add_connection(
        self, 
        connection_id: str, 
        client_id: str, 
        remote_address: str = "",
        user_agent: str = ""
    ) -> bool:
        """
        添加新连接
        
        Args:
            connection_id: 连接ID
            client_id: 客户端ID
            remote_address: 远程地址
            user_agent: 用户代理
            
        Returns:
            是否添加成功
        """
        if not self.can_accept_connection():
            logger.warning(f"Cannot accept new connection: limit reached ({self.max_connections})")
            return False
            
        if connection_id in self.connections:
            logger.warning(f"Connection already exists: {connection_id}")
            return False
            
        # 创建连接信息
        connection_info = ConnectionInfo(
            connection_id=connection_id,
            client_id=client_id,
            remote_address=remote_address,
            user_agent=user_agent,
            state=ConnectionState.CONNECTING,
            created_time=time.time(),
            last_activity_time=time.time()
        )
        
        # 添加到连接字典
        self.connections[connection_id] = connection_info
        
        # 添加到客户端连接映射
        if client_id not in self.client_connections:
            self.client_connections[client_id] = set()
        self.client_connections[client_id].add(connection_id)
        
        # 更新统计
        self.total_connections_created += 1
        
        logger.info(f"Connection added: {connection_id} (client: {client_id})")
        return True
        
    def remove_connection(self, connection_id: str) -> bool:
        """
        移除连接
        
        Args:
            connection_id: 连接ID
            
        Returns:
            是否移除成功
        """
        connection_info = self.connections.get(connection_id)
        if not connection_info:
            return False
            
        try:
            # 从客户端连接映射中移除
            client_id = connection_info.client_id
            if client_id in self.client_connections:
                self.client_connections[client_id].discard(connection_id)
                if not self.client_connections[client_id]:
                    del self.client_connections[client_id]
                    
            # 更新统计
            self.total_bytes_sent += connection_info.bytes_sent
            self.total_bytes_received += connection_info.bytes_received
            
            # 移除连接
            del self.connections[connection_id]
            
            logger.info(f"Connection removed: {connection_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing connection {connection_id}: {e}")
            return False
            
    def get_connection(self, connection_id: str) -> Optional[ConnectionInfo]:
        """获取连接信息"""
        return self.connections.get(connection_id)
        
    def update_connection_state(self, connection_id: str, state: ConnectionState) -> bool:
        """
        更新连接状态
        
        Args:
            connection_id: 连接ID
            state: 新状态
            
        Returns:
            是否更新成功
        """
        connection_info = self.connections.get(connection_id)
        if not connection_info:
            return False
            
        old_state = connection_info.state
        connection_info.state = state
        connection_info.update_activity()
        
        logger.debug(f"Connection {connection_id} state changed: {old_state.value} -> {state.value}")
        return True
        
    def record_message_sent(self, connection_id: str, message_size: int) -> bool:
        """
        记录发送消息
        
        Args:
            connection_id: 连接ID
            message_size: 消息大小
            
        Returns:
            是否记录成功
        """
        connection_info = self.connections.get(connection_id)
        if not connection_info:
            return False
            
        connection_info.add_sent_data(message_size)
        return True
        
    def record_message_received(self, connection_id: str, message_size: int) -> bool:
        """
        记录接收消息
        
        Args:
            connection_id: 连接ID
            message_size: 消息大小
            
        Returns:
            是否记录成功
        """
        connection_info = self.connections.get(connection_id)
        if not connection_info:
            return False
            
        connection_info.add_received_data(message_size)
        return True
        
    def get_client_connections(self, client_id: str) -> Set[str]:
        """获取客户端的所有连接"""
        return self.client_connections.get(client_id, set()).copy()
        
    def get_active_connections(self) -> Dict[str, ConnectionInfo]:
        """获取所有活跃连接"""
        return {
            conn_id: conn_info 
            for conn_id, conn_info in self.connections.items()
            if conn_info.state == ConnectionState.CONNECTED
        }
        
    def get_connection_count(self) -> int:
        """获取当前连接数"""
        return len(self.connections)
        
    def get_active_connection_count(self) -> int:
        """获取活跃连接数"""
        return len(self.get_active_connections())
        
    def cleanup_inactive_connections(self, timeout: float = 300.0) -> int:
        """
        清理不活跃的连接
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            清理的连接数
        """
        current_time = time.time()
        inactive_connections = []
        
        for connection_id, connection_info in self.connections.items():
            if current_time - connection_info.last_activity_time > timeout:
                inactive_connections.append(connection_id)
                
        # 移除不活跃的连接
        for connection_id in inactive_connections:
            self.remove_connection(connection_id)
            
        if inactive_connections:
            logger.info(f"Cleaned up {len(inactive_connections)} inactive connections")
            
        return len(inactive_connections)
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        active_connections = self.get_active_connections()
        
        # 计算平均连接时长
        current_time = time.time()
        total_connection_time = sum(
            current_time - conn.created_time 
            for conn in self.connections.values()
        )
        avg_connection_time = (
            total_connection_time / len(self.connections) 
            if self.connections else 0.0
        )
        
        # 计算总流量
        current_bytes_sent = sum(conn.bytes_sent for conn in self.connections.values())
        current_bytes_received = sum(conn.bytes_received for conn in self.connections.values())
        
        return {
            "current_connections": len(self.connections),
            "active_connections": len(active_connections),
            "max_connections": self.max_connections,
            "total_connections_created": self.total_connections_created,
            "avg_connection_time": avg_connection_time,
            "current_bytes_sent": current_bytes_sent,
            "current_bytes_received": current_bytes_received,
            "total_bytes_sent": self.total_bytes_sent + current_bytes_sent,
            "total_bytes_received": self.total_bytes_received + current_bytes_received,
            "unique_clients": len(self.client_connections)
        }
        
    def get_connection_details(self) -> List[Dict[str, Any]]:
        """获取所有连接的详细信息"""
        return [
            {
                "connection_id": conn.connection_id,
                "client_id": conn.client_id,
                "remote_address": conn.remote_address,
                "state": conn.state.value,
                "created_time": conn.created_time,
                "last_activity_time": conn.last_activity_time,
                "connection_duration": time.time() - conn.created_time,
                "bytes_sent": conn.bytes_sent,
                "bytes_received": conn.bytes_received,
                "messages_sent": conn.messages_sent,
                "messages_received": conn.messages_received
            }
            for conn in self.connections.values()
        ]
