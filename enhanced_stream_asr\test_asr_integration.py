#!/usr/bin/env python3
"""
ASR引擎集成测试脚本
测试新集成的StreamingASREngine是否正常工作
"""

import asyncio
import logging
import numpy as np
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.asr import StreamingASREngine, SymbolTable, FeaturePipeline
from utils.config import ConfigManager
from utils.logger import setup_logger

# 设置日志
logger = setup_logger("test_asr", level=logging.DEBUG)


async def test_symbol_table():
    """测试符号表"""
    logger.info("Testing SymbolTable...")
    
    # 创建一个简单的测试词典
    test_dict_path = "test_dict.txt"
    with open(test_dict_path, 'w', encoding='utf-8') as f:
        f.write("<blank> 0\n")
        f.write("<unk> 1\n")
        f.write("你 2\n")
        f.write("好 3\n")
        f.write("世 4\n")
        f.write("界 5\n")
    
    try:
        symbol_table = SymbolTable(test_dict_path, "zh")
        
        # 测试ID到token的转换
        tokens = symbol_table.ids2tokens([2, 3, 4, 5])
        logger.info(f"IDs to tokens: {tokens}")
        
        # 测试token到ID的转换
        ids = symbol_table.tokens2ids(["你", "好", "世", "界"])
        logger.info(f"Tokens to IDs: {ids}")
        
        # 测试字符映射
        text = symbol_table.char_map("你好<blank>世界")
        logger.info(f"Char mapping: {text}")
        
        logger.info("SymbolTable test passed!")
        
    finally:
        # 清理测试文件
        if os.path.exists(test_dict_path):
            os.remove(test_dict_path)


async def test_feature_pipeline():
    """测试特征提取管道"""
    logger.info("Testing FeaturePipeline...")
    
    try:
        config = {
            'feat_type': 'fbank',
            'num_mel_bins': 80,
            'sample_rate': 16000,
            'frame_length': 25,
            'frame_shift': 10,
            'dither': 1.0
        }
        
        pipeline = FeaturePipeline(config)
        
        # 生成测试音频数据（1秒的正弦波）
        sample_rate = 16000
        duration = 1.0
        frequency = 440  # A4音符
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.5
        
        # 转换为16位PCM
        audio_pcm = (audio_data * 32767).astype(np.int16)
        audio_bytes = audio_pcm.tobytes()
        
        # 提取特征
        features = pipeline.extract_features(audio_bytes)
        logger.info(f"Feature shape: {features.shape}")
        logger.info(f"Feature type: {features.dtype}")
        
        logger.info("FeaturePipeline test passed!")
        
    except Exception as e:
        logger.error(f"FeaturePipeline test failed: {e}")
        raise


async def test_streaming_asr_engine():
    """测试流式ASR引擎"""
    logger.info("Testing StreamingASREngine...")
    
    # 注意：这个测试需要实际的模型文件，如果没有模型文件会失败
    # 这里主要测试引擎的初始化逻辑
    
    try:
        config = {
            'language': 'zh',
            'model_path': 'models/zh',  # 这个路径可能不存在
            'dict_path': 'models/zh/units.txt',
            'device': 'cpu',
            'device_id': 0,
            'quantized': True,
            'chunk_size': 16,
            'left_chunks': 16,
            'right_context': 7,
            'decoding_window': 67,
            'subsampling_rate': 4,
            'blank_interval': 0.5,
            'min_sessions': 1,
            'max_sessions': 4,
            'session_timeout': 300,
            'feature': {
                'feat_type': 'fbank',
                'num_mel_bins': 80,
                'sample_rate': 16000,
                'frame_length': 25,
                'frame_shift': 10,
                'dither': 1.0
            },
            'num_blocks': 12,
            'head': 8,
            'output_size': 512,
            'cnn_module_kernel': 15
        }
        
        engine = StreamingASREngine('models/zh', config)
        
        # 测试引擎创建
        logger.info("StreamingASREngine created successfully")
        
        # 注意：实际的初始化需要模型文件，这里只测试创建
        logger.info("StreamingASREngine basic test passed!")
        
    except Exception as e:
        logger.warning(f"StreamingASREngine test failed (expected if no models): {e}")


async def test_config_integration():
    """测试配置集成"""
    logger.info("Testing config integration...")
    
    try:
        config_manager = ConfigManager("configs")
        
        # 测试获取语言配置
        zh_config = config_manager.get_language_config("zh")
        if zh_config:
            logger.info(f"Chinese config loaded: {zh_config.get('name')}")
            model_config = zh_config.get('model', {})
            logger.info(f"Model path: {model_config.get('model_path')}")
            logger.info(f"Dict path: {model_config.get('dict_path')}")
        else:
            logger.warning("Chinese config not found")
            
        en_config = config_manager.get_language_config("en")
        if en_config:
            logger.info(f"English config loaded: {en_config.get('name')}")
        else:
            logger.warning("English config not found")
            
        logger.info("Config integration test passed!")
        
    except Exception as e:
        logger.error(f"Config integration test failed: {e}")
        raise


async def main():
    """主测试函数"""
    logger.info("Starting ASR integration tests...")
    
    try:
        # 测试符号表
        await test_symbol_table()
        
        # 测试特征提取
        await test_feature_pipeline()
        
        # 测试配置集成
        await test_config_integration()
        
        # 测试ASR引擎（基础测试）
        await test_streaming_asr_engine()
        
        logger.info("All tests completed!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return 1
        
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
