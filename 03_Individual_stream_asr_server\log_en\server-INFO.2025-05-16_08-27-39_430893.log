2025-05-16 08:27:39.629 | INFO  | importlib._bootstrap:_call_with_frames_removed:219 - 加载配置: /ws/online_asr_server/server_config_en.yaml
2025-05-16 08:27:39.662 | INFO  | __main__:<module>    :5 - Run server on 0.0.0.0:10080
2025-05-16 08:27:39.698 | INFO  | contextlib:__aenter__  :171 - 加载模型: /ws/MODELS/online_onnx_en, fp16: False, quant: True, device: cpu
2025-05-16 08:27:40.529 | INFO  | contextlib:__aenter__  :171 - 加载词表: /ws/MODELS/online_onnx_en/units.txt
2025-05-16 08:27:40.536 | INFO  | contextlib:__aenter__  :171 - Sever start, init FEAT_PIPE, manager, SYMBOL_TABLE
2025-05-16 08:27:41.940 | INFO  | fastapi.routing:app         :383 - 新建客户连接 "client_id": "000"

2025-05-16 08:27:42.224 | INFO  | asyncio.events:_run        :81 - client_id:000 - >>> [解析] 第0个数据包, 累计帧数: 38
2025-05-16 08:27:42.623 | INFO  | asyncio.events:_run        :81 - client_id:000 - >>> [解析] 第1个数据包, 累计帧数: 76
2025-05-16 08:27:43.022 | INFO  | asyncio.events:_run        :81 - client_id:000 - >>> [解析] 第2个数据包, 累计帧数: 114
2025-05-16 08:27:43.424 | INFO  | asyncio.events:_run        :81 - client_id:000 - >>> [解析] 第3个数据包, 累计帧数: 152
2025-05-16 08:27:43.734 | INFO  | asyncio.events:_run        :81 - client_id: 000 - 关闭连接，清理资源
2025-05-16 08:27:43.735 | INFO  | asyncio.events:_run        :81 - 关闭 ws 连接
2025-05-16 08:27:43.735 | INFO  | asyncio.events:_run        :81 - ws 连接已经关闭了
2025-05-16 08:27:43.735 | INFO  | asyncio.events:_run        :81 - ASRDecoder 显式释放资源
2025-05-16 08:27:43.735 | INFO  | asyncio.events:_run        :81 - Encoder 显式释放资源
2025-05-16 08:27:43.735 | INFO  | asyncio.events:_run        :81 - CTCPrefixBeamSearch 显式释放资源
2025-05-16 08:27:45.255 | INFO  | contextlib:__aexit__   :178 - Sever shutdown, delete FEAT_PIPE, manager, SYMBOL_TABLE
