# Enhanced Stream ASR Server

基于Individual版本优化的增强流式语音识别服务器

## 🚀 主要特性

1. **🎤 Web界面支持** - 浏览器麦克风输入，实时语音识别
2. **🔄 三阶段WebSocket协议** - 握手、数据传输、断开的完整协议
3. **🌍 可配置分隔符** - 支持多语种自定义分隔符（中文全角逗号、英文半角逗号等）
4. **🤖 自动语种识别** - VAD + LID + ASR 完整流程，支持渐进式语种检测
5. **🏗️ 模块化架构** - 高度可扩展的设计，支持多种推理引擎
6. **📊 实时监控** - 连接状态、识别统计、性能监控
7. **🔧 灵活配置** - 支持多语种配置、热词、标点符号预测等

## 🎯 核心功能

### 自动语种识别流程
1. **VAD检测**: 检测有效语音（过滤静音和噪音）
2. **渐进式LID**: 0.4s → 0.8s → 2.4s 逐步提高识别准确度
3. **动态ASR**: 根据识别的语种自动调用相应的ASR模块
4. **智能分隔符**: 根据语种和时间间隔自动添加合适的分隔符

### 支持的语种
- 🇨🇳 **中文** (zh) - 支持热词、标点符号、ITN
- 🇺🇸 **英文** (en) - 支持标点符号、ITN
- 🇷🇺 **俄语** (ru) - 支持标点符号
- 🇨🇳 **维语** (ug) - 基础识别
- 🇰🇿 **哈萨克语** (kk) - 基础识别

## 📁 项目结构

```
enhanced_stream_asr/
├── 🏗️ core/                    # 核心模块
│   ├── engines/            # 推理引擎 (ONNX, Triton)
│   ├── session/            # 会话管理
│   ├── audio/              # 音频处理 (VAD, 特征提取)
│   ├── lid/                # 语种识别 (渐进式LID)
│   └── asr/                # 语音识别 (多语种ASR)
├── 🌐 api/                     # API接口
│   ├── websocket/          # WebSocket处理 (三阶段协议)
│   └── http/               # HTTP接口 (REST API)
├── 🛠️ utils/                   # 工具模块
│   ├── config/             # 配置管理
│   ├── monitoring/         # 监控指标
│   └── logger.py           # 日志工具
├── 🎨 web/                     # Web界面
│   ├── static/             # 静态资源 (HTML, CSS, JS)
│   └── templates/          # 模板文件
├── ⚙️ configs/                 # 配置文件
│   ├── lang_configs/       # 语种配置 (分隔符、模型路径等)
│   ├── server_config.yaml  # 服务器配置
│   └── lid_config.yaml     # 语种识别配置
├── 📄 requirements.txt         # Python依赖
├── 🚀 server.py               # 主服务器
└── 📖 README.md               # 项目文档
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd enhanced_stream_asr

# 创建虚拟环境 (推荐)
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 模型准备

将ASR模型文件放置到对应目录：

```
models/
├── zh/                 # 中文模型
│   ├── encoder.onnx
│   ├── ctc.onnx
│   ├── decoder.onnx
│   ├── units.txt
│   └── hotwords.txt
├── en/                 # 英文模型
│   ├── encoder.onnx
│   ├── ctc.onnx
│   └── units.txt
├── lid/                # 语种识别模型
│   └── lid_model.onnx
└── ...
```

### 3. 启动服务器

```bash
# 启动服务器
python server.py

# 或指定配置文件
python server.py --config configs/server_config.yaml
```

### 4. 访问Web界面

打开浏览器访问: http://localhost:8080

## ⚙️ 配置说明

### 服务器配置 (`configs/server_config.yaml`)

```yaml
server:
  host: "0.0.0.0"
  port: 8080

audio:
  sample_rate: 16000
  chunk_duration: 0.4

asr:
  supported_languages: ["zh", "en", "ru", "ug", "kk"]
  enable_auto_language_detection: true

lid:
  progressive_steps: [0.4, 0.8, 1.2, 1.6, 2.0, 2.4]
  confidence_threshold: 0.8
```

### 语种配置 (`configs/lang_configs/zh.yaml`)

```yaml
code: "zh"
name: "中文"
separator: "，"              # 中文全角逗号
silence_threshold: 0.35     # 静音阈值

model:
  model_path: "models/zh/encoder.onnx"
  vocabulary_path: "models/zh/units.txt"
  hotwords_path: "models/zh/hotwords.txt"

features:
  enable_punctuation: true
  enable_itn: true
  enable_hotwords: true
```

## 🌐 API文档

### WebSocket API

#### 连接端点
```
ws://localhost:8080/ws/stream
```

#### 三阶段协议

**1. 握手阶段**
```json
{
  "type": "handshake_request",
  "client_id": "client_123",
  "auto_language_detection": true,
  "sample_rate": 16000,
  "enable_intermediate_result": true,
  "custom_separator": null
}
```

**2. 数据传输阶段**
```json
{
  "type": "audio_data",
  "session_id": "session_456",
  "sequence_id": 1,
  "audio_data": "base64_encoded_audio",
  "is_final": false
}
```

**3. 断开阶段**
```json
{
  "type": "disconnect_request",
  "session_id": "session_456"
}
```

### HTTP API

#### 获取支持的语种
```http
GET /api/languages
```

#### 获取服务器配置
```http
GET /api/config
```

#### 健康检查
```http
GET /health
```

## 🔧 高级功能

### 自定义分隔符

可以为不同语种配置不同的分隔符：

```yaml
# 中文配置
separator: "，"        # 全角逗号

# 英文配置
separator: ", "        # 半角逗号+空格

# 维语配置
separator: "، "        # 维语逗号+空格
```

### 渐进式语种识别

系统会在以下时间点进行语种识别：
- 0.4秒: 初步识别
- 0.8秒: 确认识别
- 1.2秒: 进一步确认
- 2.4秒: 最终确认

### 热词支持

中文支持热词增强，将热词放在 `models/zh/hotwords.txt` 文件中：

```
北京
上海
深圳
人工智能
语音识别
```

## 📊 监控和日志

### 实时监控

Web界面提供实时监控：
- 连接状态
- 音频时长
- 识别次数
- 平均延迟
- 检测语种

### 日志配置

```yaml
logging:
  level: "INFO"
  file: "logs/asr_server.log"
  max_size: "10MB"
  backup_count: 5
```

## 🚀 部署建议

### 开发环境
```bash
python server.py
```

### 生产环境
```bash
# 使用Gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker server:app

# 使用Docker
docker build -t enhanced-asr .
docker run -p 8080:8080 enhanced-asr
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 基于 Individual Stream ASR Server 项目
- 使用 FastAPI 和 WebSocket 技术
- 集成 ONNX Runtime 推理引擎
