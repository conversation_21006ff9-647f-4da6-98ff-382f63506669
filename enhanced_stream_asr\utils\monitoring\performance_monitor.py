"""
性能监控器
"""

import time
import threading
import psutil
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from collections import deque
import logging

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: float
    session_id: str
    operation: str
    duration_ms: float
    memory_mb: float
    cpu_percent: float
    success: bool
    error_message: str = ""


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 10000):
        """
        初始化性能监控器
        
        Args:
            max_history: 最大历史记录数
        """
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self.lock = threading.RLock()
        
        # 统计计数器
        self.operation_counters = {}
        self.error_counters = {}
        
    def record_operation(self, session_id: str, operation: str, 
                        duration_ms: float, success: bool = True, 
                        error_message: str = ""):
        """
        记录操作性能
        
        Args:
            session_id: 会话ID
            operation: 操作名称
            duration_ms: 持续时间（毫秒）
            success: 是否成功
            error_message: 错误消息（如果有）
        """
        with self.lock:
            metric = PerformanceMetrics(
                timestamp=time.time(),
                session_id=session_id,
                operation=operation,
                duration_ms=duration_ms,
                memory_mb=self._get_memory_usage(),
                cpu_percent=self._get_cpu_usage(),
                success=success,
                error_message=error_message
            )
            self.metrics_history.append(metric)
            
            # 更新计数器
            self.operation_counters[operation] = self.operation_counters.get(operation, 0) + 1
            if not success:
                self.error_counters[operation] = self.error_counters.get(operation, 0) + 1
    
    def get_statistics(self, operation: str = None, 
                      time_window: int = 3600) -> Dict[str, Any]:
        """
        获取统计信息
        
        Args:
            operation: 操作名称（可选，为None时统计所有操作）
            time_window: 时间窗口（秒）
            
        Returns:
            统计信息字典
        """
        with self.lock:
            cutoff_time = time.time() - time_window
            filtered_metrics = [
                m for m in self.metrics_history 
                if m.timestamp >= cutoff_time and 
                (operation is None or m.operation == operation)
            ]
            
            if not filtered_metrics:
                return {
                    "total_operations": 0,
                    "success_rate": 0.0,
                    "avg_duration_ms": 0.0,
                    "min_duration_ms": 0.0,
                    "max_duration_ms": 0.0,
                    "p95_duration_ms": 0.0,
                    "p99_duration_ms": 0.0
                }
            
            durations = [m.duration_ms for m in filtered_metrics]
            success_count = sum(1 for m in filtered_metrics if m.success)
            
            return {
                "total_operations": len(filtered_metrics),
                "success_rate": success_count / len(filtered_metrics),
                "avg_duration_ms": sum(durations) / len(durations),
                "min_duration_ms": min(durations),
                "max_duration_ms": max(durations),
                "p95_duration_ms": self._percentile(durations, 95),
                "p99_duration_ms": self._percentile(durations, 99),
                "avg_memory_mb": sum(m.memory_mb for m in filtered_metrics) / len(filtered_metrics),
                "avg_cpu_percent": sum(m.cpu_percent for m in filtered_metrics) / len(filtered_metrics)
            }
    
    def get_operation_summary(self, time_window: int = 3600) -> Dict[str, Dict[str, Any]]:
        """
        获取操作汇总统计
        
        Args:
            time_window: 时间窗口（秒）
            
        Returns:
            按操作分组的统计信息
        """
        with self.lock:
            cutoff_time = time.time() - time_window
            filtered_metrics = [
                m for m in self.metrics_history 
                if m.timestamp >= cutoff_time
            ]
            
            # 按操作分组
            operation_groups = {}
            for metric in filtered_metrics:
                if metric.operation not in operation_groups:
                    operation_groups[metric.operation] = []
                operation_groups[metric.operation].append(metric)
            
            # 计算每个操作的统计信息
            summary = {}
            for operation, metrics in operation_groups.items():
                durations = [m.duration_ms for m in metrics]
                success_count = sum(1 for m in metrics if m.success)
                
                summary[operation] = {
                    "count": len(metrics),
                    "success_rate": success_count / len(metrics),
                    "avg_duration_ms": sum(durations) / len(durations),
                    "min_duration_ms": min(durations),
                    "max_duration_ms": max(durations),
                    "p95_duration_ms": self._percentile(durations, 95),
                    "error_count": len(metrics) - success_count
                }
            
            return summary
    
    def get_recent_errors(self, limit: int = 10, time_window: int = 3600) -> List[Dict[str, Any]]:
        """
        获取最近的错误记录
        
        Args:
            limit: 返回记录数限制
            time_window: 时间窗口（秒）
            
        Returns:
            错误记录列表
        """
        with self.lock:
            cutoff_time = time.time() - time_window
            error_metrics = [
                m for m in self.metrics_history 
                if m.timestamp >= cutoff_time and not m.success
            ]
            
            # 按时间倒序排列
            error_metrics.sort(key=lambda x: x.timestamp, reverse=True)
            
            return [
                {
                    "timestamp": m.timestamp,
                    "session_id": m.session_id,
                    "operation": m.operation,
                    "duration_ms": m.duration_ms,
                    "error_message": m.error_message
                }
                for m in error_metrics[:limit]
            ]
    
    def get_session_metrics(self, session_id: str) -> Dict[str, Any]:
        """
        获取特定会话的性能指标
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话性能指标
        """
        with self.lock:
            session_metrics = [
                m for m in self.metrics_history 
                if m.session_id == session_id
            ]
            
            if not session_metrics:
                return {}
            
            durations = [m.duration_ms for m in session_metrics]
            success_count = sum(1 for m in session_metrics if m.success)
            
            # 按操作分组
            operations = {}
            for metric in session_metrics:
                if metric.operation not in operations:
                    operations[metric.operation] = 0
                operations[metric.operation] += 1
            
            return {
                "session_id": session_id,
                "total_operations": len(session_metrics),
                "success_rate": success_count / len(session_metrics),
                "avg_duration_ms": sum(durations) / len(durations),
                "total_duration_ms": sum(durations),
                "operations": operations,
                "start_time": min(m.timestamp for m in session_metrics),
                "end_time": max(m.timestamp for m in session_metrics)
            }
    
    def clear_history(self):
        """清空历史记录"""
        with self.lock:
            self.metrics_history.clear()
            self.operation_counters.clear()
            self.error_counters.clear()
            logger.info("Performance history cleared")
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """
        计算百分位数
        
        Args:
            data: 数据列表
            percentile: 百分位数（0-100）
            
        Returns:
            百分位数值
        """
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _get_memory_usage(self) -> float:
        """获取当前进程内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # MB
        except Exception:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取当前CPU使用率"""
        try:
            return psutil.cpu_percent()
        except Exception:
            return 0.0


class PerformanceContext:
    """性能监控上下文管理器"""
    
    def __init__(self, monitor: PerformanceMonitor, session_id: str, operation: str):
        """
        初始化性能监控上下文
        
        Args:
            monitor: 性能监控器
            session_id: 会话ID
            operation: 操作名称
        """
        self.monitor = monitor
        self.session_id = session_id
        self.operation = operation
        self.start_time = None
        self.success = True
        self.error_message = ""
    
    def __enter__(self):
        """进入上下文"""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.start_time is not None:
            duration_ms = (time.time() - self.start_time) * 1000
            
            if exc_type is not None:
                self.success = False
                self.error_message = str(exc_val) if exc_val else str(exc_type)
            
            self.monitor.record_operation(
                self.session_id,
                self.operation,
                duration_ms,
                self.success,
                self.error_message
            )
    
    def mark_error(self, error_message: str):
        """标记错误"""
        self.success = False
        self.error_message = error_message


# 全局性能监控器实例
global_performance_monitor = PerformanceMonitor()


def monitor_performance(session_id: str, operation: str):
    """
    性能监控装饰器
    
    Args:
        session_id: 会话ID
        operation: 操作名称
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceContext(global_performance_monitor, session_id, operation):
                return func(*args, **kwargs)
        return wrapper
    return decorator
