"""
监控数据API接口
提供性能监控、健康检查、系统状态等API
"""

import time
import psutil
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import HTMLResponse
import json

from ...utils.monitoring.performance_monitor import global_performance_monitor
from ...utils.monitoring.system_monitor import SystemMonitor
from ...utils.monitoring.alert_manager import global_alert_manager
from ...utils.exceptions import ErrorCodes, create_error_response

router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """健康检查接口"""
    try:
        # 检查系统资源
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 检查性能监控器状态
        recent_stats = global_performance_monitor.get_statistics(time_window=300)  # 5分钟
        
        health_status = "healthy"
        issues = []
        
        # 检查CPU使用率
        if cpu_percent > 90:
            health_status = "warning"
            issues.append(f"High CPU usage: {cpu_percent:.1f}%")
        
        # 检查内存使用率
        if memory.percent > 90:
            health_status = "critical"
            issues.append(f"High memory usage: {memory.percent:.1f}%")
        
        # 检查磁盘使用率
        if disk.percent > 90:
            health_status = "warning"
            issues.append(f"High disk usage: {disk.percent:.1f}%")
        
        # 检查错误率
        if recent_stats.get('total_operations', 0) > 0:
            error_rate = 1 - recent_stats.get('success_rate', 1.0)
            if error_rate > 0.1:  # 错误率超过10%
                health_status = "warning"
                issues.append(f"High error rate: {error_rate:.1%}")
        
        return {
            "status": health_status,
            "timestamp": time.time(),
            "issues": issues,
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent
            },
            "performance": recent_stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/performance/stats")
async def get_performance_stats(
    operation: Optional[str] = Query(None, description="操作类型过滤"),
    time_window: int = Query(3600, description="时间窗口（秒）", ge=60, le=86400)
) -> Dict[str, Any]:
    """获取性能统计信息"""
    try:
        stats = global_performance_monitor.get_statistics(operation, time_window)
        return {
            "operation": operation,
            "time_window": time_window,
            "stats": stats,
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/performance/operations")
async def get_operations_summary(
    time_window: int = Query(3600, description="时间窗口（秒）", ge=60, le=86400)
) -> Dict[str, Any]:
    """获取操作汇总统计"""
    try:
        summary = global_performance_monitor.get_operation_summary(time_window)
        return {
            "time_window": time_window,
            "operations": summary,
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/performance/errors")
async def get_recent_errors(
    limit: int = Query(50, description="返回记录数", ge=1, le=1000),
    time_window: int = Query(3600, description="时间窗口（秒）", ge=60, le=86400)
) -> Dict[str, Any]:
    """获取最近的错误记录"""
    try:
        errors = global_performance_monitor.get_recent_errors(limit, time_window)
        return {
            "limit": limit,
            "time_window": time_window,
            "errors": errors,
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/performance/session/{session_id}")
async def get_session_metrics(session_id: str) -> Dict[str, Any]:
    """获取特定会话的性能指标"""
    try:
        metrics = global_performance_monitor.get_session_metrics(session_id)
        if not metrics:
            raise HTTPException(
                status_code=404,
                detail=create_error_response(ErrorCodes.SESSION_NOT_FOUND)
            )
        
        return {
            "session_metrics": metrics,
            "timestamp": time.time()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/system/status")
async def get_system_status() -> Dict[str, Any]:
    """获取系统状态信息"""
    try:
        # CPU信息
        cpu_info = {
            "percent": psutil.cpu_percent(interval=1),
            "count": psutil.cpu_count(),
            "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
        }
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "used": memory.used
        }
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_info = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100
        }
        
        # 网络信息
        network = psutil.net_io_counters()
        network_info = {
            "bytes_sent": network.bytes_sent,
            "bytes_recv": network.bytes_recv,
            "packets_sent": network.packets_sent,
            "packets_recv": network.packets_recv
        }
        
        return {
            "cpu": cpu_info,
            "memory": memory_info,
            "disk": disk_info,
            "network": network_info,
            "timestamp": time.time()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/alerts/active")
async def get_active_alerts() -> Dict[str, Any]:
    """获取活跃告警"""
    try:
        alerts = global_alert_manager.get_active_alerts()
        return {
            "alerts": [
                {
                    "rule_name": alert.rule_name,
                    "level": alert.level.value,
                    "message": alert.message,
                    "metric_value": alert.metric_value,
                    "threshold": alert.threshold,
                    "timestamp": alert.timestamp,
                    "count": alert.count
                }
                for alert in alerts
            ],
            "count": len(alerts),
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/alerts/history")
async def get_alert_history(
    limit: int = Query(100, description="返回记录数", ge=1, le=1000)
) -> Dict[str, Any]:
    """获取告警历史"""
    try:
        alerts = global_alert_manager.get_alert_history(limit)
        return {
            "alerts": [
                {
                    "rule_name": alert.rule_name,
                    "level": alert.level.value,
                    "message": alert.message,
                    "metric_value": alert.metric_value,
                    "threshold": alert.threshold,
                    "timestamp": alert.timestamp,
                    "status": alert.status.value,
                    "resolved_at": alert.resolved_at,
                    "count": alert.count
                }
                for alert in alerts
            ],
            "count": len(alerts),
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/alerts/stats")
async def get_alert_stats() -> Dict[str, Any]:
    """获取告警统计信息"""
    try:
        stats = global_alert_manager.get_alert_stats()
        return {
            "stats": stats,
            "timestamp": time.time()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=create_error_response(ErrorCodes.INTERNAL_SERVER_ERROR, str(e))
        )


@router.get("/dashboard", response_class=HTMLResponse)
async def monitoring_dashboard():
    """监控仪表板页面"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Enhanced Stream ASR - 监控仪表板</title>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                text-align: center;
            }
            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }
            .metric-card {
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .metric-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 15px;
                color: #333;
            }
            .metric-value {
                font-size: 24px;
                font-weight: bold;
                color: #667eea;
            }
            .status-healthy { color: #28a745; }
            .status-warning { color: #ffc107; }
            .status-critical { color: #dc3545; }
            .chart-container {
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
            .refresh-btn {
                background: #667eea;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
            }
            .refresh-btn:hover {
                background: #5a6fd8;
            }
            .error-list {
                max-height: 300px;
                overflow-y: auto;
            }
            .error-item {
                padding: 10px;
                border-left: 4px solid #dc3545;
                background: #f8f9fa;
                margin-bottom: 10px;
                border-radius: 0 5px 5px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Enhanced Stream ASR 监控仪表板</h1>
                <p>实时系统性能和健康状态监控</p>
                <button class="refresh-btn" onclick="refreshData()">刷新数据</button>
            </div>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-title">系统健康状态</div>
                    <div id="health-status" class="metric-value">检查中...</div>
                    <div id="health-issues"></div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">CPU 使用率</div>
                    <div id="cpu-usage" class="metric-value">--%</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">内存使用率</div>
                    <div id="memory-usage" class="metric-value">--%</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">总操作数 (1小时)</div>
                    <div id="total-operations" class="metric-value">--</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">成功率 (1小时)</div>
                    <div id="success-rate" class="metric-value">--%</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-title">平均响应时间</div>
                    <div id="avg-duration" class="metric-value">-- ms</div>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="metric-title">操作性能统计</div>
                <canvas id="operationsChart" width="400" height="200"></canvas>
            </div>
            
            <div class="chart-container">
                <div class="metric-title">最近错误记录</div>
                <div id="recent-errors" class="error-list">加载中...</div>
            </div>
        </div>
        
        <script>
            let operationsChart;
            
            async function fetchData(url) {
                try {
                    const response = await fetch(url);
                    return await response.json();
                } catch (error) {
                    console.error('获取数据失败:', error);
                    return null;
                }
            }
            
            async function updateHealthStatus() {
                const data = await fetchData('/api/monitoring/health');
                if (data) {
                    const statusElement = document.getElementById('health-status');
                    statusElement.textContent = data.status;
                    statusElement.className = `metric-value status-${data.status}`;
                    
                    const issuesElement = document.getElementById('health-issues');
                    if (data.issues && data.issues.length > 0) {
                        issuesElement.innerHTML = data.issues.map(issue => 
                            `<div style="color: #dc3545; font-size: 14px; margin-top: 5px;">${issue}</div>`
                        ).join('');
                    } else {
                        issuesElement.innerHTML = '<div style="color: #28a745; font-size: 14px; margin-top: 5px;">无问题</div>';
                    }
                    
                    if (data.system) {
                        document.getElementById('cpu-usage').textContent = `${data.system.cpu_percent.toFixed(1)}%`;
                        document.getElementById('memory-usage').textContent = `${data.system.memory_percent.toFixed(1)}%`;
                    }
                }
            }
            
            async function updatePerformanceStats() {
                const data = await fetchData('/api/monitoring/performance/stats?time_window=3600');
                if (data && data.stats) {
                    document.getElementById('total-operations').textContent = data.stats.total_operations || 0;
                    document.getElementById('success-rate').textContent = 
                        data.stats.success_rate ? `${(data.stats.success_rate * 100).toFixed(1)}%` : '100%';
                    document.getElementById('avg-duration').textContent = 
                        data.stats.avg_duration_ms ? `${data.stats.avg_duration_ms.toFixed(1)} ms` : '-- ms';
                }
            }
            
            async function updateOperationsChart() {
                const data = await fetchData('/api/monitoring/performance/operations?time_window=3600');
                if (data && data.operations) {
                    const operations = Object.keys(data.operations);
                    const counts = operations.map(op => data.operations[op].count);
                    const avgDurations = operations.map(op => data.operations[op].avg_duration_ms);
                    
                    if (operationsChart) {
                        operationsChart.destroy();
                    }
                    
                    const ctx = document.getElementById('operationsChart').getContext('2d');
                    operationsChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: operations,
                            datasets: [{
                                label: '操作次数',
                                data: counts,
                                backgroundColor: 'rgba(102, 126, 234, 0.6)',
                                borderColor: 'rgba(102, 126, 234, 1)',
                                borderWidth: 1,
                                yAxisID: 'y'
                            }, {
                                label: '平均耗时 (ms)',
                                data: avgDurations,
                                type: 'line',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                yAxisID: 'y1'
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    type: 'linear',
                                    display: true,
                                    position: 'left',
                                },
                                y1: {
                                    type: 'linear',
                                    display: true,
                                    position: 'right',
                                    grid: {
                                        drawOnChartArea: false,
                                    },
                                }
                            }
                        }
                    });
                }
            }
            
            async function updateRecentErrors() {
                const data = await fetchData('/api/monitoring/performance/errors?limit=10&time_window=3600');
                if (data && data.errors) {
                    const errorsElement = document.getElementById('recent-errors');
                    if (data.errors.length === 0) {
                        errorsElement.innerHTML = '<div style="text-align: center; color: #28a745;">最近1小时内无错误记录</div>';
                    } else {
                        errorsElement.innerHTML = data.errors.map(error => `
                            <div class="error-item">
                                <strong>${error.operation}</strong> - ${error.session_id}<br>
                                <small>${new Date(error.timestamp * 1000).toLocaleString()}</small><br>
                                <span style="color: #dc3545;">${error.error_message}</span>
                            </div>
                        `).join('');
                    }
                }
            }
            
            async function refreshData() {
                await Promise.all([
                    updateHealthStatus(),
                    updatePerformanceStats(),
                    updateOperationsChart(),
                    updateRecentErrors()
                ]);
            }
            
            // 初始加载
            refreshData();
            
            // 每30秒自动刷新
            setInterval(refreshData, 30000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)
