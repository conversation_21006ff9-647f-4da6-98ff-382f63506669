/**
 * 主应用程序
 * 协调WebSocket客户端、音频录制器和UI控制器
 */

class ASRApp {
    constructor() {
        this.wsClient = new WebSocketClient();
        this.audioRecorder = new AudioRecorder();
        this.uiController = new UIController();
        this.isInitialized = false;
        
        this.bindEvents();
        this.setupEventHandlers();
    }
    
    bindEvents() {
        // 绑定UI按钮事件
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startRecording();
        });
        
        document.getElementById('stopBtn').addEventListener('click', () => {
            this.stopRecording();
        });
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }
    
    setupEventHandlers() {
        // WebSocket事件处理
        this.wsClient.onConnectionStateChange = (state) => {
            this.uiController.updateConnectionStatus(state);
            this.uiController.setButtonsEnabled(state === 'connected');
        };
        
        this.wsClient.onRecognitionResult = (result) => {
            this.uiController.addRecognitionResult(result);
            
            // 更新检测到的语言
            if (result.language) {
                this.uiController.updateDetectedLanguage(result.language);
            }
        };
        
        this.wsClient.onError = (error) => {
            this.uiController.addLog('error', `WebSocket错误: ${error.message}`);
            console.error('WebSocket error:', error);
        };
        
        this.wsClient.onSessionInfo = (info) => {
            this.uiController.updateSessionInfo(info.session_id, info);
        };
        
        this.wsClient.onStatusUpdate = (status) => {
            this.uiController.addLog('info', `状态更新: ${status.message}`);
        };
        
        this.wsClient.onLog = (level, message, timestamp) => {
            this.uiController.addLog(level, message, timestamp);
        };
        
        // 音频录制器事件处理
        this.audioRecorder.onAudioData = (audioData, isFinal) => {
            if (this.wsClient.getConnectionState() === 'connected') {
                try {
                    this.wsClient.sendAudioData(audioData, isFinal);
                } catch (error) {
                    this.uiController.addLog('error', `发送音频数据失败: ${error.message}`);
                }
            }
        };
        
        this.audioRecorder.onVolumeChange = (volume) => {
            this.uiController.updateVolumeLevel(volume);
        };
        
        this.audioRecorder.onRecordingStateChange = (isRecording) => {
            this.uiController.updateRecordingStatus(isRecording);
            
            // 更新音频时长
            if (isRecording) {
                this.startAudioTimeUpdate();
            } else {
                this.stopAudioTimeUpdate();
            }
        };
        
        this.audioRecorder.onError = (error) => {
            this.uiController.addLog('error', `音频录制错误: ${error.message}`);
            console.error('Audio recorder error:', error);
        };
    }
    
    async startRecording() {
        try {
            this.uiController.addLog('info', '开始录音...');
            
            // 初始化（如果还没有初始化）
            if (!this.isInitialized) {
                await this.initialize();
            }
            
            // 连接WebSocket（如果还没有连接）
            if (this.wsClient.getConnectionState() === 'disconnected') {
                const config = this.uiController.getConfig();
                await this.wsClient.connect(config);
                this.uiController.updateSessionInfo(this.wsClient.getSessionId());
            }
            
            // 开始录音
            this.audioRecorder.startRecording();
            
            // 通知服务器开始录音
            this.wsClient.startRecording();
            
            this.uiController.addLog('info', '录音已开始');
            
        } catch (error) {
            this.uiController.addLog('error', `开始录音失败: ${error.message}`);
            console.error('Failed to start recording:', error);
            
            // 显示用户友好的错误消息
            this.showErrorMessage('开始录音失败', error.message);
        }
    }
    
    async stopRecording() {
        try {
            this.uiController.addLog('info', '停止录音...');
            
            // 停止录音
            this.audioRecorder.stopRecording();
            
            // 通知服务器停止录音
            if (this.wsClient.getConnectionState() === 'connected') {
                this.wsClient.stopRecording();
            }
            
            this.uiController.addLog('info', '录音已停止');
            
        } catch (error) {
            this.uiController.addLog('error', `停止录音失败: ${error.message}`);
            console.error('Failed to stop recording:', error);
        }
    }
    
    async initialize() {
        try {
            this.uiController.addLog('info', '初始化音频录制器...');
            
            const config = this.uiController.getConfig();
            await this.audioRecorder.initialize({
                sampleRate: config.sampleRate,
                channels: 1,
                chunkDuration: 0.4
            });
            
            this.isInitialized = true;
            this.uiController.addLog('info', '音频录制器初始化成功');
            
        } catch (error) {
            this.uiController.addLog('error', `初始化失败: ${error.message}`);
            throw error;
        }
    }
    
    startAudioTimeUpdate() {
        this.stopAudioTimeUpdate();
        
        this.audioTimeInterval = setInterval(() => {
            const duration = this.audioRecorder.getTotalAudioTime();
            this.uiController.updateAudioDuration(duration);
            
            // 更新会话统计
            this.uiController.updateSessionInfo(null, {
                totalAudioTime: duration
            });
        }, 100);
    }
    
    stopAudioTimeUpdate() {
        if (this.audioTimeInterval) {
            clearInterval(this.audioTimeInterval);
            this.audioTimeInterval = null;
        }
    }
    
    showErrorMessage(title, message) {
        // 简单的错误提示（可以替换为更好的UI组件）
        alert(`${title}\n\n${message}`);
    }
    
    cleanup() {
        try {
            this.stopAudioTimeUpdate();
            
            if (this.audioRecorder) {
                this.audioRecorder.destroy();
            }
            
            if (this.wsClient) {
                this.wsClient.disconnect();
            }
            
            console.log('App cleanup completed');
            
        } catch (error) {
            console.error('Cleanup error:', error);
        }
    }
    
    // 公共方法
    getConnectionState() {
        return this.wsClient.getConnectionState();
    }
    
    getSessionId() {
        return this.wsClient.getSessionId();
    }
    
    isRecording() {
        return this.audioRecorder.getIsRecording();
    }
    
    // 手动连接/断开连接
    async connect() {
        if (this.wsClient.getConnectionState() === 'disconnected') {
            const config = this.uiController.getConfig();
            await this.wsClient.connect(config);
        }
    }
    
    disconnect() {
        if (this.wsClient.getConnectionState() === 'connected') {
            this.wsClient.disconnect();
        }
    }
}

// 全局应用实例
let asrApp;

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        asrApp = new ASRApp();
        console.log('ASR App initialized successfully');
        
        // 添加初始日志
        asrApp.uiController.addLog('info', '应用程序已启动');
        asrApp.uiController.addLog('info', '点击"开始录音"开始语音识别');
        
    } catch (error) {
        console.error('Failed to initialize ASR App:', error);
        alert('应用程序初始化失败: ' + error.message);
    }
});

// 导出全局访问
window.asrApp = asrApp;
