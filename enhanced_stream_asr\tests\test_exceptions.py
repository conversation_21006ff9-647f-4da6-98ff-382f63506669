"""
异常处理模块测试
"""

import pytest
from utils.exceptions import (
    ASRError, HandshakeError, AudioProcessingError, SessionError,
    ErrorCodes, ERROR_MESSAGES, create_error_response, handle_exception, ErrorHandler
)


class TestASRError:
    """测试ASR异常类"""
    
    def test_asr_error_creation(self):
        """测试ASR异常创建"""
        error = ASRError(1001, "Test error", {"key": "value"})
        
        assert error.code == 1001
        assert error.message == "Test error"
        assert error.details == {"key": "value"}
        assert str(error) == "[1001] Test error"
    
    def test_asr_error_inheritance(self):
        """测试异常继承"""
        handshake_error = HandshakeError(1001, "Handshake failed")
        audio_error = AudioProcessingError(2001, "Audio processing failed")
        session_error = SessionError(3001, "Session error")
        
        assert isinstance(handshake_error, ASRError)
        assert isinstance(audio_error, ASRError)
        assert isinstance(session_error, ASRError)


class TestErrorCodes:
    """测试错误码"""
    
    def test_error_codes_defined(self):
        """测试错误码定义"""
        assert ErrorCodes.INVALID_HANDSHAKE_FORMAT == 1001
        assert ErrorCodes.SESSION_NOT_FOUND == 3001
        assert ErrorCodes.INTERNAL_SERVER_ERROR == 4001
    
    def test_error_messages_mapping(self):
        """测试错误消息映射"""
        assert ErrorCodes.INVALID_HANDSHAKE_FORMAT in ERROR_MESSAGES
        assert ErrorCodes.SESSION_NOT_FOUND in ERROR_MESSAGES
        assert ErrorCodes.INTERNAL_SERVER_ERROR in ERROR_MESSAGES


class TestErrorResponse:
    """测试错误响应"""
    
    def test_create_error_response(self):
        """测试创建错误响应"""
        response = create_error_response(1001, "Test error", {"detail": "test"})
        
        assert response["error"] == "Test error"
        assert response["error_code"] == 1001
        assert response["details"] == {"detail": "test"}
        assert response["success"] is False
    
    def test_create_error_response_with_default_message(self):
        """测试使用默认消息创建错误响应"""
        response = create_error_response(ErrorCodes.SESSION_NOT_FOUND)
        
        assert response["error"] == ERROR_MESSAGES[ErrorCodes.SESSION_NOT_FOUND]
        assert response["error_code"] == ErrorCodes.SESSION_NOT_FOUND
    
    def test_handle_exception_with_asr_error(self):
        """测试处理ASR异常"""
        error = ASRError(1001, "Test error", {"key": "value"})
        response = handle_exception(error)
        
        assert response["error"] == "Test error"
        assert response["error_code"] == 1001
        assert response["details"] == {"key": "value"}
    
    def test_handle_exception_with_generic_error(self):
        """测试处理通用异常"""
        error = ValueError("Generic error")
        response = handle_exception(error)
        
        assert response["error"] == "Generic error"
        assert response["error_code"] == ErrorCodes.INTERNAL_SERVER_ERROR
        assert response["details"]["exception_type"] == "ValueError"


class TestErrorHandler:
    """测试错误处理器"""
    
    def test_log_and_return_error(self):
        """测试记录日志并返回错误"""
        import logging
        from unittest.mock import Mock
        
        logger = Mock(spec=logging.Logger)
        
        response = ErrorHandler.log_and_return_error(
            logger, ErrorCodes.SESSION_NOT_FOUND, 
            details={"session_id": "test_session"}
        )
        
        # 验证日志调用
        logger.error.assert_called_once()
        
        # 验证响应
        assert response["error_code"] == ErrorCodes.SESSION_NOT_FOUND
        assert response["details"]["session_id"] == "test_session"
    
    def test_handle_and_log_exception(self):
        """测试处理和记录异常"""
        import logging
        from unittest.mock import Mock
        
        logger = Mock(spec=logging.Logger)
        error = ASRError(1001, "Test error")
        
        response = ErrorHandler.handle_and_log_exception(
            logger, error, context="Test context"
        )
        
        # 验证日志调用
        logger.error.assert_called_once()
        
        # 验证响应
        assert response["error_code"] == 1001
        assert response["error"] == "Test error"


if __name__ == "__main__":
    pytest.main([__file__])
