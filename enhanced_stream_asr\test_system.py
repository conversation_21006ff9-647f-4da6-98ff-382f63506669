#!/usr/bin/env python3
"""
Enhanced Stream ASR 系统测试脚本
测试各个模块的基本功能
"""

import sys
import asyncio
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import ConfigManager
from utils.logger import setup_logger
from core.audio import VADProcessor, FeatureExtractor, AudioUtils
from core.lid import LIDEngine, ProgressiveLID
from core.asr import TextProcessor
from core.session import SessionManager


def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    
    try:
        config_manager = ConfigManager("configs")
        
        # 测试基本配置获取
        supported_languages = config_manager.get_supported_languages()
        print(f"   支持的语种: {supported_languages}")
        
        # 测试语种配置
        for lang in supported_languages[:2]:  # 只测试前两个
            lang_config = config_manager.get_language_config(lang)
            separator = config_manager.get_separator(lang)
            print(f"   {lang} 分隔符: '{separator}'")
        
        print("   ✅ 配置管理器测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 配置管理器测试失败: {e}")
        return False


def test_audio_processing():
    """测试音频处理模块"""
    print("🎵 测试音频处理模块...")
    
    try:
        # 测试VAD处理器
        vad_config = {
            'vad_type': 'webrtcvad',
            'vad_level': 1,
            'sample_rate': 16000
        }
        vad_processor = VADProcessor(vad_config)
        
        # 生成测试音频数据
        test_audio = np.random.randint(-1000, 1000, 6400, dtype=np.int16)  # 0.4秒@16kHz
        
        # 测试VAD检测
        is_speech = vad_processor.is_speech_chunk(test_audio)
        print(f"   VAD检测结果: {is_speech}")
        
        # 测试特征提取器
        feature_config = {
            'feat_type': 'fbank',
            'num_mel_bins': 80,
            'sample_rate': 16000
        }
        feature_extractor = FeatureExtractor(feature_config)
        
        # 转换为浮点数
        audio_float = test_audio.astype(np.float32) / 32768.0
        features = feature_extractor.extract_features(audio_float)
        print(f"   特征形状: {features.shape}")
        
        # 测试音频工具
        pcm_data = AudioUtils.convert_to_float32(test_audio)
        print(f"   音频转换: {type(pcm_data)} -> {pcm_data.dtype}")
        
        print("   ✅ 音频处理模块测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 音频处理模块测试失败: {e}")
        return False


def test_text_processor():
    """测试文本处理器"""
    print("📝 测试文本处理器...")
    
    try:
        config_manager = ConfigManager("configs")
        text_processor = TextProcessor(config_manager)
        
        # 测试中文文本处理
        zh_text = text_processor.process_recognition_result(
            text="你好世界",
            language="zh",
            timestamp=1.0,
            is_final=True
        )
        print(f"   中文处理: '{zh_text}'")
        
        # 测试英文文本处理
        en_text = text_processor.process_recognition_result(
            text="hello world",
            language="en", 
            timestamp=2.0,
            is_final=True
        )
        print(f"   英文处理: '{en_text}'")
        
        # 测试分隔符添加
        zh_text2 = text_processor.process_recognition_result(
            text="第二句话",
            language="zh",
            timestamp=3.0,  # 时间间隔大于阈值
            is_final=True
        )
        print(f"   分隔符测试: '{zh_text2}'")
        
        print("   ✅ 文本处理器测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 文本处理器测试失败: {e}")
        return False


def test_lid_engine():
    """测试语种识别引擎"""
    print("🌍 测试语种识别引擎...")
    
    try:
        config_manager = ConfigManager("configs")
        lid_config = config_manager.get_lid_config()
        
        # 检查LID模型是否存在
        model_path = lid_config.get('model', {}).get('model_path', '')
        if not model_path or not Path(model_path).exists():
            print("   ⚠️  LID模型文件不存在，跳过测试")
            return True
            
        # 创建LID引擎
        lid_engine = LIDEngine(model_path, lid_config)
        
        if lid_engine.load_model():
            print("   ✅ LID模型加载成功")
            
            # 生成测试音频
            test_audio = np.random.randn(16000).astype(np.float32)  # 1秒音频
            
            # 测试语种检测
            result = lid_engine.infer({'audio': test_audio})
            if result['success']:
                print(f"   检测结果: {result['language']} (置信度: {result['confidence']:.3f})")
            else:
                print(f"   检测失败: {result.get('error', 'Unknown error')}")
                
            lid_engine.unload_model()
        else:
            print("   ⚠️  LID模型加载失败")
            
        print("   ✅ 语种识别引擎测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 语种识别引擎测试失败: {e}")
        return False


async def test_session_manager():
    """测试会话管理器"""
    print("🔗 测试会话管理器...")
    
    try:
        config_manager = ConfigManager("configs")
        session_manager = SessionManager(config_manager)
        
        print(f"   会话管理器初始化成功")
        print(f"   支持的语种: {config_manager.get_supported_languages()}")
        
        # 获取统计信息
        stats = session_manager.get_statistics()
        print(f"   当前会话数: {stats['total_sessions']}")
        
        # 清理
        await session_manager.shutdown()
        
        print("   ✅ 会话管理器测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 会话管理器测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 Enhanced Stream ASR 系统测试")
    print("=" * 50)
    
    # 设置日志
    logger = setup_logger(level="WARNING")  # 减少日志输出
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_config_manager())
    test_results.append(test_audio_processing())
    test_results.append(test_text_processor())
    test_results.append(test_lid_engine())
    test_results.append(await test_session_manager())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统基本功能正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关模块")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试运行失败: {e}")
        sys.exit(1)
