import json
import logging
import os
import time
from typing import Any, Dict, List, Optional

import numpy as np

# Triton client
try:
    import tritonclient.grpc as grpcclient
    TRITON_AVAILABLE = True
except ImportError:
    print("Warning: tritonclient not available")
    TRITON_AVAILABLE = False


LOGDIR = os.getenv("LOGDIR", "/ws/log")

def set_logger_dir(logger_dir:str):
    global LOGDIR
    os.makedirs(logger_dir, exist_ok=True)
    LOGDIR = logger_dir

def create_logger(model_name, pid=None, tid=None):
    # logging
    now_time = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
    log_name = f"log.{model_name}.{now_time}.txt"   # 多个实例需要不同的log_file， 否则会覆盖
    log_file = f'{LOGDIR}/{log_name}'
    hd = logging.FileHandler(filename=log_file, mode='w', encoding='utf-8')
    logging.basicConfig(format='%(levelname)s - %(asctime)s - %(name)s - %(message)s',
        datefmt='%y/%m/%d %H:%M:%S',
        handlers=[hd],
        level=logging.INFO)
    logger = logging.getLogger(model_name)
    return logger

class TritonGRPCClient:
    """Triton客户端封装"""
    
    def __init__(self, server_url: str, model_name: str, logger):
        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.logger = logger
        if logger is None:
            from config_loader import setup_logging
            setup_logging()
            self.logger = logging.getLogger(__name__)
        
    def connect(self):
        """连接到Triton服务器"""
        if not TRITON_AVAILABLE:
            raise RuntimeError("Triton client not available")
            
        try:
            self.client = grpcclient.InferenceServerClient(url=self.server_url)
            if not self.client.is_model_ready(self.model_name):
                raise RuntimeError(f"Model {self.model_name} is not ready")
            self.logger.info(f"Connected to Triton server at {self.server_url}")
        except Exception as e:
            self.logger.error(f"Failed to connect to Triton server: {e}")
            raise
    
    async def transcribe(self, audio_data: np.ndarray, language: str = "zh") -> Dict:
        """执行语音识别"""
        if self.client is None:
            self.connect()
        
        try:
            # Prepare inputs
            audio_data = np.array([audio_data], dtype=np.float32)
            lang_array = np.array([language.encode("utf-8")], dtype=object)
            lang_array = np.expand_dims(lang_array, axis=0)
            
            inputs = [
                grpcclient.InferInput("WAV", audio_data.shape, "FP32"),
                grpcclient.InferInput("LANG", lang_array.shape, "BYTES")
            ]
            
            inputs[0].set_data_from_numpy(audio_data)
            inputs[1].set_data_from_numpy(lang_array)
            
            outputs = [grpcclient.InferRequestedOutput("TRANSCRIPTS")]
            
            # Send request
            response = self.client.infer(self.model_name, inputs, outputs=outputs)
            
            # Parse result
            result_array = response.as_numpy("TRANSCRIPTS")
            if len(result_array) > 0:
                result_json = result_array[0].decode('utf-8')
                return json.loads(result_json)
            else:
                return {"text": "", "segments": [], "duration": 0}
                
        except Exception as e:
            self.logger.error(f"Transcription failed: {e}")
            raise