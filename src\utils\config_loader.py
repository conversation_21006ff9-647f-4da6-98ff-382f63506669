#!/usr/bin/env python3
"""
配置加载模块
统一加载和管理所有服务的配置
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_file: str = "/ws/src/conf/config.yaml"):
        self.config_file = config_file
        self.config_data = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = yaml.safe_load(f) or {}
                logger.info(f"Loaded configuration from {self.config_file}")
            else:
                logger.warning(f"Configuration file {self.config_file} not found, using defaults")
                self.config_data = {}
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            self.config_data = {}
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持嵌套键如 'triton.server_url'"""
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取配置段"""
        return self.config_data.get(section, {})

# 全局配置加载器
config_loader = ConfigLoader()

class TritonConfig:
    """Triton服务器配置"""
    
    def __init__(self):
        self.MODEL_NAME = config_loader.get("asr.model_name", "unified_asr")
        self.HOST = config_loader.get("asr.host", "localhost")
        self.HTTP_PORT = config_loader.get("asr.http_port", 10090)
        self.GRPC_PORT = config_loader.get("asr.grpc_port", 10091)
        self.METRICS_PORT = config_loader.get("asr.metrics_port", 10092)
        
        self.HTTP_URL = f"{self.HOST}:{self.HTTP_PORT}"
        self.GRPC_URL = f"{self.HOST}:{self.GRPC_PORT}"
        
        # advance parameters
        self.MODEL_REPOSITORY = config_loader.get("triton.model_repository", "./model")
        self.LOG_VERBOSE = config_loader.get("triton.log_verbose", 1)
        self.MAX_BATCH_SIZE = config_loader.get("triton.max_batch_size", 128)

class HTTPServiceConfig:
    """HTTP服务配置"""
    
    def __init__(self):
        self.HOST = config_loader.get("http_service.host", "0.0.0.0")
        self.PORT = config_loader.get("http_service.port", 10093)
        self.MAX_FILE_SIZE = config_loader.get("http_service.max_file_size", 100 * 1024 * 1024)  # 100MB
        self.UPLOAD_DIR = config_loader.get("http_service.upload_dir", "wavs/uploads")
        self.SUPPORTED_FORMATS = config_loader.get("http_service.supported_formats", 
                                                  [".wav", ".mp3", ".flac", ".m4a", ".aac"])
        self.CORS_ORIGINS = config_loader.get("http_service.cors_origins", ["*"])

class StreamServiceConfig:
    """WebSocket流式服务配置"""
    
    def __init__(self):
        self.HOST = config_loader.get("stream_service.host", "0.0.0.0")
        self.PORT = config_loader.get("stream_service.port", 8081)
        self.SAMPLE_RATE = config_loader.get("stream_service.sample_rate", 16000)
        self.CHUNK_SIZE = config_loader.get("stream_service.chunk_size", 1600)  # 100ms at 16kHz
        self.MAX_AUDIO_BUFFER = config_loader.get("stream_service.max_audio_buffer", 160000)  # 10 seconds
        self.VAD_THRESHOLD = config_loader.get("stream_service.vad_threshold", 0.5)
        self.SILENCE_TIMEOUT = config_loader.get("stream_service.silence_timeout", 2.0)  # seconds
        self.MAX_SENTENCE_SILENCE = config_loader.get("stream_service.max_sentence_silence", 450)  # ms
        self.CLEANUP_INTERVAL = config_loader.get("stream_service.cleanup_interval", 30)  # seconds

class ASRConfig:
    """ASR识别配置"""
    
    def __init__(self):
        self.DEFAULT_LANGUAGE = config_loader.get("asr.default_language", "zh")
        self.SUPPORTED_LANGUAGES = config_loader.get("asr.supported_languages", ["zh", "en", "ja", "ko"])
        
        # VAD配置
        vad_config = config_loader.get_section("asr.vad")
        self.VAD_ENABLE = vad_config.get("enable", True)
        self.VAD_TYPE = vad_config.get("type", "webrtcvad")
        self.VAD_LEVEL = vad_config.get("level", 0)
        self.VAD_FRAME_LENGTH = vad_config.get("frame_length", 30)
        self.VAD_WINDOW_SIZE = vad_config.get("window_size", 10)
        self.VAD_DECISION_THRESHOLD = vad_config.get("decision_threshold", 0.9)
        self.VAD_MAX_SPEECH_LENGTH = vad_config.get("max_speech_length", 30)
        self.VAD_MIN_SPEECH_LENGTH = vad_config.get("min_speech_length", 5)
        self.VAD_MERGE_SILENCE_LENGTH = vad_config.get("merge_silence_length", 2)
        
        # 特征提取配置
        feature_config = config_loader.get_section("asr.feature_extraction")
        self.NUM_MEL_BINS = feature_config.get("num_mel_bins", 80)
        self.FRAME_SHIFT_MS = feature_config.get("frame_shift_ms", 10)
        self.FRAME_LENGTH_MS = feature_config.get("frame_length_ms", 25)
        self.SAMPLE_RATE = feature_config.get("sample_rate", 16000)
        
        # 解码配置
        decoding_config = config_loader.get_section("asr.decoding")
        self.BEAM_SIZE = decoding_config.get("beam_size", 10)
        self.BLANK_ID = decoding_config.get("blank_id", 0)
        self.CUTOFF_PROB = decoding_config.get("cutoff_prob", 0.999)
        self.ENABLE_HOTWORDS = decoding_config.get("enable_hotwords", True)
        self.ENABLE_LANGUAGE_MODEL = decoding_config.get("enable_language_model", False)
        
        # 后处理配置
        post_config = config_loader.get_section("asr.post_processing")
        self.ENABLE_PUNCTUATION_PREDICTION = post_config.get("enable_punctuation_prediction", True)
        self.ENABLE_MODAL_PARTICLE_FILTER = post_config.get("enable_modal_particle_filter", True)
        self.ENABLE_INVERSE_TEXT_NORMALIZATION = post_config.get("enable_inverse_text_normalization", True)
        self.ENABLE_CONFIDENCE = post_config.get("enable_confidence", True)
        self.ENABLE_VOLUME = post_config.get("enable_volume", True)
        self.ENABLE_WORDS = post_config.get("enable_words", False)
        self.ENABLE_INTERMEDIATE_WORDS = post_config.get("enable_intermediate_words", False)
        self.ENABLE_LANG_LABEL = post_config.get("enable_lang_label", False)

class MonitoringConfig:
    """监控配置"""
    
    def __init__(self):
        self.ENABLE = config_loader.get("monitoring.enable", True)
        self.MAX_HISTORY = config_loader.get("monitoring.max_history", 1000)
        self.BATCH_ADJUSTMENT_INTERVAL = config_loader.get("monitoring.batch_adjustment_interval", 100)
        self.MEMORY_THRESHOLD_MB = config_loader.get("monitoring.memory_threshold_mb", 8000)
        self.LATENCY_THRESHOLD_SECONDS = config_loader.get("monitoring.latency_threshold_seconds", 5.0)
        
        # 指标配置
        metrics_config = config_loader.get_section("monitoring.metrics")
        self.ENABLE_PROMETHEUS = metrics_config.get("enable_prometheus", False)
        self.PROMETHEUS_PORT = metrics_config.get("prometheus_port", 9090)
        self.ENABLE_LOGGING = metrics_config.get("enable_logging", True)
        self.LOG_INTERVAL = metrics_config.get("log_interval", 60)

class LoggingConfig:
    """日志配置"""
    
    def __init__(self):
        self.LEVEL = config_loader.get("logging.level", "INFO")
        self.FORMAT = config_loader.get("logging.format", 
                                       "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        self.FILE = config_loader.get("logging.file", "/ws/log/asr_service.log")
        self.MAX_SIZE = config_loader.get("logging.max_size", "10MB")
        self.BACKUP_COUNT = config_loader.get("logging.backup_count", 5)

class SecurityConfig:
    """安全配置"""
    
    def __init__(self):
        self.ENABLE_AUTH = config_loader.get("security.enable_auth", False)
        self.API_KEY_HEADER = config_loader.get("security.api_key_header", "X-API-Key")
        
        # 速率限制
        rate_limit_config = config_loader.get_section("security.rate_limit")
        self.ENABLE_RATE_LIMIT = rate_limit_config.get("enable", False)
        self.REQUESTS_PER_MINUTE = rate_limit_config.get("requests_per_minute", 60)
        self.BURST_SIZE = rate_limit_config.get("burst_size", 10)

# 全局配置实例
triton_config = TritonConfig()
http_config = HTTPServiceConfig()
stream_config = StreamServiceConfig()
asr_config = ASRConfig()
monitoring_config = MonitoringConfig()
logging_config = LoggingConfig()
security_config = SecurityConfig()

def setup_logging():
    """设置日志配置"""
    # 创建日志目录
    log_file = Path(logging_config.FILE)
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 配置日志级别
    level = getattr(logging, logging_config.LEVEL.upper(), logging.INFO)
    
    # TODO：use `logging_config.BACKUP_COUNT`  `logging_config.MAX_SIZE`
    # 基础配置
    logging.basicConfig(
        level=level,
        format=logging_config.FORMAT,
        handlers=[
            logging.StreamHandler(),  # 控制台输出
            logging.FileHandler(logging_config.FILE, encoding='utf-8')  # 文件输出
        ]
    )

def get_config_summary() -> Dict[str, Any]:
    """获取配置摘要"""
    return {
        "triton": {
            "server_url": triton_config.SERVER_URL,
            "model_name": triton_config.MODEL_NAME,
            "model_repository": triton_config.MODEL_REPOSITORY
        },
        "http_service": {
            "host": http_config.HOST,
            "port": http_config.PORT,
            "max_file_size_mb": http_config.MAX_FILE_SIZE // 1024 // 1024
        },
        "stream_service": {
            "host": stream_config.HOST,
            "port": stream_config.PORT,
            "sample_rate": stream_config.SAMPLE_RATE
        },
        "asr": {
            "default_language": asr_config.DEFAULT_LANGUAGE,
            "supported_languages": asr_config.SUPPORTED_LANGUAGES
        },
        "monitoring": {
            "enabled": monitoring_config.ENABLE,
            "max_history": monitoring_config.MAX_HISTORY
        }
    }

if __name__ == "__main__":
    # 测试配置加载
    print("Configuration Summary:")
    import json
    print(json.dumps(get_config_summary(), indent=2, ensure_ascii=False))
