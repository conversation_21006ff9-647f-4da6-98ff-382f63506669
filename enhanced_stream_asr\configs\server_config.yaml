# Enhanced Stream ASR 服务器配置

# 服务器设置
server:
  host: "0.0.0.0"
  port: 8080
  workers: 1
  debug: false

# WebSocket设置
websocket:
  heartbeat_interval: 30        # 心跳间隔（秒）
  max_connections: 100          # 最大连接数
  connection_timeout: 300       # 连接超时（秒）
  max_message_size: 1048576     # 最大消息大小（1MB）

# 音频设置
audio:
  sample_rate: 16000           # 采样率
  channels: 1                  # 声道数
  chunk_duration: 0.4          # 音频块时长（秒）
  max_audio_duration: 60       # 最大音频时长（秒）
  supported_sample_rates: [8000, 16000, 44100, 48000]

# ASR设置
asr:
  supported_languages: ["zh", "en", "ru", "ug", "kk"]
  default_language: "zh"
  enable_auto_language_detection: true
  enable_intermediate_result: true
  enable_punctuation: true
  enable_itn: true
  max_recognition_time: 10     # 最大识别时间（秒）

# 模型设置
models:
  model_dir: "models"
  device: "cpu"                # cpu, gpu, npu
  device_id: 0
  quantized: true              # 是否使用量化模型
  max_batch_size: 8
  
# LID设置
lid:
  enable: true
  model_path: "models/lid/lid_model.onnx"
  confidence_threshold: 0.8
  min_audio_duration: 0.4
  max_audio_duration: 2.4
  progressive_steps: [0.4, 0.8, 1.2, 1.6, 2.0, 2.4]

# VAD设置
vad:
  enable: true
  vad_type: "webrtcvad"
  vad_level: 1
  frame_length: 30             # 帧长（毫秒）
  window_size: 10              # 窗口大小
  seg_threshold: 0.9           # 分段阈值

# 日志设置
logging:
  level: "INFO"                # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/asr_server.log"
  max_size: "10MB"
  backup_count: 5

# 监控设置
monitoring:
  enable: true
  metrics_port: 9090
  max_history: 1000            # 最大历史记录数

# 安全设置
security:
  enable_cors: true
  cors_origins: ["*"]
  max_request_size: 10485760   # 10MB
  rate_limit: 100              # 每分钟请求限制
