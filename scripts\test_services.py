#!/usr/bin/env python3
"""
ASR服务测试脚本
测试HTTP API和WebSocket流式API
"""

import os
import sys
import json
import time
import asyncio
import logging
import tempfile
from pathlib import Path

import requests
import numpy as np
import websockets
from websockets.exceptions import ConnectionClosed

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ASRServiceTester:
    """ASR服务测试器"""
    
    def __init__(self, http_url="http://localhost:8080", ws_url="ws://localhost:8081"):
        self.http_url = http_url
        self.ws_url = ws_url
        
    def create_test_audio(self, duration=3, sample_rate=16000):
        """创建测试音频数据"""
        # Generate a simple sine wave
        t = np.linspace(0, duration, int(duration * sample_rate), False)
        frequency = 440  # A4 note
        audio_data = np.sin(2 * np.pi * frequency * t).astype(np.float32)
        
        # Add some noise
        noise = np.random.normal(0, 0.1, audio_data.shape).astype(np.float32)
        audio_data = audio_data + noise
        
        return audio_data
    
    def save_audio_as_wav(self, audio_data, sample_rate=16000):
        """保存音频为WAV文件"""
        import wave
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
        
        # Convert float32 to int16
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # Write WAV file
        with wave.open(temp_file.name, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        return temp_file.name
    
    def test_health_check(self):
        """测试健康检查"""
        logger.info("Testing health check...")
        
        try:
            # Test HTTP service health
            response = requests.get(f"{self.http_url}/health", timeout=10)
            logger.info(f"HTTP Health: {response.status_code} - {response.json()}")
            
            # Test Stream service health
            ws_http_url = self.ws_url.replace("ws://", "http://").replace("wss://", "https://")
            response = requests.get(f"{ws_http_url}/health", timeout=10)
            logger.info(f"Stream Health: {response.status_code} - {response.json()}")
            
            return True
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    def test_file_upload(self):
        """测试文件上传转写"""
        logger.info("Testing file upload transcription...")
        
        try:
            # Create test audio
            audio_data = self.create_test_audio(duration=2)
            wav_file = self.save_audio_as_wav(audio_data)
            
            try:
                # Upload file
                with open(wav_file, 'rb') as f:
                    files = {'file': ('test.wav', f, 'audio/wav')}
                    data = {
                        'language': 'zh',
                        'enable_confidence': True,
                        'enable_volume': True
                    }
                    
                    response = requests.post(
                        f"{self.http_url}/transcribe/file",
                        files=files,
                        data=data,
                        timeout=30
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    task_id = result.get('task_id')
                    logger.info(f"File upload successful, task_id: {task_id}")
                    
                    # Poll for result
                    for i in range(30):  # Wait up to 30 seconds
                        time.sleep(1)
                        result_response = requests.get(
                            f"{self.http_url}/transcribe/result/{task_id}",
                            timeout=10
                        )
                        
                        if result_response.status_code == 200:
                            result_data = result_response.json()
                            status = result_data.get('status')
                            
                            if status == 'completed':
                                logger.info(f"Transcription completed: {result_data.get('text', 'No text')}")
                                return True
                            elif status == 'failed':
                                logger.error(f"Transcription failed: {result_data.get('error_message')}")
                                return False
                    
                    logger.warning("Transcription timed out")
                    return False
                else:
                    logger.error(f"File upload failed: {response.status_code} - {response.text}")
                    return False
                    
            finally:
                # Clean up temporary file
                os.unlink(wav_file)
                
        except Exception as e:
            logger.error(f"File upload test failed: {e}")
            return False
    
    def test_sync_transcription(self):
        """测试同步转写"""
        logger.info("Testing sync transcription...")
        
        try:
            # Create test audio
            audio_data = self.create_test_audio(duration=1)  # Shorter for sync
            wav_file = self.save_audio_as_wav(audio_data)
            
            try:
                # Sync transcription
                with open(wav_file, 'rb') as f:
                    files = {'file': ('test.wav', f, 'audio/wav')}
                    data = {
                        'language': 'zh',
                        'enable_confidence': True,
                        'enable_volume': True
                    }
                    
                    response = requests.post(
                        f"{self.http_url}/transcribe/sync",
                        files=files,
                        data=data,
                        timeout=30
                    )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"Sync transcription result: {result.get('text', 'No text')}")
                    logger.info(f"Duration: {result.get('duration', 0)}s")
                    logger.info(f"Cost time: {result.get('cost_time', 'N/A')}")
                    return True
                else:
                    logger.error(f"Sync transcription failed: {response.status_code} - {response.text}")
                    return False
                    
            finally:
                # Clean up temporary file
                os.unlink(wav_file)
                
        except Exception as e:
            logger.error(f"Sync transcription test failed: {e}")
            return False
    
    async def test_websocket_stream(self):
        """测试WebSocket流式识别"""
        logger.info("Testing WebSocket stream...")
        
        try:
            # Create test audio
            audio_data = self.create_test_audio(duration=3)
            
            # Convert to bytes (simulate microphone input)
            audio_int16 = (audio_data * 32767).astype(np.int16)
            audio_bytes = audio_int16.tobytes()
            
            # Connect to WebSocket
            uri = f"{self.ws_url}/ws/stream?language=zh&enable_intermediate_result=true"
            
            async with websockets.connect(uri) as websocket:
                logger.info("WebSocket connected")
                
                # Send audio in chunks
                chunk_size = 3200  # 200ms at 16kHz * 2 bytes
                total_chunks = len(audio_bytes) // chunk_size
                
                # Start sending audio
                send_task = asyncio.create_task(
                    self._send_audio_chunks(websocket, audio_bytes, chunk_size)
                )
                
                # Receive results
                results = []
                timeout_count = 0
                max_timeout = 10
                
                while timeout_count < max_timeout:
                    try:
                        # Wait for message with timeout
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        data = json.loads(message)
                        
                        logger.info(f"Received: {data.get('type', 'unknown')} - {data}")
                        results.append(data)
                        
                        if data.get('type') == 'recognition_result':
                            text = data.get('text', '')
                            if text:
                                logger.info(f"Recognition: {text} (final: {data.get('is_final', False)})")
                        
                        timeout_count = 0  # Reset timeout counter
                        
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if not send_task.done():
                            continue  # Still sending, keep waiting
                        else:
                            break  # Done sending and no more messages
                    
                    except ConnectionClosed:
                        logger.info("WebSocket connection closed")
                        break
                
                # Wait for send task to complete
                await send_task
                
                logger.info(f"WebSocket test completed, received {len(results)} messages")
                return len(results) > 0
                
        except Exception as e:
            logger.error(f"WebSocket test failed: {e}")
            return False
    
    async def _send_audio_chunks(self, websocket, audio_bytes, chunk_size):
        """发送音频数据块"""
        try:
            total_chunks = len(audio_bytes) // chunk_size
            
            for i in range(total_chunks):
                start = i * chunk_size
                end = start + chunk_size
                chunk = audio_bytes[start:end]
                
                await websocket.send(chunk)
                logger.debug(f"Sent chunk {i+1}/{total_chunks}")
                
                # Simulate real-time streaming
                await asyncio.sleep(0.1)  # 100ms delay
            
            logger.info("Finished sending audio chunks")
            
        except Exception as e:
            logger.error(f"Error sending audio chunks: {e}")
    
    def test_service_management(self):
        """测试服务管理接口"""
        logger.info("Testing service management...")
        
        try:
            # Test list tasks
            response = requests.get(f"{self.http_url}/transcribe/tasks", timeout=10)
            if response.status_code == 200:
                tasks = response.json()
                logger.info(f"Active tasks: {tasks.get('total', 0)}")
            
            # Test list sessions
            ws_http_url = self.ws_url.replace("ws://", "http://").replace("wss://", "https://")
            response = requests.get(f"{ws_http_url}/sessions", timeout=10)
            if response.status_code == 200:
                sessions = response.json()
                logger.info(f"Active sessions: {sessions.get('total', 0)}")
            
            return True
            
        except Exception as e:
            logger.error(f"Service management test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("Starting ASR service tests...")
        
        test_results = {}
        
        # Test 1: Health check
        test_results['health_check'] = self.test_health_check()
        
        # Test 2: File upload
        test_results['file_upload'] = self.test_file_upload()
        
        # Test 3: Sync transcription
        test_results['sync_transcription'] = self.test_sync_transcription()
        
        # Test 4: WebSocket stream
        test_results['websocket_stream'] = await self.test_websocket_stream()
        
        # Test 5: Service management
        test_results['service_management'] = self.test_service_management()
        
        # Summary
        logger.info("\n" + "="*50)
        logger.info("TEST RESULTS SUMMARY")
        logger.info("="*50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results.items():
            status = "PASS" if result else "FAIL"
            logger.info(f"{test_name:20} : {status}")
            if result:
                passed += 1
        
        logger.info("="*50)
        logger.info(f"TOTAL: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed!")
            return True
        else:
            logger.warning(f"⚠️  {total - passed} tests failed")
            return False

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test ASR Services")
    parser.add_argument("--http-url", default="http://localhost:8080", help="HTTP service URL")
    parser.add_argument("--ws-url", default="ws://localhost:8081", help="WebSocket service URL")
    parser.add_argument("--test", choices=['health', 'file', 'sync', 'stream', 'management', 'all'], 
                       default='all', help="Specific test to run")
    
    args = parser.parse_args()
    
    tester = ASRServiceTester(args.http_url, args.ws_url)
    
    if args.test == 'all':
        success = await tester.run_all_tests()
    elif args.test == 'health':
        success = tester.test_health_check()
    elif args.test == 'file':
        success = tester.test_file_upload()
    elif args.test == 'sync':
        success = tester.test_sync_transcription()
    elif args.test == 'stream':
        success = await tester.test_websocket_stream()
    elif args.test == 'management':
        success = tester.test_service_management()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
