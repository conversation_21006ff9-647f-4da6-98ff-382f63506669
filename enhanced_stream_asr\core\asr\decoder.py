"""
ASR解码器
基于Individual版本的解码器实现，支持多语种
"""

import logging
import time
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from abc import ABC, abstractmethod

from ..engines import ONNXEngine
from ..audio import FeatureExtractor

logger = logging.getLogger(__name__)


class ASRDecoder(ABC):
    """ASR解码器基类"""
    
    def __init__(self, language: str, config: Dict[str, Any]):
        """
        初始化ASR解码器
        
        Args:
            language: 语种代码
            config: 解码器配置
        """
        self.language = language
        self.config = config
        self.is_initialized = False
        
        # 解码参数
        self.chunk_size = config.get('chunk_size', 16)
        self.left_chunks = config.get('left_chunks', 16)
        self.decoding_window = config.get('decoding_window', 67)
        self.subsampling_rate = config.get('subsampling_rate', 4)
        self.right_context = config.get('right_context', 7)
        
        # 状态变量
        self.reset()
        
    def reset(self):
        """重置解码器状态"""
        self.chunk_idx = 0
        self.cache = {}
        self.outputs = []
        self.offset = 0
        self.required_cache_size = self.chunk_size * self.left_chunks
        
    @abstractmethod
    def initialize(self) -> bool:
        """初始化解码器"""
        pass
        
    @abstractmethod
    def decode_chunk(self, features: np.ndarray) -> Dict[str, Any]:
        """
        解码音频特征块
        
        Args:
            features: 音频特征
            
        Returns:
            解码结果
        """
        pass
        
    @abstractmethod
    def finalize(self) -> Dict[str, Any]:
        """完成解码，返回最终结果"""
        pass
        
    def is_ready(self) -> bool:
        """检查解码器是否就绪"""
        return self.is_initialized
        
    def get_language(self) -> str:
        """获取语种"""
        return self.language
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'language': self.language,
            'chunk_count': self.chunk_idx,
            'cache_size': len(self.cache),
            'output_count': len(self.outputs)
        }


class ONNXASRDecoder(ASRDecoder):
    """基于ONNX的ASR解码器"""
    
    def __init__(self, language: str, config: Dict[str, Any]):
        """
        初始化ONNX ASR解码器
        
        Args:
            language: 语种代码
            config: 解码器配置
        """
        super().__init__(language, config)
        
        self.onnx_engine = None
        self.feature_extractor = None
        self.vocabulary = None
        self.hotwords = None
        
        # 模型路径
        self.model_config = config.get('model', {})
        self.model_path = self.model_config.get('model_path', '')
        self.vocabulary_path = self.model_config.get('vocabulary_path', '')
        self.hotwords_path = self.model_config.get('hotwords_path', '')
        
    def initialize(self) -> bool:
        """初始化ONNX解码器"""
        try:
            # 初始化ONNX引擎
            engine_config = {
                'device': self.model_config.get('device', 'cpu'),
                'device_id': self.model_config.get('device_id', 0),
                'quantized': self.model_config.get('quantized', True),
                'chunk_size': self.chunk_size,
                'left_chunks': self.left_chunks,
                'decoding_window': self.decoding_window
            }
            
            self.onnx_engine = ONNXEngine(self.model_path, engine_config)
            if not self.onnx_engine.load_model():
                logger.error(f"Failed to load ONNX model: {self.model_path}")
                return False
                
            # 初始化特征提取器
            feature_config = {
                'feat_type': 'fbank',
                'num_mel_bins': 80,
                'sample_rate': 16000
            }
            self.feature_extractor = FeatureExtractor(feature_config)
            
            # 加载词汇表
            self._load_vocabulary()
            
            # 加载热词（如果有）
            self._load_hotwords()
            
            self.is_initialized = True
            logger.info(f"ONNX ASR decoder initialized for language: {self.language}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize ONNX ASR decoder: {e}")
            return False
            
    def _load_vocabulary(self):
        """加载词汇表"""
        try:
            if self.vocabulary_path:
                with open(self.vocabulary_path, 'r', encoding='utf-8') as f:
                    self.vocabulary = [line.strip() for line in f.readlines()]
                logger.info(f"Loaded vocabulary: {len(self.vocabulary)} tokens")
            else:
                logger.warning("No vocabulary path specified")
                
        except Exception as e:
            logger.error(f"Failed to load vocabulary: {e}")
            self.vocabulary = None
            
    def _load_hotwords(self):
        """加载热词"""
        try:
            if self.hotwords_path:
                with open(self.hotwords_path, 'r', encoding='utf-8') as f:
                    self.hotwords = [line.strip() for line in f.readlines() if line.strip()]
                logger.info(f"Loaded hotwords: {len(self.hotwords)} words")
            else:
                logger.debug("No hotwords path specified")
                
        except Exception as e:
            logger.warning(f"Failed to load hotwords: {e}")
            self.hotwords = None
            
    def decode_chunk(self, features: np.ndarray) -> Dict[str, Any]:
        """解码特征块"""
        if not self.is_ready():
            return {"error": "Decoder not ready", "success": False}
            
        try:
            start_time = time.time()
            
            # 准备输入特征
            input_features = self._prepare_features(features)
            
            # 执行推理
            result = self.onnx_engine.infer({'features': input_features})
            
            if not result['success']:
                return result
                
            # 解析输出
            decoded_result = self._parse_output(result)
            
            # 更新状态
            self.chunk_idx += 1
            
            # 计算处理时间
            processing_time = (time.time() - start_time) * 1000  # 毫秒
            
            decoded_result.update({
                'chunk_idx': self.chunk_idx,
                'processing_time': processing_time,
                'success': True
            })
            
            return decoded_result
            
        except Exception as e:
            logger.error(f"Chunk decoding failed: {e}")
            return {"error": str(e), "success": False}
            
    def _prepare_features(self, features: np.ndarray) -> np.ndarray:
        """准备输入特征"""
        try:
            # 确保特征是正确的形状 [T, F]
            if features.ndim == 1:
                # 如果是1维，可能是音频数据，需要提取特征
                if self.feature_extractor:
                    features = self.feature_extractor.extract_features(features)
                else:
                    raise ValueError("Cannot extract features: no feature extractor")
                    
            # 添加batch维度 [1, T, F]
            if features.ndim == 2:
                features = features[np.newaxis, :]
                
            return features
            
        except Exception as e:
            logger.error(f"Feature preparation failed: {e}")
            raise
            
    def _parse_output(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """解析模型输出"""
        try:
            # 获取CTC输出
            ctc_outputs = result.get('ctc_outputs')
            if ctc_outputs is None:
                return {"text": "", "confidence": 0.0}
                
            # 简单的CTC解码（贪心搜索）
            text, confidence = self._ctc_greedy_decode(ctc_outputs)
            
            return {
                "text": text,
                "confidence": confidence,
                "language": self.language
            }
            
        except Exception as e:
            logger.error(f"Output parsing failed: {e}")
            return {"text": "", "confidence": 0.0, "error": str(e)}
            
    def _ctc_greedy_decode(self, logits: np.ndarray) -> Tuple[str, float]:
        """CTC贪心解码"""
        try:
            # 应用softmax
            if logits.ndim == 3:
                logits = logits[0]  # 移除batch维度
                
            # 获取最大概率的token序列
            token_ids = np.argmax(logits, axis=-1)
            
            # 移除重复和空白token（假设0是空白token）
            decoded_tokens = []
            prev_token = -1
            
            for token_id in token_ids:
                if token_id != prev_token and token_id != 0:  # 0是空白token
                    decoded_tokens.append(token_id)
                prev_token = token_id
                
            # 转换为文本
            text = self._tokens_to_text(decoded_tokens)
            
            # 计算平均置信度
            max_probs = np.max(logits, axis=-1)
            confidence = float(np.mean(max_probs))
            
            return text, confidence
            
        except Exception as e:
            logger.error(f"CTC decoding failed: {e}")
            return "", 0.0
            
    def _tokens_to_text(self, token_ids: List[int]) -> str:
        """将token ID转换为文本"""
        try:
            if not self.vocabulary:
                # 如果没有词汇表，返回token ID字符串
                return " ".join(str(tid) for tid in token_ids)
                
            # 使用词汇表转换
            tokens = []
            for token_id in token_ids:
                if 0 <= token_id < len(self.vocabulary):
                    tokens.append(self.vocabulary[token_id])
                else:
                    tokens.append(f"<unk_{token_id}>")
                    
            # 合并token为文本
            text = "".join(tokens)
            
            # 简单的后处理
            text = text.replace("▁", " ")  # SentencePiece的空格标记
            text = text.strip()
            
            return text
            
        except Exception as e:
            logger.error(f"Token to text conversion failed: {e}")
            return ""
            
    def finalize(self) -> Dict[str, Any]:
        """完成解码"""
        try:
            # 这里可以进行最终的后处理
            final_result = {
                "language": self.language,
                "total_chunks": self.chunk_idx,
                "success": True
            }
            
            # 重置状态
            self.reset()
            
            return final_result
            
        except Exception as e:
            logger.error(f"Finalization failed: {e}")
            return {"error": str(e), "success": False}
            
    def cleanup(self):
        """清理资源"""
        try:
            if self.onnx_engine:
                self.onnx_engine.unload_model()
                self.onnx_engine = None
                
            self.feature_extractor = None
            self.vocabulary = None
            self.hotwords = None
            self.is_initialized = False
            
            logger.info(f"ASR decoder cleaned up for language: {self.language}")
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
