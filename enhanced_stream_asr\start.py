#!/usr/bin/env python3
"""
Enhanced Stream ASR Server 启动脚本
提供更友好的启动方式和参数配置
"""

import argparse
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import ConfigManager
from utils.logger import setup_logger


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'websockets',
        'numpy',
        'torch',
        'onnxruntime',
        'webrtcvad',
        'pydantic',
        'PyYAML'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True


def check_models(config_manager):
    """检查模型文件是否存在"""
    print("🔍 检查模型文件...")
    
    supported_languages = config_manager.get_supported_languages()
    missing_models = []
    
    for lang in supported_languages:
        lang_config = config_manager.get_language_config(lang)
        model_config = lang_config.get('model', {})
        
        model_path = model_config.get('model_path', '')
        if model_path and not os.path.exists(model_path):
            missing_models.append(f"{lang}: {model_path}")
    
    # 检查LID模型
    lid_config = config_manager.get_lid_config()
    lid_model_path = lid_config.get('model', {}).get('model_path', '')
    if lid_model_path and not os.path.exists(lid_model_path):
        missing_models.append(f"LID: {lid_model_path}")
    
    if missing_models:
        print("⚠️  以下模型文件未找到:")
        for model in missing_models:
            print(f"   - {model}")
        print("\n注意: 缺少模型文件可能导致相应功能无法使用")
        return False
    
    print("✅ 所有模型文件检查完成")
    return True


def check_directories():
    """检查必要的目录是否存在"""
    required_dirs = [
        'configs',
        'configs/lang_configs',
        'web/static',
        'logs'
    ]
    
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if not full_path.exists():
            print(f"📁 创建目录: {dir_path}")
            full_path.mkdir(parents=True, exist_ok=True)
    
    return True


def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                Enhanced Stream ASR Server                   ║
║                增强流式语音识别服务器                          ║
║                                                              ║
║  🎤 Web界面支持    🌍 多语种识别    🤖 自动语种检测           ║
║  🔄 三阶段协议    📊 实时监控      🔧 灵活配置               ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)


def print_startup_info(config_manager):
    """打印启动信息"""
    host = config_manager.get("server.host", "0.0.0.0")
    port = config_manager.get("server.port", 8080)
    supported_languages = config_manager.get_supported_languages()
    
    print(f"🌐 服务器地址: http://{host}:{port}")
    print(f"📡 WebSocket端点: ws://{host}:{port}/ws/stream")
    print(f"🌍 支持语种: {', '.join(supported_languages)}")
    print(f"🤖 自动语种识别: {'启用' if config_manager.get('asr.enable_auto_language_detection', True) else '禁用'}")
    print(f"📊 API文档: http://{host}:{port}/docs")
    print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Enhanced Stream ASR Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python start.py                          # 使用默认配置启动
  python start.py --port 9090              # 指定端口
  python start.py --host 0.0.0.0           # 指定主机
  python start.py --config custom.yaml     # 使用自定义配置
  python start.py --debug                  # 调试模式
  python start.py --check-only             # 仅检查环境，不启动服务
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        default='configs',
        help='配置文件目录或文件路径 (默认: configs)'
    )
    
    parser.add_argument(
        '--host',
        help='服务器主机地址 (覆盖配置文件)'
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        help='服务器端口 (覆盖配置文件)'
    )
    
    parser.add_argument(
        '--workers', '-w',
        type=int,
        help='工作进程数 (覆盖配置文件)'
    )
    
    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='日志级别 (覆盖配置文件)'
    )
    
    parser.add_argument(
        '--check-only',
        action='store_true',
        help='仅检查环境和配置，不启动服务器'
    )
    
    parser.add_argument(
        '--no-model-check',
        action='store_true',
        help='跳过模型文件检查'
    )
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查目录
    check_directories()
    
    try:
        # 加载配置
        print("⚙️  加载配置...")
        config_manager = ConfigManager(args.config)
        
        # 设置日志
        log_level = args.log_level or config_manager.get("logging.level", "INFO")
        logger = setup_logger(
            level=log_level,
            log_file=config_manager.get("logging.file")
        )
        
        # 检查模型文件
        if not args.no_model_check:
            check_models(config_manager)
        
        # 如果只是检查，则退出
        if args.check_only:
            print("✅ 环境检查完成")
            return
        
        # 打印启动信息
        print_startup_info(config_manager)
        
        # 准备启动参数
        host = args.host or config_manager.get("server.host", "0.0.0.0")
        port = args.port or config_manager.get("server.port", 8080)
        workers = args.workers or config_manager.get("server.workers", 1)
        debug = args.debug or config_manager.get("server.debug", False)
        
        print("🚀 启动服务器...")
        print("按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        # 启动服务器
        import uvicorn
        uvicorn.run(
            "server:app",
            host=host,
            port=port,
            workers=workers if not debug else 1,
            reload=debug,
            log_level=log_level.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
