"""
告警管理器
实现性能阈值监控和告警机制
"""

import asyncio
import time
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """告警状态"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    description: str
    metric_name: str
    threshold: float
    comparison: str  # "gt", "lt", "gte", "lte", "eq", "ne"
    level: AlertLevel
    duration: float = 60.0  # 持续时间（秒）
    cooldown: float = 300.0  # 冷却时间（秒）
    enabled: bool = True


@dataclass
class Alert:
    """告警实例"""
    rule_name: str
    level: AlertLevel
    message: str
    metric_value: float
    threshold: float
    timestamp: float
    status: AlertStatus = AlertStatus.ACTIVE
    resolved_at: Optional[float] = None
    count: int = 1


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.alert_handlers: List[Callable[[Alert], None]] = []
        self.metric_history: Dict[str, List[tuple]] = {}  # (timestamp, value)
        self.last_alert_time: Dict[str, float] = {}
        self.running = False
        self.check_interval = 30.0  # 检查间隔（秒）
        
        # 初始化默认规则
        self._init_default_rules()
    
    def _init_default_rules(self):
        """初始化默认告警规则"""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                description="CPU使用率过高",
                metric_name="cpu_percent",
                threshold=80.0,
                comparison="gt",
                level=AlertLevel.WARNING,
                duration=120.0
            ),
            AlertRule(
                name="critical_cpu_usage",
                description="CPU使用率严重过高",
                metric_name="cpu_percent",
                threshold=95.0,
                comparison="gt",
                level=AlertLevel.CRITICAL,
                duration=60.0
            ),
            AlertRule(
                name="high_memory_usage",
                description="内存使用率过高",
                metric_name="memory_percent",
                threshold=85.0,
                comparison="gt",
                level=AlertLevel.WARNING,
                duration=120.0
            ),
            AlertRule(
                name="critical_memory_usage",
                description="内存使用率严重过高",
                metric_name="memory_percent",
                threshold=95.0,
                comparison="gt",
                level=AlertLevel.CRITICAL,
                duration=60.0
            ),
            AlertRule(
                name="high_error_rate",
                description="错误率过高",
                metric_name="error_rate",
                threshold=0.1,  # 10%
                comparison="gt",
                level=AlertLevel.WARNING,
                duration=300.0
            ),
            AlertRule(
                name="slow_response_time",
                description="响应时间过慢",
                metric_name="avg_duration_ms",
                threshold=5000.0,  # 5秒
                comparison="gt",
                level=AlertLevel.WARNING,
                duration=180.0
            ),
            AlertRule(
                name="high_disk_usage",
                description="磁盘使用率过高",
                metric_name="disk_percent",
                threshold=90.0,
                comparison="gt",
                level=AlertLevel.WARNING,
                duration=300.0
            )
        ]
        
        for rule in default_rules:
            self.add_rule(rule)
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.rules[rule.name] = rule
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除告警规则"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
    
    def enable_rule(self, rule_name: str):
        """启用告警规则"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
            logger.info(f"Enabled alert rule: {rule_name}")
    
    def disable_rule(self, rule_name: str):
        """禁用告警规则"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
            logger.info(f"Disabled alert rule: {rule_name}")
    
    def add_alert_handler(self, handler: Callable[[Alert], None]):
        """添加告警处理器"""
        self.alert_handlers.append(handler)
    
    def update_metric(self, metric_name: str, value: float):
        """更新指标值"""
        timestamp = time.time()
        
        if metric_name not in self.metric_history:
            self.metric_history[metric_name] = []
        
        # 添加新值
        self.metric_history[metric_name].append((timestamp, value))
        
        # 保留最近1小时的数据
        cutoff_time = timestamp - 3600
        self.metric_history[metric_name] = [
            (t, v) for t, v in self.metric_history[metric_name] 
            if t >= cutoff_time
        ]
    
    def check_alerts(self):
        """检查告警条件"""
        current_time = time.time()
        
        for rule_name, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            try:
                # 获取指标历史数据
                if rule.metric_name not in self.metric_history:
                    continue
                
                metric_data = self.metric_history[rule.metric_name]
                if not metric_data:
                    continue
                
                # 检查是否满足持续时间要求
                duration_start = current_time - rule.duration
                recent_data = [(t, v) for t, v in metric_data if t >= duration_start]
                
                if not recent_data:
                    continue
                
                # 检查是否所有数据都满足阈值条件
                all_exceed = True
                latest_value = recent_data[-1][1]
                
                for _, value in recent_data:
                    if not self._compare_value(value, rule.threshold, rule.comparison):
                        all_exceed = False
                        break
                
                if all_exceed:
                    # 检查冷却时间
                    last_alert = self.last_alert_time.get(rule_name, 0)
                    if current_time - last_alert < rule.cooldown:
                        continue
                    
                    # 触发告警
                    self._trigger_alert(rule, latest_value, current_time)
                else:
                    # 检查是否需要解决告警
                    if rule_name in self.active_alerts:
                        self._resolve_alert(rule_name, current_time)
                        
            except Exception as e:
                logger.error(f"Error checking alert rule {rule_name}: {e}")
    
    def _compare_value(self, value: float, threshold: float, comparison: str) -> bool:
        """比较值与阈值"""
        if comparison == "gt":
            return value > threshold
        elif comparison == "gte":
            return value >= threshold
        elif comparison == "lt":
            return value < threshold
        elif comparison == "lte":
            return value <= threshold
        elif comparison == "eq":
            return value == threshold
        elif comparison == "ne":
            return value != threshold
        else:
            return False
    
    def _trigger_alert(self, rule: AlertRule, value: float, timestamp: float):
        """触发告警"""
        # 检查是否已有活跃告警
        if rule.name in self.active_alerts:
            # 更新现有告警
            alert = self.active_alerts[rule.name]
            alert.count += 1
            alert.metric_value = value
            alert.timestamp = timestamp
        else:
            # 创建新告警
            message = f"{rule.description}: {rule.metric_name}={value:.2f} (阈值: {rule.threshold})"
            alert = Alert(
                rule_name=rule.name,
                level=rule.level,
                message=message,
                metric_value=value,
                threshold=rule.threshold,
                timestamp=timestamp
            )
            self.active_alerts[rule.name] = alert
            self.alert_history.append(alert)
        
        # 更新最后告警时间
        self.last_alert_time[rule.name] = timestamp
        
        # 调用告警处理器
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error(f"Alert handler error: {e}")
        
        logger.warning(f"Alert triggered: {alert.message}")
    
    def _resolve_alert(self, rule_name: str, timestamp: float):
        """解决告警"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = timestamp
            
            # 从活跃告警中移除
            del self.active_alerts[rule_name]
            
            logger.info(f"Alert resolved: {rule_name}")
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """获取告警历史"""
        return self.alert_history[-limit:]
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """获取告警统计信息"""
        total_alerts = len(self.alert_history)
        active_alerts = len(self.active_alerts)
        
        # 按级别统计
        level_counts = {}
        for alert in self.alert_history:
            level = alert.level.value
            level_counts[level] = level_counts.get(level, 0) + 1
        
        # 按规则统计
        rule_counts = {}
        for alert in self.alert_history:
            rule = alert.rule_name
            rule_counts[rule] = rule_counts.get(rule, 0) + 1
        
        return {
            "total_alerts": total_alerts,
            "active_alerts": active_alerts,
            "level_counts": level_counts,
            "rule_counts": rule_counts,
            "rules_enabled": len([r for r in self.rules.values() if r.enabled]),
            "rules_total": len(self.rules)
        }
    
    async def start(self):
        """启动告警管理器"""
        self.running = True
        logger.info("Alert manager started")
        
        while self.running:
            try:
                self.check_alerts()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Alert manager error: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def stop(self):
        """停止告警管理器"""
        self.running = False
        logger.info("Alert manager stopped")


# 全局告警管理器实例
global_alert_manager = AlertManager()


def log_alert_handler(alert: Alert):
    """日志告警处理器"""
    level_map = {
        AlertLevel.INFO: logging.INFO,
        AlertLevel.WARNING: logging.WARNING,
        AlertLevel.CRITICAL: logging.CRITICAL
    }
    
    log_level = level_map.get(alert.level, logging.WARNING)
    logger.log(log_level, f"ALERT [{alert.level.value.upper()}]: {alert.message}")


def console_alert_handler(alert: Alert):
    """控制台告警处理器"""
    print(f"🚨 ALERT [{alert.level.value.upper()}]: {alert.message}")


# 注册默认告警处理器
global_alert_manager.add_alert_handler(log_alert_handler)
global_alert_manager.add_alert_handler(console_alert_handler)
