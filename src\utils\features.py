import torch
import torchaudio
from audio_utils import load_from_local_path

def wavform_mfcc(waveform, sample_rate, delta = 2):
    # 提取MFCC特征及其一阶和二阶差分特征
    mfcc_transform = torchaudio.transforms.MFCC(sample_rate=sample_rate,
                                                n_mfcc=13,
                                                dct_type=2,
                                                norm='ortho',
                                                melkwargs={"n_fft": 400, "hop_length": 160})
    mfcc_feat = mfcc_transform(waveform[0])
    ans = [mfcc_feat]
    # Calculate the 1st derivative
    if delta >= 1:
        mfcc_delta1 = torchaudio.functional.compute_deltas(mfcc_feat)
        ans.append(mfcc_delta1)
    if delta >= 2:
        mfcc_delta2 = torchaudio.functional.compute_deltas(mfcc_delta1)
        ans.append(mfcc_delta2)

    # Concat & transpose
    ans = torch.transpose(torch.cat(ans, dim = 0), 1, 0)

    return ans

def mfcc(wav_path, delta = 2):
    waveform, sample_rate = load_from_local_path(wav_path)
    return wavform_mfcc(waveform=waveform, sample_rate=sample_rate, delta = delta)

