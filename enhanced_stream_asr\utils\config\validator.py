"""
配置验证器
"""

import os
from typing import Dict, Any, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_server_config(config: Dict[str, Any]) -> List[str]:
        """
        验证服务器配置
        
        Args:
            config: 服务器配置字典
            
        Returns:
            错误列表，空列表表示验证通过
        """
        errors = []
        
        # 验证服务器配置
        server_config = config.get('server', {})
        
        # 验证端口范围
        port = server_config.get('port', 8080)
        if not isinstance(port, int) or not (1024 <= port <= 65535):
            errors.append(f"Invalid port: {port}, must be integer between 1024-65535")
        
        # 验证主机地址
        host = server_config.get('host', '0.0.0.0')
        if not isinstance(host, str) or not host.strip():
            errors.append(f"Invalid host: {host}, must be non-empty string")
        
        # 验证工作进程数
        workers = server_config.get('workers', 1)
        if not isinstance(workers, int) or workers < 1:
            errors.append(f"Invalid workers: {workers}, must be positive integer")
        
        # 验证音频配置
        audio_config = config.get('audio', {})
        
        # 验证采样率
        sample_rate = audio_config.get('sample_rate', 16000)
        supported_rates = [8000, 16000, 22050, 44100, 48000]
        if sample_rate not in supported_rates:
            errors.append(f"Unsupported sample rate: {sample_rate}, supported: {supported_rates}")
        
        # 验证声道数
        channels = audio_config.get('channels', 1)
        if not isinstance(channels, int) or channels not in [1, 2]:
            errors.append(f"Invalid channels: {channels}, must be 1 or 2")
        
        # 验证音频块时长
        chunk_duration = audio_config.get('chunk_duration', 0.4)
        if not isinstance(chunk_duration, (int, float)) or chunk_duration <= 0:
            errors.append(f"Invalid chunk_duration: {chunk_duration}, must be positive number")
        
        # 验证最大音频时长
        max_duration = audio_config.get('max_audio_duration', 60)
        if not isinstance(max_duration, (int, float)) or max_duration <= 0:
            errors.append(f"Invalid max_audio_duration: {max_duration}, must be positive number")
        
        # 验证WebSocket配置
        websocket_config = config.get('websocket', {})
        
        # 验证心跳间隔
        heartbeat_interval = websocket_config.get('heartbeat_interval', 30)
        if not isinstance(heartbeat_interval, (int, float)) or heartbeat_interval <= 0:
            errors.append(f"Invalid heartbeat_interval: {heartbeat_interval}, must be positive number")
        
        # 验证最大连接数
        max_connections = websocket_config.get('max_connections', 100)
        if not isinstance(max_connections, int) or max_connections <= 0:
            errors.append(f"Invalid max_connections: {max_connections}, must be positive integer")
        
        # 验证连接超时
        connection_timeout = websocket_config.get('connection_timeout', 300)
        if not isinstance(connection_timeout, (int, float)) or connection_timeout <= 0:
            errors.append(f"Invalid connection_timeout: {connection_timeout}, must be positive number")
        
        return errors
    
    @staticmethod
    def validate_language_config(lang_code: str, config: Dict[str, Any]) -> List[str]:
        """
        验证语种配置
        
        Args:
            lang_code: 语种代码
            config: 语种配置字典
            
        Returns:
            错误列表，空列表表示验证通过
        """
        errors = []
        
        # 验证语种代码
        if not isinstance(lang_code, str) or not lang_code.strip():
            errors.append(f"Invalid language code: {lang_code}")
            return errors
        
        # 验证语种名称
        name = config.get('name', '')
        if not isinstance(name, str) or not name.strip():
            errors.append(f"Missing or invalid name for language: {lang_code}")
        
        # 验证分隔符
        separator = config.get('separator', '')
        if not isinstance(separator, str):
            errors.append(f"Missing or invalid separator for language: {lang_code}")
        
        # 验证静音阈值
        silence_threshold = config.get('silence_threshold', 0.35)
        if not isinstance(silence_threshold, (int, float)) or not (0.0 <= silence_threshold <= 1.0):
            errors.append(f"Invalid silence_threshold for {lang_code}: {silence_threshold}, must be between 0.0-1.0")
        
        # 验证模型配置
        model_config = config.get('model', {})
        if not isinstance(model_config, dict):
            errors.append(f"Missing or invalid model config for language: {lang_code}")
            return errors
        
        # 验证模型路径
        model_path = model_config.get('model_path', '')
        if not model_path:
            errors.append(f"Missing model_path for language: {lang_code}")
        elif not Path(model_path).exists():
            errors.append(f"Model path not found for {lang_code}: {model_path}")
        
        # 验证词汇表路径
        vocab_path = model_config.get('vocabulary_path', '')
        if vocab_path and not Path(vocab_path).exists():
            errors.append(f"Vocabulary path not found for {lang_code}: {vocab_path}")
        
        # 验证热词路径（如果存在）
        hotwords_path = model_config.get('hotwords_path', '')
        if hotwords_path and not Path(hotwords_path).exists():
            errors.append(f"Hotwords path not found for {lang_code}: {hotwords_path}")
        
        # 验证特性配置
        features = config.get('features', {})
        if isinstance(features, dict):
            # 验证布尔值特性
            bool_features = ['enable_punctuation', 'enable_itn', 'enable_hotwords']
            for feature in bool_features:
                value = features.get(feature)
                if value is not None and not isinstance(value, bool):
                    errors.append(f"Invalid {feature} for {lang_code}: {value}, must be boolean")
        
        return errors
    
    @staticmethod
    def validate_lid_config(config: Dict[str, Any]) -> List[str]:
        """
        验证LID配置
        
        Args:
            config: LID配置字典
            
        Returns:
            错误列表，空列表表示验证通过
        """
        errors = []
        
        # 验证是否启用LID
        enable = config.get('enable', True)
        if not isinstance(enable, bool):
            errors.append(f"Invalid enable flag: {enable}, must be boolean")
        
        if not enable:
            return errors  # 如果未启用LID，跳过其他验证
        
        # 验证模型配置
        model_config = config.get('model', {})
        if not isinstance(model_config, dict):
            errors.append("Missing or invalid model config for LID")
            return errors
        
        # 验证模型路径
        model_path = model_config.get('model_path', '')
        if not model_path:
            errors.append("Missing model_path for LID")
        elif not Path(model_path).exists():
            errors.append(f"LID model not found: {model_path}")
        
        # 验证置信度阈值
        confidence_threshold = config.get('confidence_threshold', 0.8)
        if not isinstance(confidence_threshold, (int, float)) or not (0.0 <= confidence_threshold <= 1.0):
            errors.append(f"Invalid confidence_threshold: {confidence_threshold}, must be between 0.0-1.0")
        
        # 验证最小音频时长
        min_duration = config.get('min_audio_duration', 0.4)
        if not isinstance(min_duration, (int, float)) or min_duration <= 0:
            errors.append(f"Invalid min_audio_duration: {min_duration}, must be positive number")
        
        # 验证最大音频时长
        max_duration = config.get('max_audio_duration', 2.4)
        if not isinstance(max_duration, (int, float)) or max_duration <= 0:
            errors.append(f"Invalid max_audio_duration: {max_duration}, must be positive number")
        
        if min_duration >= max_duration:
            errors.append(f"min_audio_duration ({min_duration}) must be less than max_audio_duration ({max_duration})")
        
        # 验证渐进式步骤
        progressive_steps = config.get('progressive_steps', [])
        if not isinstance(progressive_steps, list):
            errors.append(f"Invalid progressive_steps: {progressive_steps}, must be list")
        else:
            for i, step in enumerate(progressive_steps):
                if not isinstance(step, (int, float)) or step <= 0:
                    errors.append(f"Invalid progressive_steps[{i}]: {step}, must be positive number")
            
            # 验证步骤是否递增
            if progressive_steps != sorted(progressive_steps):
                errors.append(f"progressive_steps must be in ascending order: {progressive_steps}")
        
        # 验证语种映射
        language_mapping = config.get('language_mapping', {})
        if not isinstance(language_mapping, dict):
            errors.append(f"Invalid language_mapping: {language_mapping}, must be dict")
        else:
            for idx, lang in language_mapping.items():
                if not isinstance(idx, int) or idx < 0:
                    errors.append(f"Invalid language_mapping index: {idx}, must be non-negative integer")
                if not isinstance(lang, str) or not lang.strip():
                    errors.append(f"Invalid language_mapping value: {lang}, must be non-empty string")
        
        return errors
    
    @staticmethod
    def validate_vad_config(config: Dict[str, Any]) -> List[str]:
        """
        验证VAD配置
        
        Args:
            config: VAD配置字典
            
        Returns:
            错误列表，空列表表示验证通过
        """
        errors = []
        
        # 验证是否启用VAD
        enable = config.get('enable', True)
        if not isinstance(enable, bool):
            errors.append(f"Invalid enable flag: {enable}, must be boolean")
        
        if not enable:
            return errors
        
        # 验证VAD类型
        vad_type = config.get('vad_type', 'webrtcvad')
        supported_types = ['webrtcvad', 'silero']
        if vad_type not in supported_types:
            errors.append(f"Unsupported vad_type: {vad_type}, supported: {supported_types}")
        
        # 验证VAD级别
        vad_level = config.get('vad_level', 1)
        if not isinstance(vad_level, int) or not (0 <= vad_level <= 3):
            errors.append(f"Invalid vad_level: {vad_level}, must be integer between 0-3")
        
        # 验证帧长度
        frame_length = config.get('frame_length', 30)
        if not isinstance(frame_length, int) or frame_length <= 0:
            errors.append(f"Invalid frame_length: {frame_length}, must be positive integer")
        
        # 验证窗口大小
        window_size = config.get('window_size', 10)
        if not isinstance(window_size, int) or window_size <= 0:
            errors.append(f"Invalid window_size: {window_size}, must be positive integer")
        
        # 验证分段阈值
        seg_threshold = config.get('seg_threshold', 0.9)
        if not isinstance(seg_threshold, (int, float)) or not (0.0 <= seg_threshold <= 1.0):
            errors.append(f"Invalid seg_threshold: {seg_threshold}, must be between 0.0-1.0")
        
        return errors
    
    @staticmethod
    def validate_all_configs(server_config: Dict[str, Any], 
                           language_configs: Dict[str, Dict[str, Any]], 
                           lid_config: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        验证所有配置
        
        Args:
            server_config: 服务器配置
            language_configs: 语种配置字典
            lid_config: LID配置
            
        Returns:
            验证结果字典，键为配置类型，值为错误列表
        """
        validation_results = {}
        
        # 验证服务器配置
        validation_results['server'] = ConfigValidator.validate_server_config(server_config)
        
        # 验证语种配置
        validation_results['languages'] = {}
        for lang_code, lang_config in language_configs.items():
            validation_results['languages'][lang_code] = ConfigValidator.validate_language_config(lang_code, lang_config)
        
        # 验证LID配置
        validation_results['lid'] = ConfigValidator.validate_lid_config(lid_config)
        
        # 验证VAD配置
        vad_config = server_config.get('vad', {})
        validation_results['vad'] = ConfigValidator.validate_vad_config(vad_config)
        
        return validation_results
    
    @staticmethod
    def has_errors(validation_results: Dict[str, Any]) -> bool:
        """
        检查验证结果是否包含错误
        
        Args:
            validation_results: 验证结果字典
            
        Returns:
            是否包含错误
        """
        for key, value in validation_results.items():
            if key == 'languages':
                for lang_errors in value.values():
                    if lang_errors:
                        return True
            elif isinstance(value, list) and value:
                return True
        return False
    
    @staticmethod
    def format_validation_errors(validation_results: Dict[str, Any]) -> str:
        """
        格式化验证错误信息
        
        Args:
            validation_results: 验证结果字典
            
        Returns:
            格式化的错误信息字符串
        """
        error_lines = []
        
        for config_type, errors in validation_results.items():
            if config_type == 'languages':
                for lang_code, lang_errors in errors.items():
                    if lang_errors:
                        error_lines.append(f"Language '{lang_code}' configuration errors:")
                        for error in lang_errors:
                            error_lines.append(f"  - {error}")
            elif isinstance(errors, list) and errors:
                error_lines.append(f"{config_type.title()} configuration errors:")
                for error in errors:
                    error_lines.append(f"  - {error}")
        
        return "\n".join(error_lines) if error_lines else "No configuration errors found."
