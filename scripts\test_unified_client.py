#!/usr/bin/env python3
"""
Test client for the unified ASR model
"""

import numpy as np
import json
import argparse
import sys
import os

# Add tools to path for audio utilities
sys.path.append('tools')

try:
    import tritonclient.http as httpclient
    TRITON_AVAILABLE = True
except ImportError:
    print("Warning: tritonclient not available. Mock mode only.")
    TRITON_AVAILABLE = False

def create_mock_audio(duration_seconds=3, sample_rate=16000):
    """Create mock audio data for testing"""
    # Generate a simple sine wave
    t = np.linspace(0, duration_seconds, int(duration_seconds * sample_rate), False)
    frequency = 440  # A4 note
    audio_data = np.sin(2 * np.pi * frequency * t).astype(np.float32)
    
    # Add some noise to make it more realistic
    noise = np.random.normal(0, 0.1, audio_data.shape).astype(np.float32)
    audio_data = audio_data + noise
    
    return audio_data

def load_audio_file(file_path):
    """Load audio file using tools"""
    try:
        from audio_utils import load_from_local_path
        waveform, sample_rate = load_from_local_path(file_path)
        
        # Convert to numpy array
        if hasattr(waveform, 'numpy'):
            audio_data = waveform.numpy().flatten()
        else:
            audio_data = waveform.flatten()
        
        return audio_data.astype(np.float32), sample_rate
    except ImportError:
        print("Error: audio_utils not available. Cannot load audio file.")
        return None, None
    except Exception as e:
        print(f"Error loading audio file: {e}")
        return None, None

def test_unified_asr_mock(audio_data, lang_code="zh"):
    """Test unified ASR in mock mode (without Triton server)"""
    print("Running in mock mode (no Triton server required)")
    print(f"Audio data shape: {audio_data.shape}")
    print(f"Audio duration: {len(audio_data) / 16000:.2f} seconds")
    print(f"Language: {lang_code}")
    
    # Mock response
    mock_response = {
        "text": "这是一个模拟的语音识别结果",
        "segments": [
            {
                "text": "这是一个模拟的",
                "start": 0.0,
                "end": 1.5
            },
            {
                "text": "语音识别结果",
                "start": 1.5,
                "end": 3.0
            }
        ],
        "duration": len(audio_data) / 16000,
        "info": {
            "cost_time": "0.1234 s"
        }
    }
    
    print("\nMock Response:")
    print(json.dumps(mock_response, ensure_ascii=False, indent=2))
    return mock_response

def test_unified_asr_triton(audio_data, lang_code="zh", server_url="localhost:8000"):
    """Test unified ASR with Triton server"""
    if not TRITON_AVAILABLE:
        print("Error: tritonclient not available")
        return None
    
    try:
        # Create client
        client = httpclient.InferenceServerClient(url=server_url)
        
        # Check if model is ready
        if not client.is_model_ready("unified_asr"):
            print("Error: unified_asr model is not ready")
            return None
        
        print(f"Connected to Triton server at {server_url}")
        print(f"Audio data shape: {audio_data.shape}")
        print(f"Language: {lang_code}")
        
        # Prepare input data
        lang_array = np.array([lang_code], dtype=object)
        
        # Create input tensors
        inputs = [
            httpclient.InferInput("WAV", audio_data.shape, "FP32"),
            httpclient.InferInput("LANG", lang_array.shape, "BYTES")
        ]
        
        inputs[0].set_data_from_numpy(audio_data)
        inputs[1].set_data_from_numpy(lang_array)
        
        # Create output tensor
        outputs = [httpclient.InferRequestedOutput("TRANSCRIPTS")]
        
        # Send inference request
        print("Sending inference request...")
        response = client.infer("unified_asr", inputs, outputs=outputs)
        
        # Get result
        result_array = response.as_numpy("TRANSCRIPTS")
        
        if len(result_array) > 0:
            result_json = result_array[0].decode('utf-8')
            result = json.loads(result_json)
            
            print("\nInference Result:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
        else:
            print("No results returned")
            return None
            
    except Exception as e:
        print(f"Error during inference: {e}")
        return None

def benchmark_performance(audio_data, lang_code="zh", server_url="localhost:8000", num_requests=10):
    """Benchmark the unified ASR performance"""
    if not TRITON_AVAILABLE:
        print("Benchmark requires tritonclient")
        return
    
    import time
    
    print(f"Running benchmark with {num_requests} requests...")
    
    try:
        client = httonclient.InferenceServerClient(url=server_url)
        
        if not client.is_model_ready("unified_asr"):
            print("Error: unified_asr model is not ready")
            return
        
        times = []
        
        for i in range(num_requests):
            start_time = time.time()
            
            # Prepare inputs
            lang_array = np.array([lang_code], dtype=object)
            inputs = [
                httpclient.InferInput("WAV", audio_data.shape, "FP32"),
                httpclient.InferInput("LANG", lang_array.shape, "BYTES")
            ]
            inputs[0].set_data_from_numpy(audio_data)
            inputs[1].set_data_from_numpy(lang_array)
            
            outputs = [httpclient.InferRequestedOutput("TRANSCRIPTS")]
            
            # Send request
            response = client.infer("unified_asr", inputs, outputs=outputs)
            
            end_time = time.time()
            request_time = end_time - start_time
            times.append(request_time)
            
            print(f"Request {i+1}/{num_requests}: {request_time:.3f}s")
        
        # Calculate statistics
        avg_time = np.mean(times)
        min_time = np.min(times)
        max_time = np.max(times)
        std_time = np.std(times)
        
        print(f"\nBenchmark Results:")
        print(f"Average time: {avg_time:.3f}s")
        print(f"Min time: {min_time:.3f}s")
        print(f"Max time: {max_time:.3f}s")
        print(f"Std deviation: {std_time:.3f}s")
        print(f"Requests per second: {1/avg_time:.2f}")
        
    except Exception as e:
        print(f"Benchmark error: {e}")

def main():
    parser = argparse.ArgumentParser(description="Test Unified ASR Model")
    parser.add_argument("--audio-file", help="Path to audio file")
    parser.add_argument("--duration", type=float, default=3.0, help="Duration for mock audio (seconds)")
    parser.add_argument("--lang", default="zh", help="Language code")
    parser.add_argument("--server", default="localhost:8000", help="Triton server URL")
    parser.add_argument("--mock", action="store_true", help="Run in mock mode")
    parser.add_argument("--benchmark", type=int, help="Run benchmark with N requests")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("UNIFIED ASR MODEL TEST CLIENT")
    print("=" * 60)
    
    # Prepare audio data
    if args.audio_file:
        print(f"Loading audio file: {args.audio_file}")
        audio_data, sample_rate = load_audio_file(args.audio_file)
        if audio_data is None:
            print("Failed to load audio file")
            return
        print(f"Loaded audio: {len(audio_data)} samples at {sample_rate}Hz")
    else:
        print(f"Creating mock audio: {args.duration}s")
        audio_data = create_mock_audio(args.duration)
    
    # Run test
    if args.mock or not TRITON_AVAILABLE:
        result = test_unified_asr_mock(audio_data, args.lang)
    else:
        result = test_unified_asr_triton(audio_data, args.lang, args.server)
    
    # Run benchmark if requested
    if args.benchmark and not args.mock and TRITON_AVAILABLE:
        print("\n" + "=" * 60)
        benchmark_performance(audio_data, args.lang, args.server, args.benchmark)
    
    print("\n" + "=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
