/**
 * UI控制器
 * 管理界面元素和用户交互
 */

class UIController {
    constructor() {
        this.elements = {};
        this.recognitionResults = [];
        this.sessionStats = {
            recognitionCount: 0,
            totalLatency: 0,
            totalAudioTime: 0
        };
        
        this.initializeElements();
        this.bindEvents();
    }
    
    initializeElements() {
        // 获取所有UI元素
        this.elements = {
            // 设置控件
            languageMode: document.getElementById('languageMode'),
            selectedLanguage: document.getElementById('selectedLanguage'),
            manualLanguageGroup: document.getElementById('manualLanguageGroup'),
            sampleRate: document.getElementById('sampleRate'),
            enableIntermediate: document.getElementById('enableIntermediate'),
            enablePunctuation: document.getElementById('enablePunctuation'),
            customSeparator: document.getElementById('customSeparator'),
            
            // 控制按钮
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            clearBtn: document.getElementById('clearBtn'),
            
            // 状态显示
            connectionStatus: document.getElementById('connectionStatus'),
            recordingStatus: document.getElementById('recordingStatus'),
            detectedLanguage: document.getElementById('detectedLanguage'),
            audioDuration: document.getElementById('audioDuration'),
            
            // 音量指示器
            volumeLevel: document.getElementById('volumeLevel'),
            
            // 结果显示
            resultsContainer: document.getElementById('resultsContainer'),
            
            // 会话信息
            sessionId: document.getElementById('sessionId'),
            recognitionCount: document.getElementById('recognitionCount'),
            avgLatency: document.getElementById('avgLatency'),
            totalAudioTime: document.getElementById('totalAudioTime'),
            
            // 日志
            toggleLogs: document.getElementById('toggleLogs'),
            logsContainer: document.getElementById('logsContainer'),
            logsList: document.getElementById('logsList'),
            clearLogs: document.getElementById('clearLogs')
        };
    }
    
    bindEvents() {
        // 语种模式切换
        this.elements.languageMode.addEventListener('change', (e) => {
            const isManual = e.target.value === 'manual';
            this.elements.manualLanguageGroup.style.display = isManual ? 'block' : 'none';
        });
        
        // 日志切换
        this.elements.toggleLogs.addEventListener('click', () => {
            const isVisible = this.elements.logsContainer.style.display !== 'none';
            this.elements.logsContainer.style.display = isVisible ? 'none' : 'block';
            
            const icon = this.elements.toggleLogs.querySelector('i');
            icon.className = isVisible ? 'fas fa-chevron-down' : 'fas fa-chevron-up';
        });
        
        // 清空日志
        this.elements.clearLogs.addEventListener('click', () => {
            this.elements.logsList.innerHTML = '';
        });
        
        // 清空结果
        this.elements.clearBtn.addEventListener('click', () => {
            this.clearResults();
        });
    }
    
    updateConnectionStatus(status) {
        const statusElement = this.elements.connectionStatus;
        statusElement.textContent = this.getStatusText(status);
        statusElement.className = `status-value ${status}`;
    }
    
    updateRecordingStatus(isRecording) {
        const statusElement = this.elements.recordingStatus;
        statusElement.textContent = isRecording ? '录音中' : '未录音';
        statusElement.className = `status-value ${isRecording ? 'recording' : ''}`;
        
        // 更新按钮状态
        this.elements.startBtn.disabled = isRecording;
        this.elements.stopBtn.disabled = !isRecording;
    }
    
    updateDetectedLanguage(language) {
        this.elements.detectedLanguage.textContent = this.getLanguageName(language) || '-';
    }
    
    updateAudioDuration(duration) {
        this.elements.audioDuration.textContent = `${duration.toFixed(1)}s`;
    }
    
    updateVolumeLevel(volume) {
        const percentage = Math.min(100, volume * 100);
        this.elements.volumeLevel.style.width = `${percentage}%`;
    }
    
    updateSessionInfo(sessionId, stats = {}) {
        if (sessionId) {
            this.elements.sessionId.textContent = sessionId.substring(0, 8) + '...';
        }
        
        if (stats.recognitionCount !== undefined) {
            this.sessionStats.recognitionCount = stats.recognitionCount;
            this.elements.recognitionCount.textContent = stats.recognitionCount;
        }
        
        if (stats.avgLatency !== undefined) {
            this.elements.avgLatency.textContent = `${stats.avgLatency.toFixed(0)}ms`;
        }
        
        if (stats.totalAudioTime !== undefined) {
            this.sessionStats.totalAudioTime = stats.totalAudioTime;
            this.elements.totalAudioTime.textContent = `${stats.totalAudioTime.toFixed(1)}s`;
        }
    }
    
    addRecognitionResult(result) {
        // 更新统计信息
        if (result.isFinal) {
            this.sessionStats.recognitionCount++;
            if (result.processingTime) {
                this.sessionStats.totalLatency += result.processingTime;
            }
        }
        
        // 移除"无结果"提示
        const noResults = this.elements.resultsContainer.querySelector('.no-results');
        if (noResults) {
            noResults.remove();
        }
        
        // 查找是否有相同序列号的中间结果
        let existingResult = null;
        if (!result.isFinal) {
            existingResult = this.elements.resultsContainer.querySelector(
                `[data-sequence-id="${result.sequenceId}"]`
            );
        }
        
        if (existingResult && !result.isFinal) {
            // 更新现有的中间结果
            this.updateResultElement(existingResult, result);
        } else {
            // 创建新的结果元素
            const resultElement = this.createResultElement(result);
            this.elements.resultsContainer.appendChild(resultElement);
            
            // 滚动到底部
            this.elements.resultsContainer.scrollTop = this.elements.resultsContainer.scrollHeight;
        }
        
        // 更新会话统计
        this.updateSessionInfo(null, {
            recognitionCount: this.sessionStats.recognitionCount,
            avgLatency: this.sessionStats.totalLatency / Math.max(1, this.sessionStats.recognitionCount)
        });
    }
    
    createResultElement(result) {
        const resultDiv = document.createElement('div');
        resultDiv.className = `result-item ${result.isFinal ? 'final' : 'intermediate'}`;
        resultDiv.setAttribute('data-sequence-id', result.sequenceId);
        
        const headerDiv = document.createElement('div');
        headerDiv.className = 'result-header';
        
        const timestamp = new Date(result.timestamp).toLocaleTimeString();
        const typeText = result.isFinal ? '最终结果' : '中间结果';
        const languageText = result.language ? ` | ${this.getLanguageName(result.language)}` : '';
        const confidenceText = result.confidence ? ` | 置信度: ${(result.confidence * 100).toFixed(1)}%` : '';
        const latencyText = result.processingTime ? ` | 延迟: ${result.processingTime.toFixed(0)}ms` : '';
        
        headerDiv.innerHTML = `
            <span>${timestamp} | ${typeText}${languageText}${confidenceText}${latencyText}</span>
        `;
        
        const textDiv = document.createElement('div');
        textDiv.className = 'result-text';
        textDiv.textContent = result.text;
        
        resultDiv.appendChild(headerDiv);
        resultDiv.appendChild(textDiv);
        
        return resultDiv;
    }
    
    updateResultElement(element, result) {
        const textDiv = element.querySelector('.result-text');
        if (textDiv) {
            textDiv.textContent = result.text;
        }
        
        // 更新头部信息
        const headerDiv = element.querySelector('.result-header');
        if (headerDiv) {
            const timestamp = new Date(result.timestamp).toLocaleTimeString();
            const typeText = result.isFinal ? '最终结果' : '中间结果';
            const languageText = result.language ? ` | ${this.getLanguageName(result.language)}` : '';
            const confidenceText = result.confidence ? ` | 置信度: ${(result.confidence * 100).toFixed(1)}%` : '';
            const latencyText = result.processingTime ? ` | 延迟: ${result.processingTime.toFixed(0)}ms` : '';
            
            headerDiv.innerHTML = `
                <span>${timestamp} | ${typeText}${languageText}${confidenceText}${latencyText}</span>
            `;
        }
        
        // 如果变成最终结果，更新样式
        if (result.isFinal) {
            element.className = 'result-item final';
        }
    }
    
    clearResults() {
        this.elements.resultsContainer.innerHTML = `
            <div class="no-results">
                <i class="fas fa-microphone-slash"></i>
                <p>点击"开始录音"开始语音识别</p>
            </div>
        `;
        
        // 重置统计信息
        this.sessionStats = {
            recognitionCount: 0,
            totalLatency: 0,
            totalAudioTime: 0
        };
        
        this.updateSessionInfo(null, this.sessionStats);
    }
    
    addLog(level, message, timestamp) {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${level}`;
        logEntry.textContent = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        
        this.elements.logsList.appendChild(logEntry);
        
        // 限制日志条数
        const logs = this.elements.logsList.children;
        if (logs.length > 100) {
            this.elements.logsList.removeChild(logs[0]);
        }
        
        // 滚动到底部
        this.elements.logsList.scrollTop = this.elements.logsList.scrollHeight;
    }
    
    getConfig() {
        return {
            language: this.elements.languageMode.value === 'manual' ? 
                     this.elements.selectedLanguage.value : null,
            autoLanguageDetection: this.elements.languageMode.value === 'auto',
            sampleRate: parseInt(this.elements.sampleRate.value),
            enableIntermediate: this.elements.enableIntermediate.checked,
            enablePunctuation: this.elements.enablePunctuation.checked,
            customSeparator: this.elements.customSeparator.value || null
        };
    }
    
    setButtonsEnabled(enabled) {
        this.elements.startBtn.disabled = !enabled;
        this.elements.stopBtn.disabled = true;
    }
    
    getStatusText(status) {
        const statusMap = {
            'disconnected': '未连接',
            'connecting': '连接中',
            'handshaking': '握手中',
            'connected': '已连接',
            'recording': '录音中',
            'processing': '处理中',
            'disconnecting': '断开中'
        };
        return statusMap[status] || status;
    }
    
    getLanguageName(code) {
        const languageMap = {
            'zh': '中文',
            'en': 'English',
            'ru': 'Русский',
            'ug': 'ئۇيغۇرچە',
            'kk': 'Қазақша'
        };
        return languageMap[code] || code;
    }
}
