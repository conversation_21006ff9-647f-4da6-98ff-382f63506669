# 中文语种配置

code: "zh"
name: "中文"

# 分隔符设置
separator: "，"                    # 中文全角逗号
silence_threshold: 0.35           # 静音阈值（秒）

# 模型配置
model:
  model_path: "models/zh"              # 模型目录路径，包含encoder.onnx, ctc.onnx等
  dict_path: "models/zh/units.txt"     # 词典文件路径
  hotwords_path: "models/zh/hotwords.txt"  # 热词文件路径

  # 解码参数
  chunk_size: 16
  left_chunks: 16
  decoding_window: 67
  subsampling_rate: 4
  right_context: 7

  # 设备设置
  device: "cpu"
  device_id: 0
  quantized: true

  # 模型结构参数
  num_blocks: 12
  head: 8
  output_size: 512
  cnn_module_kernel: 15

# 特征配置
features:
  enable_punctuation: true      # 启用标点符号预测
  enable_itn: true             # 启用逆文本标准化
  enable_hotwords: true        # 启用热词增强
  enable_modal_particle_filter: true  # 启用语气词过滤

# 后处理配置
postprocess:
  enable_text_normalization: true
  enable_number_conversion: true
  enable_time_conversion: true
  enable_date_conversion: true

# 性能配置
performance:
  max_sentence_silence: 450    # 最大句子静音时长（毫秒）
  blank_interval: 0.5          # 空白间隔（秒）
  
# 热词配置
hotwords:
  enable: true
  boost_score: 40              # 热词增强分数
  max_hotwords: 1000           # 最大热词数量
