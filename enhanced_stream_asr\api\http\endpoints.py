"""
HTTP API端点
提供REST API接口实现
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import HTTPException
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class LanguageInfo(BaseModel):
    """语种信息模型"""
    code: str
    name: str
    supported: bool
    loaded: bool = False


class ServerStatus(BaseModel):
    """服务器状态模型"""
    status: str
    uptime: float
    active_sessions: int
    total_connections: int


class HTTPEndpoints:
    """HTTP API端点类"""
    
    def __init__(self, session_manager, config_manager):
        """
        初始化HTTP端点
        
        Args:
            session_manager: 会话管理器
            config_manager: 配置管理器
        """
        self.session_manager = session_manager
        self.config_manager = config_manager
        
    async def get_languages(self) -> Dict[str, Any]:
        """获取支持的语种列表"""
        try:
            supported_languages = self.config_manager.get_supported_languages()
            
            languages = []
            for lang_code in supported_languages:
                lang_config = self.config_manager.get_language_config(lang_code)
                languages.append(LanguageInfo(
                    code=lang_code,
                    name=lang_config.get('name', lang_code),
                    supported=True,
                    loaded=False  # 这里可以检查是否已加载
                ))
                
            return {
                "languages": [lang.dict() for lang in languages],
                "default": self.config_manager.get("asr.default_language", "zh"),
                "total": len(languages)
            }
            
        except Exception as e:
            logger.error(f"Failed to get languages: {e}")
            raise HTTPException(status_code=500, detail="Failed to get languages")
            
    async def get_server_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        try:
            # 获取会话统计
            session_stats = self.session_manager.get_statistics()
            
            status = ServerStatus(
                status="running",
                uptime=0.0,  # 这里可以计算实际运行时间
                active_sessions=session_stats.get("total_sessions", 0),
                total_connections=session_stats.get("total_sessions", 0)
            )
            
            return status.dict()
            
        except Exception as e:
            logger.error(f"Failed to get server status: {e}")
            raise HTTPException(status_code=500, detail="Failed to get server status")
            
    async def get_configuration(self) -> Dict[str, Any]:
        """获取服务器配置"""
        try:
            return {
                "audio": {
                    "sample_rate": self.config_manager.get("audio.sample_rate", 16000),
                    "channels": self.config_manager.get("audio.channels", 1),
                    "chunk_duration": self.config_manager.get("audio.chunk_duration", 0.4),
                    "supported_sample_rates": self.config_manager.get("audio.supported_sample_rates", [16000])
                },
                "asr": {
                    "supported_languages": self.config_manager.get_supported_languages(),
                    "enable_auto_language_detection": self.config_manager.get("asr.enable_auto_language_detection", True),
                    "enable_intermediate_result": self.config_manager.get("asr.enable_intermediate_result", True),
                    "enable_punctuation": self.config_manager.get("asr.enable_punctuation", True)
                },
                "websocket": {
                    "heartbeat_interval": self.config_manager.get("websocket.heartbeat_interval", 30),
                    "max_connections": self.config_manager.get("websocket.max_connections", 100)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get configuration: {e}")
            raise HTTPException(status_code=500, detail="Failed to get configuration")
