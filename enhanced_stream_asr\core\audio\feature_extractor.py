"""
特征提取器
基于Individual版本的特征提取功能，支持fbank等特征
"""

import logging
import numpy as np
import torch
import torchaudio
from typing import Union, Dict, Any

logger = logging.getLogger(__name__)


class FeatureExtractor:
    """音频特征提取器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化特征提取器
        
        Args:
            config: 特征提取配置
        """
        self.feat_type = config.get('feat_type', 'fbank')
        self.num_mel_bins = config.get('num_mel_bins', 80)
        self.frame_length = config.get('frame_length', 25)  # ms
        self.frame_shift = config.get('frame_shift', 10)   # ms
        self.dither = config.get('dither', 0.0)
        self.sample_rate = config.get('sample_rate', 16000)
        
        # 计算FFT参数
        self.n_fft = config.get('n_fft', 400)
        self.hop_length = config.get('hop_length', 160)
        self.win_length = int(self.sample_rate * self.frame_length / 1000)
        
        # 初始化特征提取器
        if self.feat_type == 'fbank':
            self.feature_extractor = torchaudio.transforms.MelSpectrogram(
                sample_rate=self.sample_rate,
                n_fft=self.n_fft,
                hop_length=self.hop_length,
                win_length=self.win_length,
                n_mels=self.num_mel_bins,
                f_min=0.0,
                f_max=self.sample_rate / 2,
                power=2.0,
                normalized=False
            )
        else:
            raise ValueError(f"Unsupported feature type: {self.feat_type}")
            
        logger.info(f"Feature extractor initialized: {self.feat_type}")
        
    def extract_features(self, waveform: Union[np.ndarray, torch.Tensor]) -> torch.Tensor:
        """
        提取音频特征
        
        Args:
            waveform: 音频波形数据
            
        Returns:
            torch.Tensor: 提取的特征 [T, F]
        """
        try:
            # 转换为torch tensor
            if isinstance(waveform, np.ndarray):
                waveform = torch.from_numpy(waveform).float()
            elif not isinstance(waveform, torch.Tensor):
                raise ValueError("Waveform must be numpy array or torch tensor")
                
            # 确保是浮点类型
            if waveform.dtype != torch.float32:
                waveform = waveform.float()
                
            # 确保是1维或2维
            if waveform.dim() == 1:
                waveform = waveform.unsqueeze(0)  # [1, T]
            elif waveform.dim() > 2:
                raise ValueError("Waveform must be 1D or 2D tensor")
                
            # 添加抖动（如果配置）
            if self.dither > 0:
                waveform = waveform + torch.randn_like(waveform) * self.dither
                
            # 提取特征
            if self.feat_type == 'fbank':
                features = self._extract_fbank(waveform)
            else:
                raise ValueError(f"Unsupported feature type: {self.feat_type}")
                
            return features
            
        except Exception as e:
            logger.error(f"Feature extraction failed: {e}")
            raise
            
    def _extract_fbank(self, waveform: torch.Tensor) -> torch.Tensor:
        """提取fbank特征"""
        # 计算mel频谱图
        mel_spec = self.feature_extractor(waveform)  # [1, n_mels, T]
        
        # 转换为对数域
        log_mel_spec = torch.log(mel_spec + 1e-8)
        
        # 转置为 [T, F] 格式
        features = log_mel_spec.squeeze(0).transpose(0, 1)  # [T, n_mels]
        
        return features
        
    def to_waveform(self, pcm_bytes: bytes) -> torch.Tensor:
        """
        将PCM字节数据转换为波形张量
        
        Args:
            pcm_bytes: PCM字节数据
            
        Returns:
            torch.Tensor: 波形张量
        """
        try:
            # 将bytes转换为numpy数组
            audio_array = np.frombuffer(pcm_bytes, dtype=np.int16)
            
            # 转换为浮点数并归一化
            waveform = audio_array.astype(np.float32) / 32768.0
            
            # 转换为torch tensor
            waveform = torch.from_numpy(waveform)
            
            return waveform
            
        except Exception as e:
            logger.error(f"PCM to waveform conversion failed: {e}")
            raise
            
    def get_frame_count(self, audio_length: int) -> int:
        """
        计算给定音频长度的帧数
        
        Args:
            audio_length: 音频样本数
            
        Returns:
            int: 帧数
        """
        return (audio_length - self.win_length) // self.hop_length + 1
        
    def get_config(self) -> Dict[str, Any]:
        """获取特征提取器配置"""
        return {
            'feat_type': self.feat_type,
            'num_mel_bins': self.num_mel_bins,
            'frame_length': self.frame_length,
            'frame_shift': self.frame_shift,
            'sample_rate': self.sample_rate,
            'n_fft': self.n_fft,
            'hop_length': self.hop_length,
            'win_length': self.win_length
        }
