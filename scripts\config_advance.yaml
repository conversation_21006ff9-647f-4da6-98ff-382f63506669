# ASR服务配置文件

# Triton服务器配置
asr:
  server_url: "localhost:10091"
  model_name: "model.python"
  model_repository: "./model"
  http_port: 10090
  grpc_port: 10091
  metrics_port: 10092
  log_verbose: 1
  max_batch_size: 128

# HTTP服务配置
http_service:
  host: "0.0.0.0"
  port: 10093
  max_file_size: 104857600  # 100MB
  upload_dir: "wavs/uploads"
  supported_formats: [".wav", ".mp3", ".flac", ".m4a", ".aac"]
  cors_origins: ["*"]

# WebSocket流式服务配置
stream_service:
  host: "0.0.0.0"
  port: 10094
  sample_rate: 16000
  chunk_size: 1600  # 100ms at 16kHz
  max_audio_buffer: 160000  # 10 seconds
  vad_threshold: 0.5
  silence_timeout: 2.0  # seconds
  max_sentence_silence: 450  # ms
  cleanup_interval: 30  # seconds

# 语音识别配置
asr:
  default_language: "zh"
  supported_languages: ["zh"]
  
  # VAD配置
  vad:
    enable: true
    type: "webrtcvad"
    level: 0
    frame_length: 30
    window_size: 10
    decision_threshold: 0.9
    max_speech_length: 30
    min_speech_length: 5
    merge_silence_length: 2
  
  # 特征提取配置
  feature_extraction:
    num_mel_bins: 80
    frame_shift_ms: 10
    frame_length_ms: 25
    sample_rate: 16000
  
  # 解码配置
  decoding:
    beam_size: 10
    blank_id: 0
    cutoff_prob: 0.999
    enable_hotwords: true
    enable_language_model: false
  
  # 后处理配置
  post_processing:
    enable_punctuation_prediction: true
    enable_modal_particle_filter: true
    enable_inverse_text_normalization: true
    enable_confidence: true
    enable_volume: true
    enable_words: false
    enable_intermediate_words: false
    enable_lang_label: false

# 性能监控配置
monitoring:
  enable: true
  max_history: 1000
  batch_adjustment_interval: 100
  memory_threshold_mb: 8000
  latency_threshold_seconds: 5.0
  
  # 指标收集
  metrics:
    enable_prometheus: false
    prometheus_port: 9090
    enable_logging: true
    log_interval: 60  # seconds

# 热词配置
hotwords:
  enable: true
  file_path: "model/zh/hotwords.yaml"
  max_order: 4
  window_length: 4
  space_id: -2
  character_based: true

# 敏感词过滤配置
content_filter:
  enable: false
  forbidden_words_file: "model/zh/forbidden_words.txt"
  replacement_char: "*"

# 语气词过滤配置
modal_particle_filter:
  enable: true
  file_path: "model/zh/modal_particles.txt"

# 强制替换词典配置
correction_words:
  enable: false
  file_path: "model/zh/correction_words.yaml"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "log/asr_service.log"
  max_size: "10MB"
  backup_count: 5

# 安全配置
security:
  enable_auth: false
  api_key_header: "X-API-Key"
  rate_limit:
    enable: false
    requests_per_minute: 60
    burst_size: 10

# 缓存配置
cache:
  enable: false
  type: "memory"  # memory, redis
  redis_url: "redis://localhost:6379"
  ttl: 3600  # seconds

# 数据库配置（可选）
database:
  enable: false
  url: "sqlite:///asr_service.db"
  echo: false

# 开发配置
development:
  debug: true
  reload: true
  profiling: false
