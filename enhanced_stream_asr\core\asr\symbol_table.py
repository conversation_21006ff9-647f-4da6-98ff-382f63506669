"""
符号表模块
基于Individual版本的符号表实现，支持多语种字符映射
"""

import os
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class SymbolTable:
    """符号表类，用于token ID和字符之间的转换"""
    
    def __init__(self, dict_path: str, lang_code: str = "zh"):
        """
        初始化符号表
        
        Args:
            dict_path: 词典文件路径
            lang_code: 语言代码
        """
        self.dict_path = dict_path
        self.lang_code = lang_code
        
        # 加载词典
        self.id2char_dict = self.load_dict(dict_path)  # int -> str
        self.char2id_dict = {v: k for k, v in self.id2char_dict.items()}  # str -> int
        
        # 特殊token
        self.blank_id = 0
        self.blank_symbol = '<blank>'
        self.unk_symbol = '<unk>'
        
        logger.info(f"Symbol table loaded: {len(self.id2char_dict)} tokens for language {lang_code}")
        
    def load_dict(self, path: str) -> Dict[int, str]:
        """
        加载词典文件
        
        Args:
            path: 词典文件路径
            
        Returns:
            Dict[int, str]: ID到字符的映射
        """
        logger.info(f"Loading dictionary: {path}")
        
        if not os.path.exists(path):
            logger.error(f"Dictionary file not found: {path}")
            # 返回一个基本的词典
            return {0: '<blank>', 1: '<unk>'}
            
        char_dict = {}
        try:
            with open(path, 'r', encoding='utf-8') as fin:
                for line_num, line in enumerate(fin, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                        
                    parts = line.split()
                    if len(parts) != 2:
                        logger.warning(f"Invalid line {line_num} in {path}: {line}")
                        continue
                        
                    char, token_id = parts[0], parts[1]
                    try:
                        token_id = int(token_id)
                        char_dict[token_id] = char
                    except ValueError:
                        logger.warning(f"Invalid token ID {token_id} at line {line_num}")
                        continue
                        
        except Exception as e:
            logger.error(f"Failed to load dictionary {path}: {e}")
            return {0: '<blank>', 1: '<unk>'}
            
        logger.info(f"Loaded {len(char_dict)} tokens from {path}")
        return char_dict
        
    def ids2tokens(self, ids: List[int]) -> List[str]:
        """
        将token ID列表转换为字符列表
        
        Args:
            ids: token ID列表
            
        Returns:
            List[str]: 字符列表
        """
        tokens = []
        for token_id in ids:
            if token_id in self.id2char_dict:
                tokens.append(self.id2char_dict[token_id])
            else:
                tokens.append(self.unk_symbol)
                logger.debug(f"Unknown token ID: {token_id}")
        return tokens
        
    def tokens2ids(self, tokens: List[str]) -> List[int]:
        """
        将字符列表转换为token ID列表
        
        Args:
            tokens: 字符列表
            
        Returns:
            List[int]: token ID列表
        """
        ids = []
        for token in tokens:
            if token in self.char2id_dict:
                ids.append(self.char2id_dict[token])
            else:
                ids.append(self.char2id_dict.get(self.unk_symbol, 1))
                logger.debug(f"Unknown token: {token}")
        return ids
        
    def char_map(self, text: str) -> str:
        """
        根据语言进行字符映射和后处理
        
        Args:
            text: 原始文本
            
        Returns:
            str: 处理后的文本
        """
        if not text:
            return text
            
        # 英语和俄语处理
        if self.lang_code in ['en', 'ru']:
            text = text.replace('▁', ' ')  # SentencePiece的空格标记
            text = text.replace('  ', ' ')  # 去除多余空格
            text = text.strip()
            
        # 中文处理
        elif self.lang_code in ['zh', 'zh-cn', 'zh-tw']:
            text = text.replace('<blank>', ' ')
            text = text.replace('  ', ' ')
            text = text.strip()
            
        # 其他语言的通用处理
        else:
            text = text.replace('▁', ' ')
            text = text.replace('<blank>', ' ')
            text = text.replace('  ', ' ')
            text = text.strip()
            
        return text
        
    def get_vocab_size(self) -> int:
        """获取词汇表大小"""
        return len(self.id2char_dict)
        
    def get_blank_id(self) -> int:
        """获取空白token的ID"""
        return self.blank_id
        
    def get_unk_id(self) -> int:
        """获取未知token的ID"""
        return self.char2id_dict.get(self.unk_symbol, 1)
        
    def contains_token(self, token: str) -> bool:
        """检查是否包含指定token"""
        return token in self.char2id_dict
        
    def contains_id(self, token_id: int) -> bool:
        """检查是否包含指定token ID"""
        return token_id in self.id2char_dict
