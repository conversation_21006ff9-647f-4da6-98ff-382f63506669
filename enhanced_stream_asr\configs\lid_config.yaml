# 语种识别(LID)配置

# 模型配置
model:
  model_path: "models/lid/lid_model.onnx"
  device: "cpu"
  quantized: true

# 检测配置
detection:
  min_audio_duration: 0.4      # 最小音频时长（秒）
  max_audio_duration: 2.4      # 最大音频时长（秒）
  confidence_threshold: 0.8    # 置信度阈值
  
  # 渐进式检测步骤（秒）
  progressive_steps: [0.4, 0.8, 1.2, 1.6, 2.0, 2.4]
  
  # 检测策略
  strategy: "progressive"       # progressive, single, adaptive
  max_attempts: 6              # 最大尝试次数
  early_stop_threshold: 0.95   # 早停阈值

# 语种配置
languages:
  supported: ["zh", "en", "ru", "ug", "kk"]
  fallback: "zh"               # 默认语种
  
  # 语种映射（模型输出 -> 语种代码）
  mapping:
    "chinese": "zh"
    "english": "en"
    "russian": "ru"
    "uyghur": "ug"
    "kazakh": "kk"

# VAD集成
vad:
  enable: true                 # 启用VAD预处理
  min_speech_duration: 0.2     # 最小有效语音时长（秒）
  speech_threshold: 0.5        # 语音检测阈值

# 性能配置
performance:
  batch_size: 1                # 批处理大小
  max_concurrent: 10           # 最大并发数
  cache_size: 100              # 缓存大小

# 调试配置
debug:
  enable_logging: true         # 启用详细日志
  save_audio: false           # 保存音频文件（调试用）
  log_predictions: true       # 记录预测结果

model:
  model_path: "models/lid/lid_model.onnx"

language_mapping: {0: "zh", 1: "en", 2: "ru", 3: "ug", 4: "kk"}
  
languages: ["zh", "en", "ru", "ug", "kk"]