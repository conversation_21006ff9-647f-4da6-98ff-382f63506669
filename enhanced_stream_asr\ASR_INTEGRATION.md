# ASR引擎集成文档

## 概述

本文档描述了Enhanced Stream ASR系统中ASR引擎的集成实现。基于Individual版本的ASR实现，我们创建了一个完整的流式ASR引擎，支持实时语音识别。

## 架构设计

### 核心组件

1. **StreamingASREngine** - 主要的流式ASR引擎
2. **SymbolTable** - 符号表管理，处理token ID和字符的转换
3. **FeaturePipeline** - 特征提取管道，支持多种特征类型
4. **PooledONNXEngine** - ONNX模型推理引擎（使用会话池）

### 文件结构

```
enhanced_stream_asr/core/asr/
├── __init__.py                 # 模块导出
├── decoder.py                  # 原有的解码器接口
├── multi_lang_asr.py          # 多语言ASR管理
├── text_processor.py          # 文本后处理
├── streaming_asr_engine.py    # 新增：流式ASR引擎
├── symbol_table.py            # 新增：符号表管理
└── feature_pipeline.py        # 新增：特征提取管道
```

## 主要特性

### 1. 流式处理
- 支持实时音频块处理
- 维护编码器状态缓存
- CTC前缀束搜索解码

### 2. 多语言支持
- 基于配置的语言模型加载
- 语言特定的字符映射和后处理
- 支持热词增强（中文）

### 3. 高性能
- ONNX模型推理
- 会话池管理
- 异步处理

### 4. 可配置性
- 灵活的模型参数配置
- 特征提取参数可调
- 设备选择（CPU/GPU/NPU）

## 配置说明

### 语言配置文件

每种语言都有独立的配置文件，位于 `configs/lang_configs/` 目录：

```yaml
# 中文配置示例 (zh.yaml)
code: "zh"
name: "中文"

model:
  model_path: "models/zh"              # 模型目录
  dict_path: "models/zh/units.txt"     # 词典文件
  hotwords_path: "models/zh/hotwords.txt"  # 热词文件
  
  # 解码参数
  chunk_size: 16
  left_chunks: 16
  decoding_window: 67
  subsampling_rate: 4
  right_context: 7
  
  # 设备设置
  device: "cpu"
  device_id: 0
  quantized: true
  
  # 模型结构参数
  num_blocks: 12
  head: 8
  output_size: 512
  cnn_module_kernel: 15

performance:
  blank_interval: 0.5          # 空白间隔（秒）
```

### 模型文件结构

```
models/
├── zh/                      # 中文模型
│   ├── encoder.onnx        # 编码器模型
│   ├── ctc.onnx           # CTC模型
│   ├── decoder.onnx       # 解码器模型（可选）
│   ├── units.txt          # 词典文件
│   └── hotwords.txt       # 热词文件
└── en/                      # 英文模型
    ├── encoder.onnx
    ├── ctc.onnx
    ├── units.txt
    └── hotwords.txt
```

## 使用方法

### 1. 基本使用

```python
from core.asr import StreamingASREngine

# 创建配置
config = {
    'language': 'zh',
    'model_path': 'models/zh',
    'dict_path': 'models/zh/units.txt',
    'device': 'cpu',
    # ... 其他配置
}

# 创建引擎
engine = StreamingASREngine('models/zh', config)

# 初始化
await engine.initialize()

# 处理音频块
result = await engine.process_audio_chunk(audio_data, is_final=False)
print(result['text'])

# 完成识别
final_result = await engine.finalize()

# 清理资源
await engine.cleanup()
```

### 2. 在SessionManager中的集成

ASR引擎已经集成到SessionManager中，会在语言检测完成后自动初始化：

```python
# SessionManager会自动：
# 1. 根据检测到的语言加载配置
# 2. 创建StreamingASREngine实例
# 3. 初始化引擎
# 4. 在音频处理中调用引擎
# 5. 在会话结束时清理资源
```

## API接口

### StreamingASREngine

#### 主要方法

- `__init__(model_path, config)` - 初始化引擎
- `initialize()` - 异步初始化，加载模型和资源
- `process_audio_chunk(audio_data, is_final)` - 处理音频块
- `finalize()` - 完成识别，返回最终结果
- `cleanup()` - 清理资源
- `is_ready()` - 检查引擎是否就绪

#### 返回格式

```python
{
    "text": "识别的文本",
    "language": "zh",
    "confidence": 0.95,
    "is_final": False,
    "processing_time": 45.2,
    "success": True
}
```

### SymbolTable

#### 主要方法

- `ids2tokens(ids)` - ID列表转换为token列表
- `tokens2ids(tokens)` - token列表转换为ID列表
- `char_map(text)` - 字符映射和后处理

### FeaturePipeline

#### 主要方法

- `extract_features(audio_data)` - 提取音频特征
- `to_waveform(pcm_bytes)` - PCM字节转换为波形张量

## 测试

运行集成测试：

```bash
cd enhanced_stream_asr
python test_asr_integration.py
```

测试包括：
- 符号表功能测试
- 特征提取测试
- 配置加载测试
- ASR引擎基础测试

## 性能优化

### 1. 会话池
- 使用PooledONNXEngine管理ONNX会话
- 支持动态扩缩容
- 会话复用减少初始化开销

### 2. 内存管理
- 及时清理缓存
- 避免内存泄漏
- 合理的缓存大小设置

### 3. 并发处理
- 异步处理音频块
- 线程安全的状态管理
- 非阻塞的推理调用

## 故障排除

### 常见问题

1. **模型文件未找到**
   - 检查模型路径配置
   - 确保模型文件存在

2. **初始化失败**
   - 检查ONNX Runtime安装
   - 验证模型文件完整性
   - 检查设备配置

3. **推理错误**
   - 检查输入数据格式
   - 验证模型兼容性
   - 查看详细错误日志

### 日志调试

启用详细日志：

```python
import logging
logging.getLogger('enhanced_stream_asr').setLevel(logging.DEBUG)
```

## 扩展开发

### 添加新语言支持

1. 创建语言配置文件 `configs/lang_configs/{lang}.yaml`
2. 准备模型文件到 `models/{lang}/`
3. 更新LID模型支持新语言
4. 测试验证

### 自定义特征提取

继承FeaturePipeline类并实现自定义特征提取方法：

```python
class CustomFeaturePipeline(FeaturePipeline):
    def compute_custom_features(self, waveform, sample_rate):
        # 实现自定义特征提取
        pass
```

## 注意事项

1. **模型兼容性** - 确保ONNX模型与当前版本兼容
2. **内存使用** - 监控内存使用，特别是在高并发场景
3. **性能调优** - 根据硬件配置调整参数
4. **错误处理** - 实现完善的错误处理和恢复机制
