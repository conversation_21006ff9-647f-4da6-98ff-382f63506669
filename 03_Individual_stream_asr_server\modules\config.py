#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  config.py
# Time    :  2025/04/17 14:20:35
# Author  :  lh
# Version :  1.0
# Description:  

import yaml, argparse, os, sys
from typing import Dict
from modules.logger import logger

def read_params(lang_code):
    """
    sys.argv[0]: start server script, example '/ws/app/app.py'
    lang code, example 'zh'
    """
    params_file = os.path.dirname(os.path.abspath(sys.argv[0])) + f"/server_config_{lang_code}.yaml"
    logger.info(f"加载配置: {params_file}")
    with open(params_file, 'r') as f:
        params = yaml.safe_load(f)
    return params

def parse_args():
    parser = argparse.ArgumentParser(description="online asr service")
    parser.add_argument("--onnx_dir", type=str, default="/ws/zh_model/onnx_cpu_stream_250403_v3.1", #required=True, 
                        help="Path to directory containing ONNX models (encoder.onnx, ctc.onnx, decoder.onnx)")
    parser.add_argument('--chunk_size',
                        default=16, #required=True,
                        type=int,
                        help='decoding chunk size')
    parser.add_argument('--num_left_chunks',
                        default=16, #required=True,
                        type=int,
                        help='cache chunks')
    parser.add_argument('--reverse_weight',
                        default=0.5,
                        type=float,
                        help='reverse_weight in attention_rescoing')
    parser.add_argument("--onnx_config", type=str, default="",
                        help="YAML configuration file for decoding parameters")
    parser.add_argument('--context_list_path', type=str, default='',
                        help='Context list path')
    parser.add_argument('--context_graph_score', type=float, default=40,
                        help='''The higher the score, the greater the degree of
                                bias using decoding-graph for biasing''')
    parser.add_argument("--feat_type", type=str, default="fbank",
                        help="feat_type(default fbank) in YAML configuration file")
    parser.add_argument('--fp16', action='store_true', 
                        help="Use FP16 ONNX models instead of FP32 (default: False)")
    parser.add_argument('--quant', action='store_true', 
                        help="Use quant ONNX models instead of FP32 (default: False)")
    parser.add_argument('--device',
                        type=str,
                        choices=['cpu', 'gpu', 'npu'],
                        default='cpu',
                        help='if set `--device gpu`, `--device_id <id>` should be setted too, default=0')
    parser.add_argument('--device_id', type=int, default=0, help='test data file')
    parser.add_argument('--mode', 
                        choices=['ctc_greedy_search', 'ctc_prefix_beam_search', 'attention_rescoring'],
                        default='ctc_prefix_beam_search',
                        help='decoding mode')
    parser.add_argument('lang_code', choices=['zh', 'en', 'ru', 'kk', 'kkin', 'ug'])
    parser.add_argument('port', type=int, default=11222)
    parser.add_argument('args', nargs='*', help='可变参数列表')

    args = parser.parse_args()
    
    # update by server_config_$lang.yaml
    params = read_params(args.lang_code)
    for key, value in params.items():
        setattr(args, key, value)
    
    args.dict_path = os.path.join(args.onnx_dir, 'units.txt')
    assert os.path.exists(args.dict_path)
    if not args.onnx_config:
        args.onnx_config = os.path.join(args.onnx_dir, 'train.yaml')
    if not args.context_list_path:
        args.context_list_path = os.path.join(args.onnx_dir, 'hotwords.txt')

    return args

class ConfigManager:
    def __init__(self, args, onnx_metadatas):
        self.args = args
        self.metadatas = onnx_metadatas
        self.configs = self.load_configs(args)

    def load_configs(self, args):
        with open(args.onnx_config, 'r') as f:
            configs_ = yaml.safe_load(f)

        feat_type = args.feat_type
        feat_conf = configs_['dataset_conf'][f'{feat_type}_conf']
        encoder_conf = configs_['encoder_conf']
        onnx_encoder_conf = self.metadatas['encoder']
        
        logger.debug(f"onnx_encoder_conf: {onnx_encoder_conf}")

        configs = {
            "feat_configs":{
                "feat_type": feat_type,
                "num_mel_bins": feat_conf['num_mel_bins'],
                "frame_length": feat_conf['frame_length'],
                "frame_shift": feat_conf['frame_shift'],
                "dither": feat_conf['dither'],
                "n_fft": 400,
                "hop_length": 160,
            },
            'batch' : int(onnx_encoder_conf.get("batch", 1)),
            'chunk_size' : int(onnx_encoder_conf.get('chunk_size', args.chunk_size)),
            'left_chunks' : int(onnx_encoder_conf.get('left_chunks', args.num_left_chunks)),
            'reverse_weight' : int(onnx_encoder_conf.get('left_chunks', args.reverse_weight)),
            'output_size' : int(onnx_encoder_conf.get('output_size', encoder_conf['output_size'])),
            'num_blocks' : int(encoder_conf.get('num_blocks', encoder_conf['num_blocks'])),
            'cnn_module_kernel' : int(onnx_encoder_conf.get('cnn_module_kernel', encoder_conf['cnn_module_kernel'])),
            'head' : int(onnx_encoder_conf.get('head', encoder_conf['attention_heads'])),
            'feature_size' : int(onnx_encoder_conf.get('feature_size', configs_['input_dim'])),
            'vocab_size' : int(onnx_encoder_conf.get('vocab_size', configs_['output_dim'])),
            'decoding_window' : int(onnx_encoder_conf.get("decoding_window", 67)),
            'subsampling_rate' : int(onnx_encoder_conf.get("subsampling_rate", 4)),
            'right_context' : int(onnx_encoder_conf.get("right_context", 7)),
            'context_list_path': args.context_list_path,
            'context_graph_score': args.context_graph_score

        }
        logger.debug(f"configs: {configs}")

        return configs
