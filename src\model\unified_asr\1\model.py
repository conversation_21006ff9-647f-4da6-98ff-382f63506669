import sys
sys.path.append('/ws/src/utils')
import triton_python_backend_utils as pb_utils
import numpy as np
import torch
import json
import time
import threading
import os
import asyncio
import logging
import multiprocessing
import psutil
import gc
from collections import deque
from datetime import datetime
from torch.utils.dlpack import from_dlpack, to_dlpack
from typing import List, Dict, Optional, Union, Any, Tuple

# Feature extraction
# TODO解答: `torchaudio.compliance.kaldi` vs `kaldifeat` 选择建议：
# 1. torchaudio.compliance.kaldi:
#    - 优点：纯PyTorch实现，与PyTorch生态集成好，支持GPU加速（需要CUDA版本）
#    - 缺点：GPU加速在某些版本中不稳定，依赖PyTorch版本
#    - 适用场景：实验研究、PyTorch环境、需要梯度计算的场景
# 2. kaldifeat:
#    - 优点：基于Kaldi C++实现，性能稳定，CPU优化好，生产环境验证充分
#    - 缺点：不支持梯度计算，GPU支持有限
#    - 适用场景：生产环境、推理阶段、对稳定性要求高的场景
# 建议：生产环境推荐kaldifeat，实验环境可选择torchaudio
import torchaudio.compliance.kaldi as kaldi  # 适合实验研究和纯pytorch环境，GPU加速，当前版本GPU加速待实现
import kaldifeat   # 适合生产环境，更稳定，推荐用于推理

# ONNX Runtime
import onnxruntime as ort

# CTC Decoding
from swig_decoders import ctc_beam_search_decoder_batch, \
    Scorer, HotWordsScorer, PathTrie, TrieVector, map_batch

# Tools
from vad_utils import SpeechVadFrontend
from model_repo_utils import set_logger_dir, create_logger
from verify_license import verify_license

# import debugpy
# debugpy.listen(("0.0.0.0", 5678))
# print("Waiting for debugger attach ..")

if verify_license():
    pass
else:
    raise EnvironmentError()
    exit(-1)

# Performance monitoring
class PerformanceMonitor:
    """性能监控类"""
    def __init__(self, max_history=1000):
        self.max_history = max_history
        self.inference_times = deque(maxlen=max_history)
        self.memory_usage = deque(maxlen=max_history)
        self.batch_sizes = deque(maxlen=max_history)
        self.error_count = 0
        self.total_requests = 0
        self.start_time = time.time()

    def record_inference(self, inference_time: float, batch_size: int, memory_mb: float) -> None:
        """
        记录推理性能数据

        Args:
            inference_time (float): 推理耗时，单位秒
            batch_size (int): 批次大小
            memory_mb (float): 内存使用量，单位MB
        """
        self.inference_times.append(inference_time)
        self.batch_sizes.append(batch_size)
        self.memory_usage.append(memory_mb)
        self.total_requests += 1

    def record_error(self) -> None:
        """记录错误次数"""
        self.error_count += 1

    def get_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, float]: 包含各项性能指标的字典，包括：
                - avg_inference_time: 平均推理时间
                - p95_inference_time: 95分位推理时间
                - p99_inference_time: 99分位推理时间
                - avg_memory_usage: 平均内存使用量
                - max_memory_usage: 最大内存使用量
                - avg_batch_size: 平均批次大小
                - total_requests: 总请求数
                - error_rate: 错误率
                - uptime_seconds: 运行时间
        """
        if not self.inference_times:
            return {}

        return {
            'avg_inference_time': np.mean(list(self.inference_times)),
            'p95_inference_time': np.percentile(list(self.inference_times), 95),
            'p99_inference_time': np.percentile(list(self.inference_times), 99),
            'avg_memory_usage': np.mean(list(self.memory_usage)),
            'max_memory_usage': np.max(list(self.memory_usage)),
            'avg_batch_size': np.mean(list(self.batch_sizes)),
            'total_requests': self.total_requests,
            'error_rate': self.error_count / max(self.total_requests, 1),
            'uptime_seconds': time.time() - self.start_time
        }

    def should_adjust_batch_size(self, current_batch_size: int) -> int:
        """
        根据性能指标建议是否调整batch size

        Args:
            current_batch_size (int): 当前批次大小

        Returns:
            int: 建议的批次大小
        """
        if len(self.inference_times) < 10:
            return current_batch_size

        avg_time = np.mean(list(self.inference_times)[-10:])
        avg_memory = np.mean(list(self.memory_usage)[-10:])

        # 如果推理时间过长或内存使用过高，建议减小batch size
        if avg_time > 5.0 or avg_memory > 8000:  # 5秒或8GB
            return max(1, current_batch_size // 2)
        # 如果性能良好，可以适当增加batch size
        elif avg_time < 1.0 and avg_memory < 4000:  # 1秒且4GB以下
            return min(128, current_batch_size * 2)

        return current_batch_size

class VolumeCalculator:
    """音量计算器"""
    def __init__(self) -> None:
        pass

    def calculate_volume(self, audio_data: np.ndarray) -> int:
        """
        计算音频音量 (0-100)

        Args:
            audio_data (np.ndarray): 音频数据数组

        Returns:
            int: 音量值，范围0-100
        """
        if len(audio_data) == 0:
            return 0

        # 计算RMS音量
        rms = np.sqrt(np.mean(audio_data ** 2))
        # 转换为0-100范围
        volume = min(100, int(rms * 1000))
        return volume

class ConfidenceCalculator:
    """置信度计算器"""
    def __init__(self) -> None:
        pass

    def calculate_confidence(self, log_probs: np.ndarray, text_length: int) -> float:
        """
        计算识别结果置信度

        Args:
            log_probs (np.ndarray): 对数概率数组
            text_length (int): 文本长度

        Returns:
            float: 置信度值，范围0.0-1.0
        """
        if len(log_probs) == 0 or text_length == 0:
            return 0.0

        # 简单的置信度计算：基于平均log概率
        avg_log_prob = np.mean(log_probs)
        confidence = min(1.0, max(0.0, (avg_log_prob + 10) / 10))  # 归一化到0-1
        return confidence

class Fbank(torch.nn.Module):
    """
    基于torchaudio.compliance.kaldi的Fbank特征提取器
    """
    def __init__(self, sample_rate: int, num_mel_bins: int, frame_length: int, frame_shift: int, dither: float) -> None:
        """
        初始化Fbank特征提取器

        Args:
            sample_rate (int): 采样率
            num_mel_bins (int): Mel滤波器组数量
            frame_length (int): 帧长度(ms)
            frame_shift (int): 帧移(ms)
            dither (float): 抖动系数
        """
        super(Fbank, self).__init__()
        self.num_mel_bins = num_mel_bins
        self.frame_length = frame_length
        self.frame_shift = frame_shift
        self.dither = dither
        self.sample_rate = sample_rate

    def forward(self, waveform: torch.Tensor) -> torch.Tensor:
        """
        前向传播，提取Fbank特征

        Args:
            waveform (torch.Tensor): 输入波形

        Returns:
            torch.Tensor: Fbank特征
        """
        feat = kaldi.fbank(waveform,
                      num_mel_bins=self.num_mel_bins,
                      frame_length=self.frame_length,
                      frame_shift=self.frame_shift,
                      dither=self.dither,
                      energy_floor=0.0,
                      sample_frequency=self.sample_rate)
        return feat

class Fbank_kaldifeat(torch.nn.Module):
    """
    基于kaldifeat的Fbank特征提取器
    """
    def __init__(self, opts: Any) -> None:
        """
        初始化kaldifeat Fbank特征提取器

        Args:
            opts (Any): kaldifeat的FbankOptions配置
        """
        super(Fbank_kaldifeat, self).__init__()
        self.fbank = kaldifeat.Fbank(opts)

    def forward(self, waves: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        前向传播，提取Fbank特征

        Args:
            waves (List[torch.Tensor]): 输入波形列表

        Returns:
            List[torch.Tensor]: Fbank特征列表
        """
        feats = self.fbank(waves)
        return feats

class TritonPythonModel:
    """Unified ASR model that integrates all processing steps"""

    def initialize(self, args: Dict[str, Any]) -> None:
        """
        初始化统一ASR模型

        Args:
            args (Dict[str, Any]): 初始化参数字典，包含model_config等
        """
        self.model_config = model_config = json.loads(args['model_config'])
        self.max_batch_size = max(model_config["max_batch_size"], 1)

        # Parse parameters
        params = self.model_config['parameters']
        self._parse_parameters(params)

        # Setup logging
        set_logger_dir(self.log_dir)
        self.logger = create_logger(model_name="unified_asr")
        self.logger.info('Initializing Unified ASR Model...')

        # Initialize performance monitor
        self.performance_monitor = PerformanceMonitor()

        # Model version management
        self.model_version = args.get('model_version', '1')
        self.model_name = args.get('model_name', 'unified_asr')

        # Initialize components
        if self.feat_type == "kaldifeat":
            self.logger.info('Use kaldifeat')
            self._init_feature_extractor_kaldifeat()
        else: 
            self.logger.info('Use torchaudio')
            self._init_feature_extractor()
        self._init_encoder()
        self._init_decoder()
        self._init_vad()
        self._init_advanced_features()

        # Dynamic batch size adjustment
        self.current_batch_size = self.max_batch_size
        self.batch_adjustment_interval = 100  # 每100个请求检查一次
        self.request_count = 0

        self.logger.info(f'Unified ASR Model v{self.model_version} initialized successfully!')
        self.logger.info(f'Initial batch size: {self.current_batch_size}')

    def _parse_parameters(self, params: Dict[str, Any]) -> None:
        """
        解析配置参数

        Args:
            params (Dict[str, Any]): 配置参数字典
        """
        # VAD parameters
        self.vad_enable = int(params.get('vad_enable', {'string_value': '1'})['string_value'])
        self.vad_conf = {
            'vad_type': params.get('vad_type', {'string_value': 'webrtcvad'})['string_value'],
            'vad_level': int(params.get('vad_level', {'string_value': '0'})['string_value']),
            'frame_length': int(params.get('vad_frame_len', {'string_value': '30'})['string_value']),
            'window_size': int(params.get('vad_window_size', {'string_value': '10'})['string_value']),
            'seg_thres': float(params.get('vad_decision_thres', {'string_value': '0.9'})['string_value']),
            'max_speech_len': int(params.get('vad_max_speech_len', {'string_value': '30'})['string_value']),
            'min_speech_len': int(params.get('vad_min_speech_len', {'string_value': '5'})['string_value']),
            'merge_sil_thres': int(params.get('vad_merge_sil_len', {'string_value': '2'})['string_value'])
        }
        
        # Feature extraction parameters
        self.feat_type = params.get('feat_type', {'string_value': 'torchaudio'})['string_value']
        self.sample_rate = int(params.get('sample_rate', {'string_value': '16000'})['string_value'])
        self.num_mel_bins = int(params.get('num_mel_bins', {'string_value': '80'})['string_value'])
        self.frame_shift_ms = int(params.get('frame_shift_in_ms', {'string_value': '10'})['string_value'])
        self.frame_length_ms = int(params.get('frame_length_in_ms', {'string_value': '25'})['string_value'])
        
        # Model paths
        self.encoder_model_path = params.get('encoder_model_path', {'string_value': '/ws/model_repo/encoder_zh/1/encoder.onnx'})['string_value']
        self.fp16 = int(params.get('fp16', {'string_value': '0'})['string_value'])
        
        # Decoding parameters
        self.language = params.get('language', {'string_value': 'zh'})['string_value']
        self.vocabulary_path = params.get('vocabulary', {'string_value': '/ws/res/dec/zh/units.txt'})['string_value']
        self.bidecoder = int(params.get('bidecoder', {'string_value': '1'})['string_value'])
        self.lm_path = params.get('lm_path', {'string_value': ''})['string_value']
        self.hotwords_path = params.get('hotwords_path', {'string_value': ''})['string_value']
        
        # Post-processing parameters
        self.post_enable = int(params.get('post_enable', {'string_value': '1'})['string_value'])
        self.itn_enable = int(params.get('itn_enable', {'string_value': '1'})['string_value'])
        self.enable_punctuation_prediction = int(params.get('enable_punctuation_prediction', {'string_value': '1'})['string_value'])
        self.enable_modal_particle_filter = int(params.get('enable_modal_particle_filter', {'string_value': '1'})['string_value'])

        # Advanced features parameters
        self.enable_words = int(params.get('enable_words', {'string_value': '0'})['string_value'])
        self.enable_intermediate_words = int(params.get('enable_intermediate_words', {'string_value': '0'})['string_value'])
        self.enable_confidence = int(params.get('enable_confidence', {'string_value': '1'})['string_value'])
        self.enable_volume = int(params.get('enable_volume', {'string_value': '1'})['string_value'])
        self.enable_lang_label = int(params.get('enable_lang_label', {'string_value': '0'})['string_value'])

        # Audio processing parameters
        self.gain = int(params.get('gain', {'string_value': '1'})['string_value'])   # NOTE: 振幅增益系数 调节音频音量
        self.signal_up_scale = self.gain
        self.max_sentence_silence = int(params.get('max_sentence_silence', {'string_value': '450'})['string_value'])

        # Other parameters
        # self.tmp_audio_dir = params.get('tmp_audio_dir', {'string_value': 'tmp_audios'})['string_value']  # NOTE：tools/audio_utils.py 中用到的临时文件夹，tools/ 后续重构
        self.log_dir = params.get('log_dir', {'string_value': '/ws/log/'})['string_value']
        self.log_verbose = int(params.get('log_verbose', {'string_value': '1'})['string_value'])

        # Request length slots for padding optimization
        self.req_len_slots = [5000, 10000, 20000, 30000, 60000]

    def _init_feature_extractor(self) -> None:
        """初始化基于torchaudio的特征提取器"""
        self.feature_extractor = Fbank(self.sample_rate, self.num_mel_bins, self.frame_length_ms, self.frame_shift_ms, dither=0)
        self.logger.info(f'Feature extractor initialized with {self.num_mel_bins} mel bins')

    def _init_feature_extractor_kaldifeat(self) -> None:
        """初始化基于kaldifeat的特征提取器"""
        opts = kaldifeat.FbankOptions()
        opts.frame_opts.dither = 0
        opts.mel_opts.num_bins = self.num_mel_bins
        opts.frame_opts.frame_shift_ms = self.frame_shift_ms
        opts.frame_opts.frame_length_ms = self.frame_length_ms
        opts.frame_opts.samp_freq = self.sample_rate
        
        # Set device
        if torch.cuda.is_available():
            self.device = torch.device('cuda')
        else:
            self.device = torch.device('cpu')
        opts.device = self.device

        self.feature_extractor = Fbank_kaldifeat(opts)
        self.logger.info(f'Feature extractor initialized with {self.num_mel_bins} mel bins')

    def _init_encoder(self) -> None:
        """初始化ONNX编码器模型"""
        try:
            # Setup ONNX Runtime session
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if torch.cuda.is_available() else ['CPUExecutionProvider']
            self.encoder_session = ort.InferenceSession(self.encoder_model_path, providers=providers)
            
            # Get input/output info
            self.encoder_input_names = [input.name for input in self.encoder_session.get_inputs()]
            self.encoder_output_names = [output.name for output in self.encoder_session.get_outputs()]
            
            self.logger.info(f'Encoder model loaded from {self.encoder_model_path}')
            self.logger.info(f'Encoder inputs: {self.encoder_input_names}')
            self.logger.info(f'Encoder outputs: {self.encoder_output_names}')
            
        except Exception as e:
            self.logger.error(f'Failed to load encoder model: {e}')
            raise

    def _init_decoder(self) -> None:
        """初始化CTC解码器"""
        # Load vocabulary
        self.vocabulary = self._load_vocabulary(self.vocabulary_path)
        
        # Initialize language model scorer if provided
        self.lm_scorer = None
        if self.lm_path and os.path.exists(self.lm_path):
            try:
                alpha, beta = 0.5, 0.5  # Default LM weights
                self.lm_scorer = Scorer(alpha, beta, self.lm_path, self.vocabulary)
                self.logger.info("Language model loaded successfully!")
            except Exception as e:
                self.logger.warning(f"Failed to load language model: {e}")
        
        # Initialize hotwords scorer if provided
        self.hotwords_scorer = None
        if self.hotwords_path and os.path.exists(self.hotwords_path):
            try:
                hotwords = self._load_hotwords(self.hotwords_path)
                if hotwords:
                    max_order = max(4, max(len(w) for w in hotwords))
                    self.hotwords_scorer = HotWordsScorer(
                        hotwords, self.vocabulary,
                        window_length=max_order,
                        SPACE_ID=-2,
                        is_character_based=True
                    )
                    self.logger.info(f"Hotwords loaded successfully! Max order: {max_order}")
            except Exception as e:
                self.logger.warning(f"Failed to load hotwords: {e}")
        
        # Decoder parameters
        self.beam_size = 10
        self.blank_id = 0
        self.sos = 1
        self.eos = 2
        self.cutoff_prob = 0.999
        self.num_processes = min(multiprocessing.cpu_count(), 4)
        
        self.logger.info('CTC decoder initialized')

    def _init_vad(self) -> None:
        """初始化VAD（语音活动检测）"""
        if self.vad_enable:
            self.logger.info('VAD enabled with config: {}'.format(self.vad_conf))
        else:
            self.logger.info('VAD disabled')

    def _init_advanced_features(self) -> None:
        """初始化高级功能特性"""
        # Initialize forbidden words (敏感词) if configured
        self.forbidden_words = []

        # Initialize modal particle filter (语气词过滤)
        self.modal_particles = []
        if self.enable_modal_particle_filter:
            self._load_modal_particles()

        # Initialize correction words (强制替换)
        self.correction_words = {}

        # Initialize volume calculation
        self.volume_calculator: Optional[VolumeCalculator] = VolumeCalculator() if self.enable_volume else None

        # Initialize confidence calculation
        self.confidence_calculator: Optional[ConfidenceCalculator] = ConfidenceCalculator() if self.enable_confidence else None

        self.logger.info('Advanced features initialized')

    def _load_modal_particles(self) -> None:
        """加载语气词过滤规则"""
        modal_particle_file = f"model/{self.language}/post/modal_particle.txt"
        if os.path.exists(modal_particle_file):
            try:
                with open(modal_particle_file, 'r', encoding='utf-8') as f:
                    self.modal_particles = [line.strip() for line in f if line.strip()]
                self.logger.info(f'Loaded {len(self.modal_particles)} modal particle rules')
            except Exception as e:
                self.logger.warning(f'Failed to load modal particles: {e}')
        else:
            self.logger.info('No modal particle file found')

    def _get_memory_usage(self) -> float:
        """
        获取当前内存使用量

        Returns:
            float: 内存使用量，单位MB
        """
        try:
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0

    def _load_vocabulary(self, vocab_path: str) -> List[str]:
        """
        从文件加载词汇表

        Args:
            vocab_path (str): 词汇表文件路径

        Returns:
            List[str]: 词汇表列表
        """
        vocabulary = []
        try:
            with open(vocab_path, 'r', encoding='utf-8') as f:
                for line in f:
                    vocabulary.append(line.strip().split()[0])
            self.logger.info(f'Loaded vocabulary with {len(vocabulary)} tokens')
            return vocabulary
        except Exception as e:
            self.logger.error(f'Failed to load vocabulary from {vocab_path}: {e}')
            raise

    def _load_hotwords(self, hotwords_path: str) -> List[str]:
        """
        从YAML文件加载热词

        Args:
            hotwords_path (str): 热词文件路径

        Returns:
            List[str]: 热词列表
        """
        try:
            import yaml
            with open(hotwords_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            hotwords = data.get('hotwords', [])
            self.logger.info(f'Loaded {len(hotwords)} hotwords')
            return hotwords
        except Exception as e:
            self.logger.error(f'Failed to load hotwords from {hotwords_path}: {e}')
            return []

    async def execute(self, requests: List[Any]) -> List[Any]:
        """
        执行统一ASR推理

        Args:
            requests (List[Any]): Triton推理请求列表

        Returns:
            List[Any]: Triton推理响应列表
        """
        # import pdb
        # pdb.set_trace()

        responses = []
        start_time = time.time()
        memory_start = self._get_memory_usage()

        try:
            # Update request count and check for batch size adjustment
            self.request_count += 1
            if self.request_count % self.batch_adjustment_interval == 0:
                self._adjust_batch_size()
            self.logger.info(f"Received {len(requests)} requests.")

            # Process each request
            batch_count = []
            total_waves = []
            total_langs = []

            for request in requests:
                # Get request id
                request_id = request.request_id()

                # Get input tensors
                wav_tensor = pb_utils.get_input_tensor_by_name(request, "WAV")
                lang_tensor = pb_utils.get_input_tensor_by_name(request, "LANG")

                # Extract data
                wav_data = wav_tensor.as_numpy()    # shape: (b, length)
                lang_data = lang_tensor.as_numpy()   # shape: (b, 1)

                # Handle batch
                if len(wav_data.shape) == 1:
                    wav_data = wav_data.reshape(1, -1)

                batch_size = wav_data.shape[0]
                batch_count.append(batch_size)

                # Process each sample in batch
                for i in range(batch_size):
                    audio_data = wav_data[i]
                    lang_code = str(lang_data[i][0], encoding='utf-8')

                    total_waves.append(audio_data)
                    total_langs.append(lang_code)

            # Convert audio data to tensors
            audios = []
            audio_lens = []
            audio_times = []
            sr = self.sample_rate

            for audio_data in total_waves:
                # Convert to tensor
                audio_tensor = torch.from_numpy(audio_data.astype(np.float32)).unsqueeze(0)
                audios.append(audio_tensor)

                # Calculate length in milliseconds
                audio_len_ms = len(audio_data) * 1000 // sr
                audio_lens.append(audio_len_ms)
                audio_times.append([0, audio_len_ms])

            self.logger.info(f"Processing {len(audios)} audio samples")

            # Apply VAD if enabled
            if self.vad_enable or max(audio_lens) > 30000:   # 30 seconds
                audio_extends, audio_extends_len, audio_extends_time = [], [], []
                audio_extends_lang, audio_seg_nums = [], []

                vad_frontend = SpeechVadFrontend(**self.vad_conf)
                for waveform, lang in zip(audios, total_langs):
                    segments, segment_lens, segment_times = vad_frontend.get_all_speech_segments(waveform, sr)

                    audio_extends.extend(segments)
                    audio_extends_len.extend(segment_lens)
                    audio_extends_time.extend(segment_times)
                    audio_extends_lang.extend([lang] * len(segment_lens))
                    audio_seg_nums.append(len(segment_lens))
            else:
                audio_extends, audio_extends_len, audio_extends_time = audios, audio_lens, audio_times
                audio_extends_lang = total_langs
                audio_seg_nums = [1] * len(audios)

            self.logger.info(f"After VAD: {len(audio_extends)} segments")

            # TODO解答: 方案选择建议：
            # 方案一：async execute for every audio in audio_extends（逐个异步处理）
            #   - 优点：内存占用小，适合长音频，可以流式处理
            #   - 缺点：无法充分利用GPU并行计算能力，吞吐量较低
            # 方案二：batch execute by padding（批量填充处理）
            #   - 优点：GPU利用率高，吞吐量大，适合大量短音频
            #   - 缺点：内存占用大，填充浪费计算资源，不适合长音频
            # 建议：根据音频长度动态选择：
            #   - 短音频（<10s）且数量多：使用方案二（批量处理）
            #   - 长音频（>10s）或内存受限：使用方案一（逐个处理）
            #   - 当前实现采用方案一，适合通用场景
            # Process each audio segment
            transcripts = []
            for speech, speech_len, lang in zip(audio_extends, audio_extends_len, audio_extends_lang):
                try:
                    # 1. Feature extraction
                    features = self._extract_features(speech, speech_len)

                    # 2. Encoder inference
                    encoder_out, encoder_out_lens, ctc_log_probs, beam_log_probs, beam_log_probs_idx = self._run_encoder(features)

                    # 3. CTC decoding
                    transcript = self._decode_ctc(encoder_out, encoder_out_lens, ctc_log_probs, beam_log_probs, beam_log_probs_idx)

                    # # 4. Apply advanced features
                    # transcript = self._apply_advanced_features(transcript, speech, ctc_log_probs)

                    transcripts.append(transcript)

                except Exception as e:
                    self.logger.error(f"Error processing segment: {e}")
                    transcripts.append({"result": ""})

            # Combine results
            results = []
            st = 0
            info_dict = {
                'cost_time': f"{time.time() - start_time:.4f} s",
            }

            for seg_num, waveform_dur in zip(audio_seg_nums, audio_lens):
                waveform_transcripts = transcripts[st:st+seg_num]
                waveform_timestamps = audio_extends_time[st:st+seg_num]

                result = self._make_trans_result(
                    waveform_transcripts,
                    waveform_timestamps,
                    waveform_dur,
                    info_dict
                )

                results.append(result)
                st += seg_num

            # Create responses
            st = 0
            for batch_size in batch_count:
                batch_results = results[st:st+batch_size]
                result_array = np.array([json.dumps(r, ensure_ascii=False) for r in batch_results])

                out_tensor = pb_utils.Tensor("TRANSCRIPTS", result_array.astype(np.object_))
                response = pb_utils.InferenceResponse(output_tensors=[out_tensor])
                responses.append(response)
                st += batch_size

            return responses

        except Exception as e:
            self.logger.error(f"Error in execute: {e}")
            self.performance_monitor.record_error()

            # Return error response
            error_response = pb_utils.InferenceResponse(
                error=pb_utils.TritonError(f"Inference failed: {str(e)}")
            )
            return [error_response] * len(requests)

        finally:
            # Record performance metrics
            inference_time = time.time() - start_time
            memory_end = self._get_memory_usage()
            batch_size = len(requests)

            self.performance_monitor.record_inference(
                inference_time, batch_size, memory_end
            )

            if self.log_verbose:
                self.logger.info(f"Inference completed in {inference_time:.3f}s, "
                               f"batch_size={batch_size}, memory={memory_end:.1f}MB")

    def _adjust_batch_size(self) -> None:
        """根据性能指标调整批次大小"""
        new_batch_size = self.performance_monitor.should_adjust_batch_size(self.current_batch_size)
        if new_batch_size != self.current_batch_size:
            self.logger.info(f"Adjusting batch size from {self.current_batch_size} to {new_batch_size}")
            self.current_batch_size = new_batch_size

    def _apply_advanced_features(self, transcript: Dict[str, Any], audio_data: Optional[np.ndarray], log_probs: Optional[np.ndarray]) -> Dict[str, Any]:
        """
        应用高级功能特性到转录结果

        Args:
            transcript (Dict[str, Any]): 基础转录结果
            audio_data (Optional[np.ndarray]): 音频数据，用于计算音量
            log_probs (Optional[np.ndarray]): 对数概率，用于计算置信度

        Returns:
            Dict[str, Any]: 增强后的转录结果
        """
        result = transcript.copy()
        text = result.get("result", "")

        # Apply modal particle filter
        if self.enable_modal_particle_filter and text:
            text = self._filter_modal_particles(text)

        # Calculate confidence
        if self.enable_confidence and log_probs is not None and self.confidence_calculator is not None:
            confidence = self.confidence_calculator.calculate_confidence(log_probs, len(text))
            result["confidence"] = confidence

        # Calculate volume
        if self.enable_volume and audio_data is not None and self.volume_calculator is not None:
            volume = self.volume_calculator.calculate_volume(audio_data)
            result["volume"] = volume

        # Add language label if enabled
        if self.enable_lang_label:
            result["lang"] = self.language

        # Apply correction words
        if text and self.correction_words:
            for old_word, new_word in self.correction_words.items():
                text = text.replace(old_word, new_word)

        result["result"] = text
        return result

    def _filter_modal_particles(self, text: str) -> str:
        """
        从文本中过滤语气词

        Args:
            text (str): 输入文本

        Returns:
            str: 过滤后的文本
        """
        filtered_text = text
        for particle in self.modal_particles:
            filtered_text = filtered_text.replace(particle, "")
        return filtered_text.strip()

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取当前性能统计信息

        Returns:
            Dict[str, Any]: 性能统计数据
        """
        return self.performance_monitor.get_stats()

    def _extract_features(self, waveform: torch.Tensor, speech_len_ms: int) -> Tuple[np.ndarray, int]:
        """
        从波形中提取特征

        Args:
            waveform (torch.Tensor): 输入波形
            speech_len_ms (int): 语音长度（毫秒）

        Returns:
            Tuple[np.ndarray, int]: 特征数组和特征长度
        """
        try:
            # Ensure waveform is on correct device
            if isinstance(waveform, np.ndarray):
                waveform = torch.from_numpy(waveform.astype(np.float32))

            if self.signal_up_scale:
                waveform = waveform * (1 << 15)  # 将浮点数扩大缩放为整数

            # Extract features using `kaldifeat` or `torchaudio.compliance.kaldi`
            if isinstance(self.feature_extractor, Fbank_kaldifeat):
                waveform = waveform.to(self.device)
                features = self.feature_extractor([waveform])  # return: List[torch.Tensor]
            else:
                if len(waveform.shape) == 1:
                    waveform = waveform.unsqueeze(0)
                features = self.feature_extractor(waveform)   # return: torch.Tensor  TODO：how to use GPU (torchaudio.compliance.kaldi)
            
            if isinstance(features, list):
                features = features[0].unsqueeze(0)
            else:
                features = features.unsqueeze(0)   # required shape: (bz, feature_length, n_mels_bin)  if bz > 1, should padding zeros

            # Convert to numpy for ONNX
            if isinstance(features, torch.Tensor):
                features = features.cpu().numpy()

            # Calculate feature length
            feature_len = features.shape[1]

            self.logger.debug(f"Extracted features shape: {features.shape}, speech_len: {speech_len_ms}ms")
            return features, feature_len

        except Exception as e:
            self.logger.error(f"Feature extraction failed: {e}")
            raise

    def _run_encoder(self, features: Tuple[np.ndarray, int]) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        运行ONNX编码器推理

        Args:
            features (Tuple[np.ndarray, int]): 特征数组和长度

        Returns:
            Tuple: (encoder_out, encoder_out_lens, ctc_log_probs, beam_log_probs, beam_log_probs_idx)
        """
        try:
            speech_input, feature_len = features

            # Prepare inputs for ONNX model
            if self.fp16:
                speech_input = speech_input.astype(np.float16)  # Convert to FP16
            speech_lengths = np.array([feature_len], dtype=np.int32)

            # Run inference
            inputs = {
                'speech': speech_input,        # required shape: (bz, seq_len, n_mel_bins)
                'speech_lengths': speech_lengths   # required shape: (bz, )
            }

            outputs = self.encoder_session.run(self.encoder_output_names, inputs)

            # Extract outputs
            encoder_out = outputs[0]  # encoder_out
            encoder_out_lens = outputs[1]  # encoder_out_lens
            ctc_log_probs = outputs[2]  # ctc_log_probs
            beam_log_probs = outputs[3]  # beam_log_probs
            beam_log_probs_idx = outputs[4]  # beam_log_probs_idx

            self.logger.debug(f"Encoder output shape: {encoder_out.shape}")
            return encoder_out, encoder_out_lens, ctc_log_probs, beam_log_probs, beam_log_probs_idx

        except Exception as e:
            self.logger.error(f"Encoder inference failed: {e}")
            raise

    def _decode_ctc(self, encoder_out: np.ndarray, encoder_out_lens: np.ndarray, ctc_log_probs: np.ndarray, beam_log_probs: Optional[np.ndarray] = None, beam_log_probs_idx: Optional[np.ndarray] = None) -> Dict[str, str]:
        """
        执行CTC束搜索解码

        Args:
            encoder_out (np.ndarray): 编码器输出
            encoder_out_lens (np.ndarray): 编码器输出长度
            ctc_log_probs (np.ndarray): CTC对数概率
            beam_log_probs (Optional[np.ndarray]): 束搜索对数概率
            beam_log_probs_idx (Optional[np.ndarray]): 束搜索概率索引

        Returns:
            Dict[str, str]: 解码结果，包含"result"字段
        """
        try:
            self.logger.debug(f"Decoding with encoder_out shape: {encoder_out.shape}, lens: {encoder_out_lens}")

            # Convert log probs to the format expected by decoder
            if beam_log_probs is not None:
                if len(beam_log_probs.shape) == 3:
                    # Remove batch dimension if present
                    log_probs_seq = beam_log_probs[0].tolist()
                else:
                    log_probs_seq = beam_log_probs.tolist()
            else:
                # Fallback to ctc_log_probs if beam_log_probs is None
                if len(ctc_log_probs.shape) == 3:
                    log_probs_seq = ctc_log_probs[0].tolist()
                else:
                    log_probs_seq = ctc_log_probs.tolist()

            # Create log_probs_idx (indices of top-k probabilities)
            if beam_log_probs_idx is not None:
                if len(beam_log_probs_idx.shape) == 3:
                    log_probs_idx = beam_log_probs_idx[0].tolist()
                else:
                    log_probs_idx = beam_log_probs_idx.tolist()
            else:
                # Create indices for all vocabulary
                vocab_size = len(log_probs_seq[0]) if log_probs_seq else len(self.vocabulary)
                log_probs_idx = [list(range(vocab_size)) for _ in log_probs_seq]

            # Initialize path trie
            root_trie = PathTrie()
            batch_root = TrieVector()
            batch_root.append(root_trie)

            # Perform beam search decoding
            num_processes = 1
            results = ctc_beam_search_decoder_batch(
                [log_probs_seq],  # batch of log_probs_seq
                [log_probs_idx],  # batch of log_probs_idx
                batch_root,      # batch of root tries
                [True],           # batch of start flags
                self.beam_size,
                1,  # num_processes for single sample
                blank_id=self.blank_id,
                space_id=-2,
                cutoff_prob=self.cutoff_prob,
                ext_scorer=self.lm_scorer,
                hotwords_scorer=self.hotwords_scorer
            )
            
            # Get best hypothesis
            if results and len(results) > 0 and len(results[0]) > 0:
                score, best_hyp = results[0][0]  # Ignore score for now

                # Map indices to text
                text = map_batch([list(best_hyp)], self.vocabulary, 1)[0]
                # hyps = map_batch([list(best_hyp)], self.vocabulary, num_processes, False, 0)

                # Post-processing
                if self.language in ['ar', 'id']:
                    text = text.replace("▁", " ").strip()

                # Log beam_log_probs usage if available
                if beam_log_probs is not None:
                    self.logger.debug(f"Used beam_log_probs shape: {beam_log_probs.shape}")

                return {"result": text}
            else:
                return {"result": ""}

        except Exception as e:
            self.logger.error(f"CTC decoding failed: {e}")
            return {"result": ""}

    def _make_trans_result(self, transcripts: List[Dict[str, str]], timestamps: List[List[int]], duration_ms: int, info_dict: Dict[str, str]) -> Dict[str, Any]:
        """
        创建最终转录结果

        Args:
            transcripts (List[Dict[str, str]]): 转录结果列表
            timestamps (List[List[int]]): 时间戳列表
            duration_ms (int): 音频时长（毫秒）
            info_dict (Dict[str, str]): 信息字典

        Returns:
            Dict[str, Any]: 最终转录结果
        """
        try:
            # Combine all segment transcripts
            full_text = ""
            segments = []

            for transcript, timestamp in zip(transcripts, timestamps):
                text = transcript.get("result", "")
                if text.strip():
                    if full_text:
                        full_text += " "
                    full_text += text

                    segments.append({
                        "text": text,
                        "start": timestamp[0] / 1000.0,  # Convert to seconds
                        "end": timestamp[1] / 1000.0
                    })

            result = {
                "text": full_text,
                "segments": segments,
                "duration": duration_ms / 1000.0,
                "info": info_dict
            }

            return result

        except Exception as e:
            self.logger.error(f"Failed to create result: {e}")
            return {
                "text": "",
                "segments": [],
                "duration": duration_ms / 1000.0,
                "info": info_dict
            }

    def finalize(self) -> None:
        """
        清理资源
        """
        self.logger.info('Cleaning up Unified ASR Model...')

        # Clean up ONNX session
        if hasattr(self, 'encoder_session'):
            del self.encoder_session
