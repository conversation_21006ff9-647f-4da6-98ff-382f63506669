import os, sys
from typing import Optional, Dict, Any
from loguru import logger

def get_folder_path() -> str:
    """
    Get the absolute path of the directory containing the current script.
    Returns:
        str: The absolute path of the directory where the script is located.
    """
    return os.path.dirname(os.path.abspath(sys.argv[0])) + f"/log_{sys.argv[1]}/"


def create_multi_level_log():
    """
    Configure a multi-level logging system using loguru.
    This function sets up different log files for each severity level (DEBUG, INFO, WARNING, ERROR, CRITICAL),
    with the following configuration:
        - Logs are rotated daily.
        - Old logs are retained for 30 days.
        - Logs are compressed in ZIP format.
        - Traceback and diagnose information are included.
    Returns:
        A configured loguru Logger instance.
    """
    prefix = "server-"
    rotation = "1 day"
    retention = "30 days"
    encoding = "utf-8"
    backtrace = True
    diagnose = True

    def add_log_level(level, filename):
        """
        Add a new log level with the specified configuration.
        Args:
            level (str): The severity level of the log.
            filename (str): The prefix for the log file name.
        """
        logger.add((folder + filename),
          level=level,
          backtrace=backtrace,
          diagnose=diagnose,
          rotation=rotation,
          retention=retention,
          encoding=encoding,
          compression="zip",
          filter=(lambda record: record["level"].no >= getattr(logger.level, level).no))
        add_log_level("DEBUG", "debug.{time}.log")
        add_log_level("INFO", "info.{time}.log")
        add_log_level("WARNING", "warning.{time}.log")
        add_log_level("ERROR", "error.{time}.log")
        add_log_level("CRITICAL", "critical.{time}.log")
        logger.add((sys.stderr),
          level="CRITICAL",
          backtrace=backtrace,
          diagnose=diagnose,
          filter=(lambda record: record["level"].no >= getattr(logger.level, "CRITICAL").no))
        return logger


def create_log(level: str='INFO'):
    """
    Create a loguru Logger instance with custom configuration.
    Args:
        level (str, optional): The minimum severity level to be logged. Defaults to "INFO".
    Returns:
        A configured loguru Logger instance.
    """
    prefix = f"server-{level}."
    rotation = "1 day"
    retention = "7 days"
    encoding = "utf-8"
    compression = "zip"
    backtrace = False
    diagnose = False
    format_str = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <5}</level> | <cyan>{name:7}</cyan>:<cyan>{function:12}</cyan>:<yellow>{line}</yellow> - <level>{message}</level>"
    debug_flag = False
    if len(sys.argv) == 4:
        if sys.argv[-1] == 'debug':
            debug_flag = True
    if debug_flag:
        pass
    else:
        logger.remove()   # 不在终端打印日志 

    logger.add((folder + prefix + "{time}.log"),
      format=format_str,
      rotation=rotation,
      retention=retention,
      compression=compression,
      encoding=encoding,
      level=level,
      backtrace=backtrace,
      diagnose=diagnose)
    return logger


folder = get_folder_path()
logger = create_log(level="INFO")

# okay decompiling /ws/online_asr_server_private/server/__pycache__/logger.cpython-38.pyc
