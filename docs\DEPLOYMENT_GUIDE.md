# ASR服务部署指南

## 概述

本指南将帮助您部署完整的ASR（自动语音识别）服务，包括：
- 统一的Triton Python后端
- HTTP文件转写API服务
- WebSocket实时识别服务
- 性能监控和管理功能

## 系统要求

### 硬件要求
- **CPU**: 8核心以上，推荐16核心
- **内存**: 16GB以上，推荐32GB
- **GPU**: NVIDIA GPU（可选，推荐用于生产环境）
  - 显存: 8GB以上
  - CUDA版本: 11.8+
- **存储**: 50GB以上可用空间

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows 10+
- **Python**: 3.8+
- **Docker**: 20.10+ (可选)
- **NVIDIA Docker**: 2.0+ (GPU环境)

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd ASRServer

# 创建Python虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 模型准备

```bash
# 确保模型文件存在
ls -la model/zh/unified_asr/
# 应该包含:
# - config.pbtxt
# - 1/model.py
# - 相关的ONNX模型文件和词汇表

# 检查工具目录
ls -la tools/
# 应该包含:
# - audio_utils.py
# - vad_utils.py
# - model_repo_utils.py
```

### 3. 启动服务

#### 方式一：使用服务管理器（推荐）
```bash
# 启动所有服务
python start_services.py

# 服务将在以下端口启动:
# - Triton Server: 8000 (HTTP), 8001 (gRPC), 8002 (Metrics)
# - HTTP API: 8080
# - WebSocket API: 8081
```

#### 方式二：手动启动
```bash
# 终端1: 启动Triton服务器
tritonserver --model-repository=./model --http-port=8000 --grpc-port=8001

# 终端2: 启动HTTP服务
uvicorn app:app --host 0.0.0.0 --port 8080

# 终端3: 启动WebSocket服务
uvicorn app_stream:app --host 0.0.0.0 --port 8081
```

### 4. 验证部署

```bash
# 运行测试脚本
python test_services.py

# 或者手动检查
curl http://localhost:8080/health
curl http://localhost:8081/health
curl http://localhost:8000/v2/health/ready
```

## 详细配置

### 1. 配置文件说明

编辑 `config.yaml` 文件来自定义配置：

```yaml
# Triton服务器配置
triton:
  server_url: "localhost:8000"
  model_name: "unified_asr"
  max_batch_size: 128

# HTTP服务配置
http_service:
  host: "0.0.0.0"
  port: 8080
  max_file_size: 104857600  # 100MB

# WebSocket服务配置
stream_service:
  host: "0.0.0.0"
  port: 8081
  sample_rate: 16000
```

### 2. 环境变量

```bash
# 设置环境变量
export TRITON_SERVER_URL="localhost:8000"
export MODEL_REPOSITORY="./model"
export UPLOAD_DIR="./uploads"
export LOG_LEVEL="INFO"
```

### 3. 模型配置调优

编辑 `model/zh/unified_asr/config.pbtxt`：

```protobuf
# 调整批处理大小
max_batch_size: 64  # 根据GPU内存调整

# 调整实例数量
instance_group [
    {
      count: 2  # 增加实例数提高并发
      kind: KIND_GPU
    }
]
```

## 生产环境部署

### 1. Docker部署

创建 `Dockerfile`：
```dockerfile
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3 python3-pip \
    ffmpeg libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . /app
WORKDIR /app

# 创建必要目录
RUN mkdir -p uploads logs

# 暴露端口
EXPOSE 8000 8080 8081

# 启动服务
CMD ["python3", "start_services.py"]
```

构建和运行：
```bash
# 构建镜像
docker build -t asr-service:latest .

# 运行容器
docker run -d \
  --name asr-service \
  --gpus all \
  -p 8000:8000 \
  -p 8080:8080 \
  -p 8081:8081 \
  -v $(pwd)/model:/app/model \
  -v $(pwd)/uploads:/app/uploads \
  asr-service:latest
```

### 2. Kubernetes部署

创建 `k8s-deployment.yaml`：
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: asr-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: asr-service
  template:
    metadata:
      labels:
        app: asr-service
    spec:
      containers:
      - name: asr-service
        image: asr-service:latest
        ports:
        - containerPort: 8080
        - containerPort: 8081
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
            nvidia.com/gpu: 1
          limits:
            memory: "8Gi"
            cpu: "4"
            nvidia.com/gpu: 1
        env:
        - name: TRITON_SERVER_URL
          value: "localhost:8000"
        volumeMounts:
        - name: model-storage
          mountPath: /app/model
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: model-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: asr-service
spec:
  selector:
    app: asr-service
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: websocket
    port: 8081
    targetPort: 8081
  type: LoadBalancer
```

部署到Kubernetes：
```bash
kubectl apply -f k8s-deployment.yaml
```

### 3. 负载均衡配置

使用Nginx作为反向代理：
```nginx
upstream asr_http {
    server 127.0.0.1:8080;
    server 127.0.0.1:8180;  # 多个实例
    server 127.0.0.1:8280;
}

upstream asr_websocket {
    server 127.0.0.1:8081;
    server 127.0.0.1:8181;
    server 127.0.0.1:8281;
}

server {
    listen 80;
    server_name asr.example.com;

    # HTTP API
    location /api/ {
        proxy_pass http://asr_http/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # WebSocket
    location /ws/ {
        proxy_pass http://asr_websocket/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

## 监控和维护

### 1. 性能监控

```bash
# 查看服务状态
curl http://localhost:8080/health
curl http://localhost:8081/health

# 查看活跃会话
curl http://localhost:8081/sessions

# 查看任务列表
curl http://localhost:8080/transcribe/tasks
```

### 2. 日志管理

```bash
# 查看服务日志
tail -f logs/asr_service.log

# 查看Triton日志
docker logs triton-server

# 日志轮转配置
logrotate -d /etc/logrotate.d/asr-service
```

### 3. 性能调优

#### GPU内存优化
```python
# 在模型配置中调整
max_batch_size: 32  # 减少批处理大小
instance_group [
    {
      count: 1  # 减少实例数量
      kind: KIND_GPU
    }
]
```

#### 并发优化
```python
# 在应用配置中调整
uvicorn app:app --workers 4 --worker-class uvicorn.workers.UvicornWorker
```

## 故障排除

### 常见问题

1. **Triton模型加载失败**
   ```bash
   # 检查模型文件
   ls -la model/zh/unified_asr/1/
   
   # 检查配置文件语法
   tritonserver --model-repository=./model --exit-on-error
   ```

2. **GPU内存不足**
   ```bash
   # 监控GPU使用
   nvidia-smi
   
   # 减少批处理大小
   # 编辑 model/zh/unified_asr/config.pbtxt
   max_batch_size: 16
   ```

3. **音频格式不支持**
   ```bash
   # 安装额外的音频编解码器
   apt-get install ffmpeg libavcodec-extra
   ```

4. **WebSocket连接失败**
   ```bash
   # 检查防火墙设置
   ufw allow 8081
   
   # 检查代理配置
   # 确保支持WebSocket升级
   ```

### 调试模式

```bash
# 启用调试模式
export LOG_LEVEL=DEBUG
python start_services.py

# 或者单独启动服务进行调试
python -m uvicorn app:app --reload --log-level debug
```

## API使用示例

### HTTP API示例

```python
import requests

# 文件上传转写
with open('audio.wav', 'rb') as f:
    files = {'file': f}
    data = {'language': 'zh', 'enable_confidence': True}
    response = requests.post('http://localhost:8080/transcribe/file', 
                           files=files, data=data)
    task_id = response.json()['task_id']

# 查询结果
result = requests.get(f'http://localhost:8080/transcribe/result/{task_id}')
print(result.json())
```

### WebSocket API示例

```python
import asyncio
import websockets
import json

async def stream_recognition():
    uri = "ws://localhost:8081/ws/stream?language=zh"
    
    async with websockets.connect(uri) as websocket:
        # 发送音频数据
        with open('audio.wav', 'rb') as f:
            audio_data = f.read()
            await websocket.send(audio_data)
        
        # 接收结果
        async for message in websocket:
            result = json.loads(message)
            print(f"识别结果: {result.get('text', '')}")

asyncio.run(stream_recognition())
```

## 安全建议

1. **API密钥认证**：在生产环境中启用API密钥认证
2. **HTTPS/WSS**：使用SSL/TLS加密传输
3. **速率限制**：配置适当的速率限制防止滥用
4. **输入验证**：严格验证上传的音频文件
5. **日志审计**：记录所有API调用和用户操作

## 扩展和定制

1. **添加新语言**：扩展模型支持更多语言
2. **自定义后处理**：实现特定领域的后处理逻辑
3. **集成外部服务**：连接翻译、情感分析等服务
4. **移动端SDK**：开发移动应用SDK

通过本指南，您应该能够成功部署和运行ASR服务。如有问题，请查看日志文件或联系技术支持。
