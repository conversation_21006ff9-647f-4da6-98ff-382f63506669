"""
ONNX Session会话池管理
实现动态扩缩容的ONNX session池，支持高并发请求处理
"""

import asyncio
import logging
import threading
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import queue
import onnxruntime as ort
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class SessionState(Enum):
    """Session状态枚举"""
    IDLE = "idle"
    BUSY = "busy"
    INITIALIZING = "initializing"
    ERROR = "error"
    TERMINATING = "terminating"


@dataclass
class SessionInfo:
    """Session信息"""
    session_id: str
    session: ort.InferenceSession
    state: SessionState
    created_time: float
    last_used_time: float
    usage_count: int = 0
    error_count: int = 0
    
    def update_usage(self):
        """更新使用信息"""
        self.last_used_time = time.time()
        self.usage_count += 1
        
    def mark_error(self):
        """标记错误"""
        self.error_count += 1
        self.state = SessionState.ERROR


class ONNXSessionPool:
    """ONNX Session池"""
    
    def __init__(
        self,
        model_path: str,
        providers: List[str],
        min_sessions: int = 1,
        max_sessions: int = 10,
        session_timeout: float = 300.0,
        scale_up_threshold: float = 0.8,
        scale_down_threshold: float = 0.3,
        check_interval: float = 30.0
    ):
        """
        初始化Session池
        
        Args:
            model_path: 模型路径
            providers: ONNX Runtime提供者列表
            min_sessions: 最小session数量
            max_sessions: 最大session数量
            session_timeout: session超时时间（秒）
            scale_up_threshold: 扩容阈值（使用率）
            scale_down_threshold: 缩容阈值（使用率）
            check_interval: 检查间隔（秒）
        """
        self.model_path = model_path
        self.providers = providers
        self.min_sessions = min_sessions
        self.max_sessions = max_sessions
        self.session_timeout = session_timeout
        self.scale_up_threshold = scale_up_threshold
        self.scale_down_threshold = scale_down_threshold
        self.check_interval = check_interval
        
        # Session管理
        self.sessions: Dict[str, SessionInfo] = {}
        self.idle_sessions: queue.Queue = queue.Queue()
        self.session_lock = threading.RLock()
        self.session_counter = 0
        
        # 统计信息
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.queue_wait_times: List[float] = []
        
        # 控制标志
        self.is_running = False
        self.monitor_task = None
        self.executor = ThreadPoolExecutor(max_workers=max_sessions, thread_name_prefix="onnx_pool")
        
    async def start(self):
        """启动Session池"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # 创建初始sessions
        await self._create_initial_sessions()
        
        # 启动监控任务
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        
        logger.info(f"ONNX Session池启动: {self.model_path} (min={self.min_sessions}, max={self.max_sessions})")

    async def warmup(self):
        """预热会话池，创建最小数量的会话"""
        logger.info(f"Warming up session pool: {self.model_path}")
        for i in range(self.min_sessions):
            success = await self._try_create_session()
            if not success:
                logger.warning(f"Failed to create session {i+1}/{self.min_sessions} during warmup")
        logger.info(f"Session pool warmup completed: {len(self.sessions)} sessions created")
        
    async def stop(self):
        """停止Session池"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 停止监控任务
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
                
        # 清理所有sessions
        await self._cleanup_all_sessions()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        logger.info(f"ONNX Session池已停止: {self.model_path}")
        
    async def get_session(self, timeout: float = 30.0) -> Optional[Tuple[str, ort.InferenceSession]]:
        """
        获取可用的session
        
        Args:
            timeout: 超时时间
            
        Returns:
            (session_id, session) 或 None
        """
        start_time = time.time()
        
        try:
            # 尝试从空闲队列获取
            session_info = await self._get_idle_session(timeout)
            
            if session_info:
                # 更新使用信息
                session_info.state = SessionState.BUSY
                session_info.update_usage()
                
                # 记录等待时间
                wait_time = time.time() - start_time
                self.queue_wait_times.append(wait_time)
                if len(self.queue_wait_times) > 1000:
                    self.queue_wait_times = self.queue_wait_times[-500:]
                    
                self.total_requests += 1
                
                return session_info.session_id, session_info.session
                
            return None

        except Exception as e:
            logger.error(f"获取session失败: {e}")
            self.failed_requests += 1
            return None

    async def get_session_with_retry(self, timeout: float = 30.0, max_retries: int = 3) -> Optional[Tuple[str, ort.InferenceSession]]:
        """
        带重试的获取session方法

        Args:
            timeout: 超时时间
            max_retries: 最大重试次数

        Returns:
            (session_id, session) 或 None
        """
        for attempt in range(max_retries):
            try:
                result = await self.get_session(timeout)
                if result:
                    return result

                # 如果没有获取到session，等待一段时间后重试
                if attempt < max_retries - 1:
                    wait_time = 0.1 * (2 ** attempt)  # 指数退避
                    logger.debug(f"Session获取失败，{wait_time:.1f}秒后重试 (attempt {attempt + 1}/{max_retries})")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                logger.error(f"Session获取异常 (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(0.1 * (2 ** attempt))

        return None
            
    async def return_session(self, session_id: str, success: bool = True):
        """
        归还session

        Args:
            session_id: session ID
            success: 是否成功使用
        """
        session_to_remove = None

        with self.session_lock:
            session_info = self.sessions.get(session_id)
            if not session_info:
                return

            if success:
                session_info.state = SessionState.IDLE
                self.idle_sessions.put(session_info)
                self.successful_requests += 1
            else:
                session_info.mark_error()
                # 错误次数过多则标记删除
                if session_info.error_count > 3:
                    session_to_remove = session_id
                    session_info.state = SessionState.ERROR
                else:
                    session_info.state = SessionState.IDLE
                    self.idle_sessions.put(session_info)

                self.failed_requests += 1

        # 在锁外执行异步删除操作
        if session_to_remove:
            await self._remove_session(session_to_remove)
                
    async def _get_idle_session(self, timeout: float) -> Optional[SessionInfo]:
        """获取空闲session"""
        end_time = time.time() + timeout
        
        while time.time() < end_time:
            try:
                # 尝试从队列获取
                session_info = self.idle_sessions.get_nowait()
                
                # 检查session是否有效
                if self._is_session_valid(session_info):
                    return session_info
                else:
                    # 无效session，移除并继续
                    await self._remove_session(session_info.session_id)
                    
            except queue.Empty:
                # 队列为空，检查是否可以创建新session
                if await self._try_create_session():
                    continue
                else:
                    # 等待一小段时间后重试
                    await asyncio.sleep(0.1)
                    
        return None
        
    def _is_session_valid(self, session_info: SessionInfo) -> bool:
        """检查session是否有效"""
        if session_info.state == SessionState.ERROR:
            return False
            
        # 检查是否超时
        if time.time() - session_info.last_used_time > self.session_timeout:
            return False
            
        return True
        
    async def _try_create_session(self) -> bool:
        """尝试创建新session"""
        with self.session_lock:
            if len(self.sessions) >= self.max_sessions:
                return False
                
            # 创建新session
            session_id = f"session_{self.session_counter}"
            self.session_counter += 1
            
            try:
                # 在线程池中创建session
                loop = asyncio.get_event_loop()
                session = await loop.run_in_executor(
                    self.executor,
                    self._create_onnx_session
                )
                
                session_info = SessionInfo(
                    session_id=session_id,
                    session=session,
                    state=SessionState.IDLE,
                    created_time=time.time(),
                    last_used_time=time.time()
                )
                
                self.sessions[session_id] = session_info
                self.idle_sessions.put(session_info)
                
                logger.debug(f"创建新session: {session_id}")
                return True
                
            except Exception as e:
                logger.error(f"创建session失败: {e}")
                return False
                
    def _create_onnx_session(self) -> ort.InferenceSession:
        """创建ONNX session（在线程池中执行）"""
        return ort.InferenceSession(self.model_path, providers=self.providers)
        
    async def _create_initial_sessions(self):
        """创建初始sessions"""
        for _ in range(self.min_sessions):
            await self._try_create_session()
            
    async def _remove_session(self, session_id: str):
        """移除session"""
        with self.session_lock:
            session_info = self.sessions.pop(session_id, None)
            if session_info:
                # 清理session资源
                try:
                    del session_info.session
                except:
                    pass
                logger.debug(f"移除session: {session_id}")
                
    async def _cleanup_all_sessions(self):
        """清理所有sessions"""
        with self.session_lock:
            session_ids = list(self.sessions.keys())
            
        for session_id in session_ids:
            await self._remove_session(session_id)
            
        # 清空队列
        while not self.idle_sessions.empty():
            try:
                self.idle_sessions.get_nowait()
            except queue.Empty:
                break
                
    async def _monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                await asyncio.sleep(self.check_interval)
                await self._check_and_scale()
                await self._cleanup_expired_sessions()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                
    async def _check_and_scale(self):
        """检查并调整session数量"""
        scale_action = None

        with self.session_lock:
            total_sessions = len(self.sessions)
            busy_sessions = sum(1 for s in self.sessions.values()
                               if s.state == SessionState.BUSY)
            idle_sessions = sum(1 for s in self.sessions.values()
                               if s.state == SessionState.IDLE)

            if total_sessions == 0:
                return

            usage_rate = busy_sessions / total_sessions

            # 扩容检查 - 只有在没有空闲会话且使用率高时才扩容
            if (usage_rate > self.scale_up_threshold and
                total_sessions < self.max_sessions and
                idle_sessions == 0):
                scale_action = "up"

            # 缩容检查 - 确保有足够的空闲会话且总数大于最小值
            elif (usage_rate < self.scale_down_threshold and
                  total_sessions > self.min_sessions and
                  idle_sessions > 1):
                scale_action = "down"

        # 在锁外执行扩缩容操作
        if scale_action == "up":
            await self._scale_up()
        elif scale_action == "down":
            await self._scale_down()
                
    async def _scale_up(self):
        """扩容"""
        # 创建新session
        if await self._try_create_session():
            logger.info(f"Session池扩容: {len(self.sessions)} sessions")
            
    async def _scale_down(self):
        """缩容"""
        # 移除一个空闲session
        try:
            session_info = self.idle_sessions.get_nowait()
            await self._remove_session(session_info.session_id)
            logger.info(f"Session池缩容: {len(self.sessions)} sessions")
        except queue.Empty:
            pass
            
    async def _cleanup_expired_sessions(self):
        """清理过期sessions"""
        current_time = time.time()
        expired_sessions = []

        with self.session_lock:
            for session_id, session_info in self.sessions.items():
                # 只清理空闲状态的过期会话，避免清理正在使用的会话
                if (session_info.state == SessionState.IDLE and
                    current_time - session_info.last_used_time > self.session_timeout):
                    expired_sessions.append(session_id)

        # 在锁外执行删除操作
        for session_id in expired_sessions:
            await self._remove_session(session_id)

        if expired_sessions:
            logger.debug(f"清理过期sessions: {len(expired_sessions)}")
                
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.session_lock:
            total_sessions = len(self.sessions)
            idle_sessions = self.idle_sessions.qsize()
            busy_sessions = total_sessions - idle_sessions
            
            avg_wait_time = (
                sum(self.queue_wait_times) / len(self.queue_wait_times)
                if self.queue_wait_times else 0.0
            )
            
            return {
                "model_path": self.model_path,
                "total_sessions": total_sessions,
                "idle_sessions": idle_sessions,
                "busy_sessions": busy_sessions,
                "min_sessions": self.min_sessions,
                "max_sessions": self.max_sessions,
                "total_requests": self.total_requests,
                "successful_requests": self.successful_requests,
                "failed_requests": self.failed_requests,
                "success_rate": (
                    self.successful_requests / max(self.total_requests, 1) * 100
                ),
                "avg_wait_time_ms": avg_wait_time * 1000,
                "usage_rate": busy_sessions / max(total_sessions, 1) * 100
            }
