#!/usr/bin/env python3
"""
FastAPI WebSocket流式服务接口 - ASR实时识别服务
基于竞品功能实现的语音识别WebSocket API
"""

import os
import sys
import json
import time
import uuid
import asyncio
import logging
import tempfile
from typing import Optional, Dict, Any, List
from datetime import datetime
from collections import deque

import numpy as np
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Triton client
try:
    import tritonclient.http as httpclient
    TRITON_AVAILABLE = True
except ImportError:
    print("Warning: tritonclient not available")
    TRITON_AVAILABLE = False

# Add tools to path
sys.path.append('tools')

# Load configuration
from config_loader import (
    triton_config, stream_config, asr_config,
    setup_logging, get_config_summary
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="ASR实时识别服务",
    description="基于Triton的语音识别WebSocket API服务",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration loaded from config_loader

class StreamSession:
    """流式识别会话"""
    
    def __init__(self, session_id: str, websocket: WebSocket, language: str = "zh"):
        self.session_id = session_id
        self.websocket = websocket
        self.language = language
        self.audio_buffer = deque(maxlen=stream_config.MAX_AUDIO_BUFFER)
        self.is_active = True
        self.last_activity = time.time()
        self.sentence_buffer = []
        self.current_sentence = ""
        self.sentence_start_time = 0
        self.total_audio_time = 0
        
        # Recognition parameters
        self.enable_intermediate_result = True
        self.enable_punctuation_prediction = True
        self.enable_inverse_text_normalization = True
        self.enable_voice_detection = True
        self.max_sentence_silence = stream_config.MAX_SENTENCE_SILENCE
        
        # Performance tracking
        self.recognition_count = 0
        self.total_recognition_time = 0
        
    def add_audio_chunk(self, audio_data: bytes):
        """添加音频数据块"""
        # Convert bytes to float32 array
        audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
        self.audio_buffer.extend(audio_array)
        self.last_activity = time.time()
        self.total_audio_time += len(audio_array) / stream_config.SAMPLE_RATE
        
    def get_audio_for_recognition(self) -> Optional[np.ndarray]:
        """获取用于识别的音频数据"""
        if len(self.audio_buffer) < stream_config.CHUNK_SIZE:
            return None
        
        # Get audio chunk
        audio_chunk = np.array(list(self.audio_buffer))
        return audio_chunk
        
    def clear_audio_buffer(self):
        """清空音频缓冲区"""
        self.audio_buffer.clear()
        
    def is_timeout(self) -> bool:
        """检查是否超时"""
        return time.time() - self.last_activity > stream_config.SILENCE_TIMEOUT
        
    def get_stats(self) -> Dict:
        """获取会话统计信息"""
        avg_recognition_time = (
            self.total_recognition_time / max(self.recognition_count, 1)
        )
        
        return {
            "session_id": self.session_id,
            "language": self.language,
            "total_audio_time": self.total_audio_time,
            "recognition_count": self.recognition_count,
            "avg_recognition_time": avg_recognition_time,
            "buffer_size": len(self.audio_buffer),
            "is_active": self.is_active
        }

class StreamTritonClient:
    """流式Triton客户端"""
    
    def __init__(self, server_url: str, model_name: str):
        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        
    def connect(self):
        """连接到Triton服务器"""
        if not TRITON_AVAILABLE:
            raise RuntimeError("Triton client not available")
            
        try:
            self.client = httpclient.InferenceServerClient(url=self.server_url)
            if not self.client.is_model_ready(self.model_name):
                raise RuntimeError(f"Model {self.model_name} is not ready")
            logger.info(f"Stream client connected to Triton server at {self.server_url}")
        except Exception as e:
            logger.error(f"Failed to connect to Triton server: {e}")
            raise
    
    async def recognize_stream(self, audio_data: np.ndarray, language: str = "zh") -> Dict:
        """执行流式语音识别"""
        if self.client is None:
            self.connect()
        
        try:
            # Prepare inputs
            lang_array = np.array([language], dtype=object)
            
            inputs = [
                httpclient.InferInput("WAV", audio_data.shape, "FP32"),
                httpclient.InferInput("LANG", lang_array.shape, "BYTES")
            ]
            
            inputs[0].set_data_from_numpy(audio_data)
            inputs[1].set_data_from_numpy(lang_array)
            
            outputs = [httpclient.InferRequestedOutput("TRANSCRIPTS")]
            
            # Send request
            response = self.client.infer(self.model_name, inputs, outputs=outputs)
            
            # Parse result
            result_array = response.as_numpy("TRANSCRIPTS")
            if len(result_array) > 0:
                result_json = result_array[0].decode('utf-8')
                return json.loads(result_json)
            else:
                return {"text": "", "segments": [], "duration": 0}
                
        except Exception as e:
            logger.error(f"Stream recognition failed: {e}")
            return {"text": "", "segments": [], "duration": 0, "error": str(e)}

# Global clients and sessions
stream_client = StreamTritonClient(triton_config.SERVER_URL, triton_config.MODEL_NAME)
active_sessions: Dict[str, StreamSession] = {}

async def cleanup_inactive_sessions():
    """清理非活跃会话"""
    while True:
        try:
            current_time = time.time()
            inactive_sessions = []
            
            for session_id, session in active_sessions.items():
                if not session.is_active or session.is_timeout():
                    inactive_sessions.append(session_id)
            
            for session_id in inactive_sessions:
                if session_id in active_sessions:
                    logger.info(f"Cleaning up inactive session: {session_id}")
                    del active_sessions[session_id]
            
            await asyncio.sleep(30)  # Check every 30 seconds
            
        except Exception as e:
            logger.error(f"Error in cleanup task: {e}")
            await asyncio.sleep(30)

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("Starting ASR Stream Service...")
    try:
        stream_client.connect()
        # Start cleanup task
        asyncio.create_task(cleanup_inactive_sessions())
        logger.info("ASR Stream Service started successfully")
    except Exception as e:
        logger.error(f"Failed to start stream service: {e}")

@app.get("/")
async def root():
    """根路径"""
    return {"message": "ASR实时识别服务", "version": "1.0.0", "status": "running"}

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        if stream_client.client and stream_client.client.is_model_ready(triton_config.MODEL_NAME):
            return {
                "status": "healthy", 
                "triton": "connected", 
                "model": "ready",
                "active_sessions": len(active_sessions)
            }
        else:
            return {"status": "unhealthy", "triton": "disconnected", "model": "not_ready"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}

@app.get("/sessions")
async def list_sessions():
    """列出活跃会话"""
    sessions = []
    for session_id, session in active_sessions.items():
        sessions.append(session.get_stats())
    
    return {"sessions": sessions, "total": len(sessions)}

@app.get("/demo")
async def demo_page():
    """演示页面"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>ASR实时识别演示</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .controls { margin: 20px 0; }
            button { padding: 10px 20px; margin: 5px; font-size: 16px; }
            .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
            .connected { background-color: #d4edda; color: #155724; }
            .disconnected { background-color: #f8d7da; color: #721c24; }
            .results { border: 1px solid #ddd; padding: 20px; margin: 20px 0; min-height: 200px; }
            .intermediate { color: #666; font-style: italic; }
            .final { color: #000; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>ASR实时识别演示</h1>
            
            <div class="controls">
                <button id="startBtn">开始录音</button>
                <button id="stopBtn" disabled>停止录音</button>
                <button id="clearBtn">清空结果</button>
            </div>
            
            <div id="status" class="status disconnected">未连接</div>
            
            <div class="results">
                <h3>识别结果：</h3>
                <div id="results"></div>
            </div>
            
            <div>
                <h3>会话信息：</h3>
                <div id="sessionInfo"></div>
            </div>
        </div>

        <script>
            let websocket = null;
            let mediaRecorder = null;
            let audioStream = null;
            let sessionId = null;
            
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const clearBtn = document.getElementById('clearBtn');
            const status = document.getElementById('status');
            const results = document.getElementById('results');
            const sessionInfo = document.getElementById('sessionInfo');
            
            function updateStatus(message, isConnected) {
                status.textContent = message;
                status.className = 'status ' + (isConnected ? 'connected' : 'disconnected');
            }
            
            function addResult(text, isFinal) {
                const div = document.createElement('div');
                div.className = isFinal ? 'final' : 'intermediate';
                div.textContent = `[${new Date().toLocaleTimeString()}] ${text}`;
                results.appendChild(div);
                results.scrollTop = results.scrollHeight;
            }
            
            function updateSessionInfo(info) {
                sessionInfo.innerHTML = `
                    <p>会话ID: ${info.session_id || 'N/A'}</p>
                    <p>语言: ${info.language || 'N/A'}</p>
                    <p>音频时长: ${(info.total_audio_time || 0).toFixed(2)}秒</p>
                    <p>识别次数: ${info.recognition_count || 0}</p>
                    <p>平均识别时间: ${(info.avg_recognition_time || 0).toFixed(3)}秒</p>
                `;
            }
            
            async function startRecording() {
                try {
                    // Connect WebSocket
                    const wsUrl = `ws://localhost:8080/ws/stream?language=zh`;
                    websocket = new WebSocket(wsUrl);
                    
                    websocket.onopen = function() {
                        updateStatus('WebSocket已连接', true);
                    };
                    
                    websocket.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        
                        if (data.type === 'session_info') {
                            sessionId = data.session_id;
                            updateSessionInfo(data);
                        } else if (data.type === 'recognition_result') {
                            if (data.text) {
                                addResult(data.text, data.is_final);
                            }
                            if (data.session_info) {
                                updateSessionInfo(data.session_info);
                            }
                        } else if (data.type === 'error') {
                            addResult(`错误: ${data.message}`, true);
                        }
                    };
                    
                    websocket.onclose = function() {
                        updateStatus('WebSocket已断开', false);
                    };
                    
                    websocket.onerror = function(error) {
                        updateStatus('WebSocket错误', false);
                        console.error('WebSocket error:', error);
                    };
                    
                    // Start audio recording
                    audioStream = await navigator.mediaDevices.getUserMedia({ 
                        audio: { 
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true
                        } 
                    });
                    
                    mediaRecorder = new MediaRecorder(audioStream, {
                        mimeType: 'audio/webm;codecs=opus'
                    });
                    
                    mediaRecorder.ondataavailable = function(event) {
                        if (event.data.size > 0 && websocket && websocket.readyState === WebSocket.OPEN) {
                            websocket.send(event.data);
                        }
                    };
                    
                    mediaRecorder.start(100); // Send data every 100ms
                    
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                    updateStatus('录音中...', true);
                    
                } catch (error) {
                    updateStatus('启动录音失败: ' + error.message, false);
                    console.error('Error starting recording:', error);
                }
            }
            
            function stopRecording() {
                if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                    mediaRecorder.stop();
                }
                
                if (audioStream) {
                    audioStream.getTracks().forEach(track => track.stop());
                    audioStream = null;
                }
                
                if (websocket) {
                    websocket.close();
                    websocket = null;
                }
                
                startBtn.disabled = false;
                stopBtn.disabled = true;
                updateStatus('录音已停止', false);
            }
            
            function clearResults() {
                results.innerHTML = '';
                sessionInfo.innerHTML = '';
            }
            
            startBtn.addEventListener('click', startRecording);
            stopBtn.addEventListener('click', stopRecording);
            clearBtn.addEventListener('click', clearResults);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.websocket("/ws/stream")
async def websocket_stream_endpoint(
    websocket: WebSocket,
    language: str = "zh",
    enable_intermediate_result: bool = True,
    enable_punctuation_prediction: bool = True,
    enable_inverse_text_normalization: bool = True,
    max_sentence_silence: int = 450
):
    """WebSocket流式识别端点"""

    # Accept connection
    await websocket.accept()

    # Create session
    session_id = str(uuid.uuid4())
    session = StreamSession(session_id, websocket, language)
    session.enable_intermediate_result = enable_intermediate_result
    session.enable_punctuation_prediction = enable_punctuation_prediction
    session.enable_inverse_text_normalization = enable_inverse_text_normalization
    session.max_sentence_silence = max_sentence_silence

    active_sessions[session_id] = session

    logger.info(f"New stream session started: {session_id}")

    # Send session info
    await websocket.send_text(json.dumps({
        "type": "session_info",
        "session_id": session_id,
        "language": language,
        "message": "Session started successfully"
    }))

    try:
        while session.is_active:
            try:
                # Receive audio data
                data = await websocket.receive_bytes()

                # Add to audio buffer
                session.add_audio_chunk(data)

                # Check if we have enough audio for recognition
                audio_data = session.get_audio_for_recognition()
                if audio_data is not None and len(audio_data) > 0:

                    # Perform recognition
                    start_time = time.time()
                    result = await stream_client.recognize_stream(audio_data, language)
                    recognition_time = time.time() - start_time

                    # Update session stats
                    session.recognition_count += 1
                    session.total_recognition_time += recognition_time

                    # Process result
                    text = result.get("text", "").strip()

                    if text:
                        # Determine if this is a final result
                        is_final = _is_final_result(text, session)

                        # Send result
                        response = {
                            "type": "recognition_result",
                            "text": text,
                            "is_final": is_final,
                            "confidence": result.get("confidence", 0.0),
                            "volume": result.get("volume", 0),
                            "recognition_time": recognition_time,
                            "session_info": session.get_stats()
                        }

                        # Add segments if available
                        if "segments" in result and result["segments"]:
                            response["segments"] = result["segments"]

                        await websocket.send_text(json.dumps(response, ensure_ascii=False))

                        # Clear buffer if final result
                        if is_final:
                            session.clear_audio_buffer()

                    # Send periodic session updates
                    if session.recognition_count % 10 == 0:
                        await websocket.send_text(json.dumps({
                            "type": "session_update",
                            "session_info": session.get_stats()
                        }))

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for session: {session_id}")
                break

            except Exception as e:
                logger.error(f"Error in stream processing for session {session_id}: {e}")

                # Send error message
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e),
                    "session_id": session_id
                }))

                # Continue processing
                continue

    except Exception as e:
        logger.error(f"Fatal error in stream session {session_id}: {e}")

    finally:
        # Cleanup session
        session.is_active = False
        if session_id in active_sessions:
            del active_sessions[session_id]

        logger.info(f"Stream session ended: {session_id}")

        # Send final session stats
        try:
            await websocket.send_text(json.dumps({
                "type": "session_ended",
                "session_id": session_id,
                "final_stats": session.get_stats()
            }))
        except:
            pass

def _is_final_result(text: str, session: StreamSession) -> bool:
    """判断是否为最终结果"""

    # Simple heuristic: if text ends with punctuation, consider it final
    final_punctuation = ['。', '！', '？', '.', '!', '?']

    if any(text.endswith(p) for p in final_punctuation):
        return True

    # If silence detected (no new audio for a while), consider it final
    if time.time() - session.last_activity > 1.0:  # 1 second silence
        return True

    return False

@app.delete("/sessions/{session_id}")
async def terminate_session(session_id: str):
    """终止指定会话"""

    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    session = active_sessions[session_id]
    session.is_active = False

    try:
        await session.websocket.close()
    except:
        pass

    del active_sessions[session_id]

    return {"message": f"Session {session_id} terminated successfully"}

@app.post("/sessions/cleanup")
async def cleanup_sessions():
    """清理所有非活跃会话"""

    inactive_count = 0
    session_ids_to_remove = []

    for session_id, session in active_sessions.items():
        if not session.is_active or session.is_timeout():
            session_ids_to_remove.append(session_id)
            inactive_count += 1

    for session_id in session_ids_to_remove:
        if session_id in active_sessions:
            try:
                await active_sessions[session_id].websocket.close()
            except:
                pass
            del active_sessions[session_id]

    return {
        "message": f"Cleaned up {inactive_count} inactive sessions",
        "remaining_sessions": len(active_sessions)
    }

if __name__ == "__main__":
    uvicorn.run(
        "app_stream:app",
        host=stream_config.HOST,
        port=stream_config.PORT,
        reload=True,
        log_level="info"
    )
