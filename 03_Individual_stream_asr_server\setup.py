from setuptools import setup, find_packages
from Cython.Build import cythonize
import os
import sys
sys.path = ['/ws/online_asr_server/modules', '/ws/online_asr_server/utils', '/ws/online_asr_server', '/ws/online_asr_server', '/ws/online_asr_server/server', '/ws/online_asr_server/server/utils', '/opt/conda/lib/python38.zip', '/opt/conda/lib/python3.8', '/opt/conda/lib/python3.8/lib-dynload', '/opt/conda/lib/python3.8/site-packages']
def find_py_files(path):
    py_files = []
    for root, dirs, files in os.walk(path):
        for file in files:
            if file.endswith('.py') and file != '__init__.py' and file != 'app.py' and file != 'setup.py':
                py_files.append(os.path.join(root, file))
    return py_files

py_files = find_py_files('.')

setup(
    name="online_asr_server",
    version='0.1',
    packages=find_packages(),
    ext_modules=cythonize(py_files)
)
