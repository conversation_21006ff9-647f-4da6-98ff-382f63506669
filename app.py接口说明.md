# ASR文件转写服务 API 接口说明

## 概述

本服务提供基于Triton的语音文件识别HTTP API，支持多种音频格式的语音转文字功能。

## 接口列表

| 接口名                                           | 协议     | URL                                                  | 方法   | 说明                     |
| ------------------------------------------------ | -------- | ---------------------------------------------------- | ------ | ------------------------ |
| 服务状态检查                                     | HTTP/1.1 | http:{ip_address}:{port}/                            | GET    | 获取服务基本信息         |
| 健康检查                                         | HTTP/1.1 | http:{ip_address}:{port}/health                      | GET    | 检查服务健康状态         |
| 请求文件立即转写（从本地文件上传，立即返回结果） | HTTP/1.1 | http:{ip_address}:{port}/transcribe/sync             | POST   | 同步转写，适合小文件     |
| 请求文件后台转写（从本地文件上传）               | HTTP/1.1 | http:{ip_address}:{port}/transcribe/file             | POST   | 异步转写，适合大文件     |
| 请求文件后台转写（从url上传）                    | HTTP/1.1 | http:{ip_address}:{port}/transcribe/url              | POST   | 从URL异步转写           |
| 查询后台任务                                     | HTTP/1.1 | http:{ip_address}:{port}/transcribe/tasks            | GET    | 列出所有转写任务         |
| 获取转写结果                                     | HTTP/1.1 | http:{ip_address}:{port}/transcribe/result/{task_id} | GET    | 获取指定任务的转写结果   |
| 删除后台任务                                     | HTTP/1.1 | http:{ip_address}:{port}/transcribe/delete/{task_id} | DELETE | 删除指定的转写任务       |

## 数据模型

### TranscriptionRequest（转写请求参数）

| 参数名           | 类型                | 必填 | 默认值 | 说明                                           |
| ---------------- | ------------------- | ---- | ------ | ---------------------------------------------- |
| audio_url        | string              | 否   | null   | 音频文件URL（仅用于/transcribe/url接口）       |
| language         | string              | 否   | "zh"   | 语言代码（zh=中文, en=英文）                   |
| enable_words     | boolean             | 否   | false  | 是否返回词级别时间戳                           |
| enable_confidence| boolean             | 否   | true   | 是否返回置信度信息                             |
| enable_volume    | boolean             | 否   | true   | 是否返回音量信息                               |
| hotwords         | array[string]       | 否   | null   | 热词列表，提高特定词汇识别准确率               |
| forbidden_words  | array[string]       | 否   | null   | 敏感词列表，识别结果中会过滤这些词             |
| correction_words | object[string,string]| 否   | null   | 强制替换词典，key为原词，value为替换词         |

### TranscriptionResponse（转写响应结果）

| 参数名        | 类型          | 说明                                                   |
| ------------- | ------------- | ------------------------------------------------------ |
| task_id       | string        | 任务唯一标识符                                         |
| status        | string        | 任务状态：created/processing/completed/failed         |
| text          | string        | 完整的识别结果文本                                     |
| segments      | array[object] | 分段结果，包含每段的文本、开始时间、结束时间           |
| duration      | float         | 音频总时长（秒）                                       |
| confidence    | float         | 整体置信度（0.0-1.0）                                  |
| volume        | integer       | 音量级别（0-100）                                      |
| language      | string        | 检测到的语言代码                                       |
| cost_time     | string        | 处理耗时                                               |
| error_message | string        | 错误信息（仅在失败时返回）                             |

#### segments 字段详细说明

每个segment对象包含：
- `text` (string): 该段的识别文本
- `start` (float): 开始时间（秒）
- `end` (float): 结束时间（秒）

### TaskStatus（任务状态）

| 参数名     | 类型     | 说明                                                    |
| ---------- | -------- | ------------------------------------------------------- |
| task_id    | string   | 任务ID                                                  |
| status     | string   | 任务状态                                                |
| progress   | integer  | 进度百分比（0-100）                                     |
| result     | object   | 转写结果（仅在completed状态时有值）                     |
| error      | string   | 错误信息（仅在failed状态时有值）                        |
| created_at | datetime | 任务创建时间                                            |
| updated_at | datetime | 任务最后更新时间                                        |

#### status 状态说明

| 状态值     | 进度值 | 说明                           |
| ---------- | ------ | ------------------------------ |
| created    | 0      | 任务已创建，等待处理           |
| processing | 10-99  | 任务处理中                     |
| completed  | 100    | 任务完成，可获取结果           |
| failed     | -      | 任务失败，查看error_message    |

## 支持的音频格式

- WAV (.wav)
- MP3 (.mp3) 
- FLAC (.flac)
- M4A (.m4a)
- AAC (.aac)

## 文件大小限制

- 同步接口(/transcribe/sync): 最大10MB
- 异步接口(/transcribe/file, /transcribe/url): 最大100MB（可配置）

## 接口详细说明

### 1. 服务状态检查

**请求示例：**
```bash
curl -X GET "http://0.0.0.0:10093/"
```

**响应示例：**
```json
{
  "message": "ASR文件转写服务",
  "version": "1.1.25.06",
  "status": "running"
}
```

### 2. 健康检查

**请求示例：**
```bash
curl -X GET "http://0.0.0.0:10093/health"
```

**响应示例：**
```json
{
  "status": "healthy",
  "triton": "connected", 
  "model": "ready"
}
```

### 3. 同步转写接口

**请求示例：**
```bash
curl -X POST "http://0.0.0.0:10093/transcribe/sync" \
  -F "file=@wavs/zh/zh_4s_16khz.wav" \
  -F "language=zh" \
  -F "enable_confidence=true" \
  -F "enable_volume=true"
```

**响应示例：**
```json
{
  "task_id": "8b0084db-ee73-4481-8389-e921fbb19098",
  "status": "completed",
  "text": "我们应该抱着更加长远和开阔的视角来看待",
  "segments": [
    {
      "text": "我们应该抱着更加长远和开阔的视角来看待",
      "start": 0.0,
      "end": 4.823
    }
  ],
  "duration": 4.823,
  "confidence": 0.95,
  "volume": 75,
  "language": "zh",
  "cost_time": "0.1224 s",
  "error_message": null
}
```

### 4. 异步文件转写接口

**基本请求示例：**
```bash
curl -X POST "http://0.0.0.0:10093/transcribe/file" \
  -F "file=@wavs/zh/zh_4s_16khz.wav" \
  -F "language=zh"
```

**高级参数请求示例：**
```bash
curl -X POST "http://0.0.0.0:10093/transcribe/file" \
  -F "file=@audio.wav" \
  -F "language=zh" \
  -F "enable_words=true" \
  -F "enable_confidence=true" \
  -F "enable_volume=true" \
  -F 'hotwords=["科技","人工智能","机器学习"]' \
  -F 'forbidden_words=["敏感词1","敏感词2"]' \
  -F 'correction_words={"错误词":"正确词","旧词":"新词"}'
```

**响应示例：**
```json
{
  "task_id": "dbe7b0cc-ffb4-4d70-bd2e-8e1409a0a5fa",
  "status": "processing",
  "text": null,
  "segments": null,
  "duration": null,
  "confidence": null,
  "volume": null,
  "language": null,
  "cost_time": null,
  "error_message": null
}
```

### 5. URL转写接口

**请求示例：**
```bash
curl -X POST "http://0.0.0.0:10093/transcribe/url" \
  -H "Content-Type: application/json" \
  -d '{
    "audio_url": "http://example.com/audio.wav",
    "language": "zh",
    "enable_confidence": true,
    "enable_volume": true,
    "hotwords": ["科技", "人工智能"],
    "forbidden_words": ["敏感词"],
    "correction_words": {"错误词": "正确词"}
  }'
```

**响应示例：**
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "processing",
  "text": null,
  "segments": null,
  "duration": null,
  "confidence": null,
  "volume": null,
  "language": null,
  "cost_time": null,
  "error_message": null
}
```

### 6. 获取转写结果

**请求示例：**
```bash
curl -X GET "http://0.0.0.0:10093/transcribe/result/dbe7b0cc-ffb4-4d70-bd2e-8e1409a0a5fa"
```

**响应示例（处理中）：**
```json
{
  "task_id": "dbe7b0cc-ffb4-4d70-bd2e-8e1409a0a5fa",
  "status": "processing",
  "text": null,
  "segments": null,
  "duration": null,
  "confidence": null,
  "volume": null,
  "language": null,
  "cost_time": null,
  "error_message": null
}
```

**响应示例（已完成）：**
```json
{
  "task_id": "dbe7b0cc-ffb4-4d70-bd2e-8e1409a0a5fa",
  "status": "completed",
  "text": "我们应该抱着更加长远和开阔的视角来看待",
  "segments": [
    {
      "text": "我们应该抱着更加长远和开阔的视角来看待",
      "start": 0.0,
      "end": 4.823
    }
  ],
  "duration": 4.823,
  "confidence": 0.95,
  "volume": 75,
  "language": "zh",
  "cost_time": "0.3264 s",
  "error_message": null
}
```

### 7. 查询所有任务

**请求示例：**
```bash
curl -X GET "http://0.0.0.0:10093/transcribe/tasks"
```

**响应示例：**
```json
{
  "tasks": [
    {
      "task_id": "dbe7b0cc-ffb4-4d70-bd2e-8e1409a0a5fa",
      "status": "completed",
      "progress": 100,
      "created_at": "2025-06-18T02:00:53.609278",
      "updated_at": "2025-06-18T02:01:00.301368"
    },
    {
      "task_id": "db908099-4e4a-467c-9402-376ad1bcfe6b",
      "status": "processing",
      "progress": 50,
      "created_at": "2025-06-18T02:05:39.901555",
      "updated_at": "2025-06-18T02:05:40.042746"
    }
  ],
  "total": 2
}
```

### 8. 删除任务

**请求示例：**
```bash
curl -X DELETE "http://0.0.0.0:10093/transcribe/delete/dbe7b0cc-ffb4-4d70-bd2e-8e1409a0a5fa"
```

**响应示例（成功）：**
```json
{
  "message": "Task deleted successfully"
}
```

**响应示例（任务不存在）：**
```json
{
  "detail": "Task not found"
}
```

## 错误处理

### 常见错误码

| HTTP状态码 | 错误类型           | 说明                           |
| ---------- | ------------------ | ------------------------------ |
| 400        | Bad Request        | 请求参数错误或文件格式不支持   |
| 404        | Not Found          | 任务不存在                     |
| 413        | Payload Too Large  | 文件过大                       |
| 500        | Internal Error     | 服务器内部错误                 |

### 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

## 使用建议

1. **文件大小选择**：
   - 小文件（<10MB）：推荐使用同步接口 `/transcribe/sync`
   - 大文件（>10MB）：使用异步接口 `/transcribe/file` 或 `/transcribe/url`

2. **轮询建议**：
   - 异步任务建议每2-5秒轮询一次结果
   - 避免过于频繁的轮询请求

3. **参数优化**：
   - 根据实际需求开启/关闭 `enable_confidence`、`enable_volume` 等功能
   - 合理使用热词功能提高特定领域识别准确率

4. **任务管理**：
   - 及时删除不需要的任务，避免内存占用
   - 生产环境建议使用Redis或数据库存储任务状态

## 进度值与状态对应关系

根据代码分析，progress整数值与status字符串的对应关系如下：

| progress值 | status状态  | 说明                           |
| ---------- | ----------- | ------------------------------ |
| 0          | created     | 任务已创建，等待处理           |
| 10         | processing  | 任务开始处理                   |
| 100        | completed   | 任务处理完成                   |
| -          | failed      | 任务处理失败（无固定progress值）|

## 技术特性

### 性能监控
- 支持推理时间、内存使用、批次大小等性能指标监控
- 自动调整批次大小以优化性能
- 提供详细的性能统计信息

### 高级功能
- **热词支持**：提高特定词汇识别准确率
- **敏感词过滤**：自动过滤指定的敏感词汇
- **强制替换**：将识别结果中的特定词汇替换为指定内容
- **置信度计算**：提供识别结果的可信度评分
- **音量检测**：分析音频的音量级别
- **语气词过滤**：可选择过滤语气词

### 音频处理
- **VAD（语音活动检测）**：自动检测和分割语音段落
- **多格式支持**：支持WAV、MP3、FLAC、M4A、AAC等格式
- **自动重采样**：自动处理不同采样率的音频文件
