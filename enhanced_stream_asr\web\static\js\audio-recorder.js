/**
 * 音频录制器
 * 支持浏览器麦克风录音和实时音频处理
 */

class AudioRecorder {
    constructor() {
        this.mediaStream = null;
        this.mediaRecorder = null;
        this.audioContext = null;
        this.analyser = null;
        this.microphone = null;
        this.processor = null;
        this.isRecording = false;
        this.sampleRate = 16000;
        this.channels = 1;
        this.chunkDuration = 0.4; // 秒
        this.audioBuffer = [];
        this.totalAudioTime = 0;
        this.volumeMonitorInterval = null;
        
        // 事件回调
        this.onAudioData = null;
        this.onVolumeChange = null;
        this.onError = null;
        this.onRecordingStateChange = null;
    }
    
    async initialize(config = {}) {
        try {
            this.sampleRate = config.sampleRate || 16000;
            this.channels = config.channels || 1;
            this.chunkDuration = config.chunkDuration || 0.4;
            
            // 请求麦克风权限
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: this.sampleRate,
                    channelCount: this.channels,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.sampleRate
            });
            
            // 创建分析器（用于音量检测）
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 256;
            this.analyser.smoothingTimeConstant = 0.8;
            
            // 连接麦克风到分析器
            this.microphone = this.audioContext.createMediaStreamSource(this.mediaStream);
            this.microphone.connect(this.analyser);
            
            // 创建音频处理器
            await this.createAudioProcessor();
            
            console.log('Audio recorder initialized successfully');
            return true;
            
        } catch (error) {
            console.error('Audio recorder initialization failed:', error);
            if (this.onError) {
                this.onError(error);
            }
            throw error;
        }
    }
    
    async createAudioProcessor() {
        try {
            // 使用ScriptProcessorNode（兼容性更好）
            const bufferSize = 4096;
            this.processor = this.audioContext.createScriptProcessor(bufferSize, this.channels, this.channels);
            
            this.processor.onaudioprocess = (event) => {
                if (this.isRecording) {
                    const inputBuffer = event.inputBuffer;
                    const audioData = inputBuffer.getChannelData(0);
                    this.audioBuffer.push(new Float32Array(audioData));
                    
                    // 检查是否达到chunk大小
                    const totalSamples = this.audioBuffer.reduce((sum, chunk) => sum + chunk.length, 0);
                    const chunkSamples = this.sampleRate * this.chunkDuration;
                    
                    if (totalSamples >= chunkSamples) {
                        this.processAudioBuffer();
                    }
                }
            };
            
            this.microphone.connect(this.processor);
            this.processor.connect(this.audioContext.destination);
            
        } catch (error) {
            console.error('Audio processor creation failed:', error);
            throw error;
        }
    }
    
    startRecording() {
        if (this.isRecording) {
            return;
        }
        
        try {
            this.isRecording = true;
            this.audioBuffer = [];
            this.totalAudioTime = 0;
            
            // 恢复音频上下文（如果被暂停）
            if (this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
            
            // 开始音量监测
            this.startVolumeMonitoring();
            
            if (this.onRecordingStateChange) {
                this.onRecordingStateChange(true);
            }
            
            console.log('Recording started');
            
        } catch (error) {
            console.error('Failed to start recording:', error);
            if (this.onError) {
                this.onError(error);
            }
        }
    }
    
    stopRecording() {
        if (!this.isRecording) {
            return;
        }
        
        try {
            this.isRecording = false;
            
            // 处理剩余的音频数据
            if (this.audioBuffer.length > 0) {
                this.processAudioBuffer(true);
            }
            
            // 停止音量监测
            this.stopVolumeMonitoring();
            
            if (this.onRecordingStateChange) {
                this.onRecordingStateChange(false);
            }
            
            console.log('Recording stopped');
            
        } catch (error) {
            console.error('Failed to stop recording:', error);
            if (this.onError) {
                this.onError(error);
            }
        }
    }
    
    processAudioBuffer(isFinal = false) {
        if (this.audioBuffer.length === 0) {
            return;
        }
        
        // 合并音频块
        const totalSamples = this.audioBuffer.reduce((sum, chunk) => sum + chunk.length, 0);
        const mergedAudio = new Float32Array(totalSamples);
        
        let offset = 0;
        for (const chunk of this.audioBuffer) {
            mergedAudio.set(chunk, offset);
            offset += chunk.length;
        }
        
        // 处理音频数据
        this.handleAudioChunk(mergedAudio);
        
        // 清空缓冲区
        this.audioBuffer = [];
        
        if (isFinal && this.onAudioData) {
            // 发送最终标记
            this.onAudioData('', true);
        }
    }
    
    handleAudioChunk(audioData) {
        if (this.isRecording && this.onAudioData) {
            // 转换为PCM格式
            const pcmData = this.float32ToPCM16(audioData);
            
            // 转换为base64
            const base64Data = this.arrayBufferToBase64(pcmData.buffer);
            
            // 更新总音频时长
            this.totalAudioTime += audioData.length / this.sampleRate;
            
            // 发送音频数据
            this.onAudioData(base64Data, false);
        }
    }
    
    float32ToPCM16(float32Array) {
        const pcm16Array = new Int16Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            // 限制范围到 [-1, 1]
            const sample = Math.max(-1, Math.min(1, float32Array[i]));
            // 转换到 16位整数范围
            pcm16Array[i] = sample * 0x7FFF;
        }
        return pcm16Array;
    }
    
    arrayBufferToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }
    
    startVolumeMonitoring() {
        this.stopVolumeMonitoring();
        
        this.volumeMonitorInterval = setInterval(() => {
            if (this.analyser && this.onVolumeChange) {
                const dataArray = new Uint8Array(this.analyser.frequencyBinCount);
                this.analyser.getByteFrequencyData(dataArray);
                
                // 计算平均音量
                let sum = 0;
                for (let i = 0; i < dataArray.length; i++) {
                    sum += dataArray[i];
                }
                const average = sum / dataArray.length;
                const volume = average / 255; // 归一化到 [0, 1]
                
                this.onVolumeChange(volume);
            }
        }, 100); // 每100ms更新一次
    }
    
    stopVolumeMonitoring() {
        if (this.volumeMonitorInterval) {
            clearInterval(this.volumeMonitorInterval);
            this.volumeMonitorInterval = null;
        }
    }
    
    destroy() {
        try {
            this.stopRecording();
            this.stopVolumeMonitoring();
            
            if (this.processor) {
                this.processor.disconnect();
                this.processor = null;
            }
            
            if (this.microphone) {
                this.microphone.disconnect();
                this.microphone = null;
            }
            
            if (this.analyser) {
                this.analyser.disconnect();
                this.analyser = null;
            }
            
            if (this.audioContext) {
                this.audioContext.close();
                this.audioContext = null;
            }
            
            if (this.mediaStream) {
                this.mediaStream.getTracks().forEach(track => track.stop());
                this.mediaStream = null;
            }
            
            console.log('Audio recorder destroyed');
            
        } catch (error) {
            console.error('Error destroying audio recorder:', error);
        }
    }
    
    getTotalAudioTime() {
        return this.totalAudioTime;
    }
    
    getIsRecording() {
        return this.isRecording;
    }
    
    getSampleRate() {
        return this.sampleRate;
    }
    
    getChannels() {
        return this.channels;
    }
}
